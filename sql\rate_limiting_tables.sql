-- Create table for storing rate limits
CREATE TABLE IF NOT EXISTS rate_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    limit_type VARCHAR(50) NOT NULL,
    identifier VARCHAR(100),
    endpoint VARCHAR(255),
    max_requests INT NOT NULL,
    time_window INT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_limit_type (limit_type),
    INDEX idx_identifier (identifier),
    INDEX idx_endpoint (endpoint),
    INDEX idx_updated_at (updated_at)
);

-- Create table for logging API requests
CREATE TABLE IF NOT EXISTS api_requests (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL,
    method VARCHAR(10) NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    base_url VARCHAR(255) NOT NULL,
    correlation_id CHAR(36) NOT NULL,
    response_code INT NOT NULL,
    response_time DECIMAL(10,3) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_identifier (identifier),
    INDEX idx_endpoint (endpoint),
    INDEX idx_timestamp (timestamp),
    INDEX idx_correlation_id (correlation_id)
);

-- Insert default rate limits
INSERT INTO rate_limits (limit_type, identifier, endpoint, max_requests, time_window) VALUES
-- Global rate limit (30 requests per minute)
('global', NULL, NULL, 30, 60),

-- Per identifier rate limits (20 requests per minute)
('per_identifier', NULL, NULL, 20, 60),

-- Endpoint specific rate limits
('per_endpoint', NULL, 'positions', 10, 60),        -- Position operations
('per_endpoint', NULL, 'positions/otc', 5, 60),     -- Trade operations
('per_endpoint', NULL, 'markets', 15, 60),          -- Market data
('per_endpoint', NULL, 'prices', 20, 60),           -- Price data
('per_endpoint', NULL, 'workingorders', 10, 60),    -- Working orders
('per_endpoint', NULL, 'confirms', 15, 60),         -- Trade confirmations
('per_endpoint', NULL, 'history', 10, 60);          -- History data