### @name news-default
GET {{API_BASE_URL}}/api/v1/fmp/news
Content-Type: application/json

> {%
    client.test("News Request with default parameters executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Response status is not success");
        client.assert(response.body.data && Array.isArray(response.body.data), "Data is not an array");
    });

    client.test("News data contains expected fields", function() {
        if (response.body.data && response.body.data.length > 0) {
            const newsItem = response.body.data[0];
            client.assert(newsItem.hasOwnProperty('title'), "Missing title field");
            client.assert(newsItem.hasOwnProperty('publishedDate'), "Missing publishedDate field");
        }
    });

    client.test("Response contains parameters with default values", function() {
        client.assert(response.body.hasOwnProperty('page'), "Missing page parameter");
        client.assert(response.body.hasOwnProperty('size'), "Missing size parameter");
    });
%}


### @name news-with-large-size
GET {{API_BASE_URL}}/api/v1/fmp/news?size=100
Content-Type: application/json

> {%
    client.test("News Request with large size executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

### @name news-with-invalid-parameters
GET {{API_BASE_URL}}/api/v1/fmp/news?page=invalid&size=invalid
Content-Type: application/json

> {%
    client.test("News Request with invalid parameters should handle gracefully", function() {
        client.assert(response.status === 200 || response.status === 400, "Response status unexpected");
    });
%}