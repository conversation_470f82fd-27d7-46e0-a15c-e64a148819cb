/**
 * Verfügbare Log-Level für das Logging-System
 * @enum {string}
 */
const LOG_LEVELS = {
    /** Kritische Fehler, die sofortige Aufmerksamkeit erfordern */
    ERROR: 'ERROR',
    /** Warnungen über potenzielle Probleme */
    WARN: 'WARN', 
    /** Informative Nachrichten über wichtige Operationen */
    INFO: 'INFO',
    /** Detaillierte Debug-Informationen für Entwickler */
    DEBUG: 'DEBUG'
};

/**
 * Formatiert eine Log-Nachricht mit zusätzlichem Kontext
 * 
 * @param {string} level - Log-Level aus LOG_LEVELS
 * @param {string} functionName - Name der aufrufenden Funktion
 * @param {string} message - Die Hauptnachricht des Logs
 * @param {Object} [details={}] - Zusätzliche Kontext-Informationen
 * @returns {Object} Formatierte Log-Nachricht mit Zeitstempel und bereinigten Details
 * 
 * @example
 * const logMsg = formatLogMessage(LOG_LEVELS.INFO, 'getUser', 'User retrieved', { userId: 123 });
 * // Returns: { timestamp: '2024-11-23T...', level: 'INFO', function: 'getUser', ... }
 */
function formatLogMessage(level, functionName, message, details = {}) {
    const timestamp = new Date().toISOString();
    return {
        timestamp,
        level,
        function: functionName,
        message,
        details: cleanDetails(details)
    };
}

/**
 * Bereinigt sensitive oder zu große Daten aus den Log-Details
 * 
 * @param {Object} details - Zu bereinigende Details
 * @returns {Object} Bereinigte Details ohne sensitive Daten
 * 
 * @example
 * const cleaned = cleanDetails({ password: '123', result: [1,2,3] });
 * // Returns: { password: '***', resultCount: 3 }
 */
function cleanDetails(details) {
    const cleaned = {...details};
    // Sensitive Daten entfernen/maskieren
    if (cleaned.password) cleaned.password = '***';
    if (cleaned.token) cleaned.token = '***';
    
    // Große Datensätze kürzen
    if (cleaned.result && Array.isArray(cleaned.result)) {
        cleaned.resultCount = cleaned.result.length;
        delete cleaned.result;
    }
    
    return cleaned;
}

/**
 * Erweitertes Logging für die Entwicklungsumgebung
 * 
 * @param {string} level - Log-Level aus LOG_LEVELS
 * @param {string} functionName - Name der aufrufenden Funktion
 * @param {string} message - Die Hauptnachricht des Logs
 * @param {Object} [details={}] - Zusätzliche Debug-Informationen
 * 
 * @example
 * devLog(LOG_LEVELS.DEBUG, 'processData', 'Processing started', { itemCount: 5 });
 */
function devLog(level, functionName, message, details = {}) {
    // Always log environment mode at startup only
    if (!global._nodemonLoggingInitialized) {
        console.log(`Logging in ${process.env.NODE_ENV || 'development'} mode with nodemon`);
        global._nodemonLoggingInitialized = true;
    }
    
    // Always log in development or when NODE_ENV is not explicitly set to production
    // This ensures logs are visible when running with nodemon
    if (process.env.NODE_ENV !== 'production') {
        const cleanedDetails = cleanDetails(details);
        const logData = formatLogMessage(level, functionName, message, {
            ...cleanedDetails
        });
        
        // Lazy Evaluation für Performance-Metriken
        if (level === LOG_LEVELS.ERROR || level === LOG_LEVELS.WARN) {
            // Formatiere memory-Objekt
            const mem = process.memoryUsage();
            logData.details.memory = `${Math.round(mem.heapUsed / 1024 / 1024)}MB/${Math.round(mem.heapTotal / 1024 / 1024)}MB`;
            
            // Kürze und formatiere Stack-Trace
            if (details.stack) {
                const stackLines = details.stack.split('\n')
                    .slice(0, 3)
                    .map(line => line.trim()
                        .replace(/\s+at\s+/, '')
                        .replace(/\(|\)/g, '')
                        .replace(/\/var\/www\/vhosts\/ml-algotrader\.com\/api\.ml-algotrader\.com\//g, '')
                    );
                logData.details.stack = stackLines.join(' -> ');
            }
        }

        // Log all levels to console (including DEBUG)
        const { level: lvl, function: fn, message: msg, details: dtls } = logData;
        const detailsStr = Object.entries(dtls)
            .map(([k, v]) => `${k}=${v}`)
            .join(' ');
            
        // Use appropriate console method based on log level
        switch (lvl) {
            case LOG_LEVELS.ERROR:
                console.error(`${lvl} [${fn}] ${msg} | ${detailsStr}`);
                break;
            case LOG_LEVELS.WARN:
                console.warn(`${lvl} [${fn}] ${msg} | ${detailsStr}`);
                break;
            case LOG_LEVELS.INFO:
                console.log(`${lvl} [${fn}] ${msg} | ${detailsStr}`);
                break;
            case LOG_LEVELS.DEBUG:
                console.debug(`${lvl} [${fn}] ${msg} | ${detailsStr}`);
                break;
            default:
                console.log(`${lvl} [${fn}] ${msg} | ${detailsStr}`);
        }
    }
}

/**
 * Optimiertes Logging für die Produktionsumgebung
 * 
 * Reduziert Log-Ausgaben und entfernt entwicklungsspezifische Details
 * für bessere Performance im Produktivbetrieb.
 * 
 * @param {string} level - Log-Level aus LOG_LEVELS
 * @param {string} functionName - Name der aufrufenden Funktion
 * @param {string} message - Die Hauptnachricht des Logs
 * @param {Object} [details={}] - Zusätzliche Produktions-relevante Details
 * 
 * @example
 * prodLog(LOG_LEVELS.ERROR, 'processOrder', 'Order failed', { orderId: '123' });
 */
function prodLog(level, functionName, message, details = {}) {
    if (process.env.NODE_ENV === 'production') {
        // Entferne entwicklungsspezifische und sensitive Details
        const cleanedDetails = cleanDetails(details);
        delete cleanedDetails.sql;
        delete cleanedDetails.params;
        delete cleanedDetails.stack;
        delete cleanedDetails.memory;
        delete cleanedDetails.timestamp_ms;

        // Minimale Kontext-Informationen für Produktion
        const logData = formatLogMessage(level, functionName, message, {
            ...cleanedDetails,
            environment: 'production'
        });

        // Effiziente String-Formatierung
        const { level: lvl, function: fn, message: msg, details: logDetails } = logData;
        const detailsStr = Object.entries(logDetails)
            .map(([k, v]) => `${k}=${v}`)
            .join(' ');
        const logString = `${lvl} [${fn}] ${msg} | ${detailsStr}`;
        
        // Log all levels to console in production
        switch (lvl) {
            case LOG_LEVELS.ERROR:
                console.error(logString);
                break;
            case LOG_LEVELS.WARN:
                console.warn(logString);
                break;
            case LOG_LEVELS.INFO:
                console.log(logString);
                break;
            case LOG_LEVELS.DEBUG:
                console.debug(logString);
                break;
            default:
                console.log(logString);
        }
    }
}

/**
 * Universelle Logging-Funktion für alle Umgebungen
 * 
 * Leitet Logs basierend auf der Umgebung an devLog oder prodLog weiter.
 * 
 * @param {string} level - Log-Level aus LOG_LEVELS
 * @param {string} functionName - Name der aufrufenden Funktion
 * @param {string} message - Die Hauptnachricht des Logs
 * @param {Object} [details={}] - Zusätzliche Kontext-Informationen
 * 
 * @example
 * log(LOG_LEVELS.INFO, 'startProcess', 'Process started', { processId: 123 });
 */
function log(level, functionName, message, details = {}) {
    devLog(level, functionName, message, details);
    prodLog(level, functionName, message, details);
}

/**
 * Erweitertes Performance-Logging mit detaillierten Ressourcenmetriken
 * 
 * Misst Ausführungszeit und Speicherverbrauch von Funktionen und
 * protokolliert Performance-Warnungen bei Überschreitung von Schwellwerten.
 * 
 * @param {string} functionName - Name der gemessenen Funktion
 * @param {bigint} startTime - Startzeitpunkt der Messung (process.hrtime.bigint())
 * @param {Object} [details={}] - Zusätzliche Performance-relevante Details
 * 
 * @example
 * const startTime = process.hrtime.bigint();
 * // ... Funktion ausführen ...
 * logPerformance('processData', startTime, { dataSize: 1000 });
 */
function logPerformance(functionName, startTime, details = {}) {
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1_000_000; // Konvertierung zu Millisekunden
    
    const memoryUsage = process.memoryUsage();
    const performanceMetrics = {
        duration_ms: duration,
        ...details
    };

    // Performance-Warnung bei langen Ausführungszeiten
    if (duration > 1000) { // Warnung bei > 1 Sekunde
        log(LOG_LEVELS.WARN, functionName, `Lange Ausführungszeit: ${duration.toFixed(2)}ms`, performanceMetrics);
    } else {
        log(LOG_LEVELS.DEBUG, functionName, `Ausführungszeit: ${duration.toFixed(2)}ms`, performanceMetrics);
    }

    // Cache-Effizienz-Metriken
    if (details.cacheHit !== undefined) {
        const cacheMetrics = {
            cache_hit: details.cacheHit,
            cache_key: details.cacheKey,
            cache_ttl: details.cacheTTL
        };
        log(LOG_LEVELS.DEBUG, functionName, 'Cache-Metriken', cacheMetrics);
    }
}

/**
 * Singleton LoggingService class for consistent logging across the application
 */
class LoggingService {
    static instance = null;

    constructor() {
        if (LoggingService.instance) {
            return LoggingService.instance;
        }
        LoggingService.instance = this;
    }

    static getInstance() {
        if (!LoggingService.instance) {
            LoggingService.instance = new LoggingService();
        }
        return LoggingService.instance;
    }

    info(message, details = {}) {
        log(LOG_LEVELS.INFO, this.getCallerName(), message, details);
    }

    error(message, details = {}) {
        log(LOG_LEVELS.ERROR, this.getCallerName(), message, details);
    }

    warn(message, details = {}) {
        log(LOG_LEVELS.WARN, this.getCallerName(), message, details);
    }

    debug(message, details = {}) {
        log(LOG_LEVELS.DEBUG, this.getCallerName(), message, details);
    }

    /**
     * Get the name of the calling function/class for better log context
     * @private
     */
    getCallerName() {
        const error = new Error();
        const stack = error.stack.split('\n');
        // Get the caller's name from the stack trace
        const caller = stack[3] || 'unknown';
        const match = caller.match(/at (.+?) \(/);
        return match ? match[1] : 'unknown';
    }
}

module.exports = {
    LOG_LEVELS,
    log,
    logPerformance,
    LoggingService
};
