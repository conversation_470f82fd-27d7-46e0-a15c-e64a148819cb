{"components": {"schemas": {"CalendarEntry": {"type": "object", "properties": {"id": {"type": "integer", "description": "Eindeutige ID des Kalendereintrags", "example": 1436}, "event_date": {"type": "string", "format": "date-time", "description": "Datum und Uhrzeit des Ereignisses", "example": "2023-04-15T14:30:00Z"}, "country": {"type": "string", "description": "Land, auf das sich das Ereignis bezieht", "example": "US"}, "impact": {"type": "integer", "description": "Auswirkungsgrad des Ereignisses (1-3, wobei 3 die höchste Auswirkung hat)", "minimum": 1, "maximum": 3, "example": 3}, "event_name": {"type": "string", "description": "Name des Ereignisses", "example": "Non-Farm Payrolls"}, "actual": {"type": "string", "description": "Tatsächlicher Wert des Ereignisses (falls verfügbar)", "example": "236K"}, "forecast": {"type": "string", "description": "Prognostizierter Wert des Ereignisses", "example": "240K"}, "previous": {"type": "string", "description": "Vorheriger Wert des Ereignisses", "example": "311K"}, "currency": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>, auf die sich das Ereignis bezieht", "example": "USD"}, "is_today": {"type": "boolean", "description": "G<PERSON>t an, ob das Ereignis heute stattfindet", "example": false}, "is_tomorrow": {"type": "boolean", "description": "G<PERSON><PERSON> an, ob das Ereignis morgen stattfindet", "example": true}, "toggle_deactivate_high_volume_trades": {"type": "boolean", "description": "G<PERSON><PERSON> an, ob Trades mit hohem Volumen für dieses Ereignis deaktiviert sind", "example": true}}}, "DeltaReport": {"type": "object", "properties": {"calendar_id": {"type": "integer", "description": "ID des Kalendereintrags", "example": 1436}, "symbol": {"type": "string", "description": "Trading-Symbol", "example": "EURUSD"}, "event_date": {"type": "string", "format": "date-time", "description": "Datum und Uhrzeit des Ereignisses", "example": "2023-04-15T14:30:00Z"}, "event_name": {"type": "string", "description": "Name des Ereignisses", "example": "Non-Farm Payrolls"}, "delta_1min": {"type": "number", "format": "float", "description": "Preisänderung nach 1 Minute", "example": 0.0012}, "delta_5min": {"type": "number", "format": "float", "description": "Preisänderung nach 5 Minuten", "example": 0.0025}, "delta_15min": {"type": "number", "format": "float", "description": "Preisänderung nach 15 Minuten", "example": 0.0018}, "delta_30min": {"type": "number", "format": "float", "description": "Preisänderung nach 30 Minuten", "example": -0.0005}, "delta_60min": {"type": "number", "format": "float", "description": "Preisänderung nach 60 Minuten", "example": -0.0022}, "price_before": {"type": "number", "format": "float", "description": "Preis vor dem Ereignis", "example": 1.0865}, "price_after_1min": {"type": "number", "format": "float", "description": "Preis 1 Minute nach dem Ereignis", "example": 1.0877}, "price_after_60min": {"type": "number", "format": "float", "description": "Preis 60 Minuten nach dem Ereignis", "example": 1.0843}, "volatility_before": {"type": "number", "format": "float", "description": "Volatilität vor dem Ereignis", "example": 0.0003}, "volatility_after": {"type": "number", "format": "float", "description": "Volatilität nach dem Ereignis", "example": 0.0012}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Invalid days parameters"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getCalendarList"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Invalid days parameters", "function": "getCalendarList"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to retrieve calendar list", "function": "getCalendarList"}}}}}}}