{"paths": {"/api/v1/db/news": {"get": {"summary": "Nachrichten abrufen", "description": "Ruft Finanznachrichten und Marktanalysen aus der Datenbank ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Sortiert nach Veröffentlichungsdatum (neueste zu<PERSON>t)\n- Limitierbar auf eine bestimmte Anzahl von Einträgen\n- Filterbar nach Zeitraum in Tagen\n- Optional filterbar nach Lesestatus (nur ungelesene Nachrichten)\n- Optional filterbar nach Lesezeichenstatus (nur mit Lesezeichen versehene Nachrichten)\n- Optional durchsuchbar nach Stichworten (UND-Verknüpfung)\n- Alle Filter können kombiniert werden für präzise Ergebnisse\n\nSuch-Funktionalität:\n- Keyword-Suche in Titel (title), Nachrichtentext (body) und KI-Zusammenfassung (gpt_summarize)\n- UND-Verknüpfung: Alle angegebenen Keywords müssen gefunden werden\n- Case-insensitive Suche\n- Keywords werden durch Kommas getrennt\n\nAnwendungsfälle:\n- <PERSON><PERSON><PERSON> von aktuellen Finanznachrichten im Trading-Dashboard\n- <PERSON><PERSON><PERSON> von Marktstimmungen und -trends\n- Identifizierung von potenziellen Handelsmöglichkeiten basierend auf Nachrichtenereignissen\n- Überwachung von relevanten Wirtschaftsnachrichten für bestimmte Handelsinstrumente\n- Filtern von ungelesenen Nachrichten für bessere Übersichtlichkeit\n- Zugriff auf gespeicherte/wichtige Nachrichten über Lesezeichen-Filter\n- Gezielte Suche nach spezifischen Themen oder Währungen (z.B. 'USD,EUR' für Dollar-Euro-Nachrichten)", "tags": ["News"], "parameters": [{"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 50, "minimum": 1, "maximum": 1000}, "description": "Maximale Anzahl der zurückzugebenden Nachrichteneinträge", "example": 50}, {"in": "query", "name": "days", "required": false, "schema": {"type": "integer", "default": 3, "minimum": 0, "maximum": 365}, "description": "<PERSON><PERSON><PERSON> der Tage in der Vergangenheit, für die Nachrichten abgerufen werden sollen", "example": 3}, {"in": "query", "name": "onlyUnread", "required": false, "schema": {"type": "boolean"}, "description": "Filter für nur ungelesene Nachrichten. Wenn auf true gesetzt, werden nur Nachrichten mit is_read=0 zurückgegeben. Akzeptiert boolean-Werte oder String-Repräsentationen (true/false, 1/0)", "example": true}, {"in": "query", "name": "onlyBookmarked", "required": false, "schema": {"type": "boolean"}, "description": "Filter für nur mit Lesezeichen versehene Nachrichten. Wenn auf true gesetzt, werden nur Nachrichten mit is_bookmarked=1 zurückgegeben. Akzeptiert boolean-Werte oder String-Repräsentationen (true/false, 1/0)", "example": true}, {"in": "query", "name": "searchKeywords", "required": false, "schema": {"type": "string", "maxLength": 500}, "description": "Kommagetrennte Liste von Stichworten zur Suche in Nachrichten. Suchlogik: UND-Verknüpfung (alle Keywords müssen gefunden werden). Durchsucht Titel (title), Nachrichtentext (body) und KI-Zusammenfassung (gpt_summarize) der Nachrichten (case-insensitive). Beispiel: 'USD,EUR' findet nur Nachrichten, die sowohl USD als auch EUR enthalten.", "example": "USD,Bitcoin,Federal Reserve"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Nachrichten", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NewsItem"}}, "example": [{"news_id": 12345, "title": "US Federal Reserve Raises Interest Rates by 25 Basis Points", "content": "The Federal Reserve raised its benchmark interest rate by 25 basis points on Wednesday, bringing it to a range of 5.00% to 5.25%, the highest level since 2007.", "publish_date": "2023-04-15T14:30:00Z", "source": "Bloomberg", "url": "https://www.bloomberg.com/news/articles/2023-04-15/fed-raises-rates", "category": "Economy", "sentiment": -0.25, "relevance": 0.85, "symbols": "EURUSD,GBPUSD,USDJPY"}, {"news_id": 12344, "title": "ECB Signals Potential Rate Hike in June", "content": "European Central Bank officials signaled they're on track to raise interest rates again in June as inflation remains persistent.", "publish_date": "2023-04-14T10:15:00Z", "source": "Reuters", "url": "https://www.reuters.com/markets/europe/ecb-signals-rate-hike-june", "category": "Economy", "sentiment": -0.15, "relevance": 0.78, "symbols": "EURUSD,EURGBP"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/factor_map/latest": {"get": {"summary": "Neueste Faktorkarte abrufen", "description": "Ruft die neueste Faktorkarte ab, die verschiedene Marktfaktoren und ihre aktuellen Werte enthält.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält Faktoren wie Zinssätze, Inflation, BIP-Wachstum, etc.\n- Enthält eine Zusammenfassung der aktuellen Marktlage\n- Limitierbar auf eine bestimmte Anzahl von Einträgen (standardmäßig nur der neueste Eintrag)\n\nAnwendungsfälle:\n- Anzeige der aktuellen Marktfaktoren im Trading-Dashboard\n- Entscheidungsunterstützung für Trading-Strategien\n- Risikobewertung basierend auf aktuellen Marktbedingungen\n- Ana<PERSON><PERSON> von Markttrends und -stimmungen", "tags": ["News"], "parameters": [{"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1, "maximum": 100}, "description": "Maximale Anzahl der zurückzugebenden Faktorkarten", "example": 1}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Faktorkarte", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FactorMap"}}, "example": [{"factor_map_id": 789, "timestamp": "2023-04-15T14:30:00Z", "factors": {"interest_rates": 0.85, "inflation": 0.72, "gdp_growth": -0.25, "unemployment": 0.15, "consumer_confidence": -0.35}, "market_sentiment": "Bearish", "risk_level": "High", "summary": "Markets are cautious due to rising interest rates and inflation concerns, with a bearish outlook for the near term."}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/news/read-status": {"post": {"summary": "Status einer Nachricht aktualisieren", "description": "Aktualisiert den Lese- und/oder Lesezeichenstatus einer bestimmten Nachricht in der Datenbank.\n\nTechnische Details:\n- Direktes Update in der Datenbank ohne Caching\n- Erfordert die Nachrichten-ID im Request-Body\n- Unterstützt die Aktualisierung von is_read und/oder is_bookmarked\n\nAnwendungsfälle:\n- Mark<PERSON><PERSON> von Nachrichten als gelesen/ungelesen im Benutzerinterface\n- Setzen/Entfernen von Lesezeichen für Nachrichten\n- Tracking des Lesestatus für Benutzeranalysen\n- Filterung von ungelesenen oder mit Lesezeichen versehenen Nachrichten", "tags": ["News"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID der Nachricht, deren Status aktualisiert werden soll"}, "is_read": {"type": "boolean", "description": "<PERSON><PERSON>er Lesestatus der Nachricht (true = gelesen, false = ungelesen)"}, "is_bookmarked": {"type": "boolean", "description": "<PERSON><PERSON>er Lesezeichenstatus der Nachricht (true = mit Lesezeichen versehen, false = ohne Lesezeichen)"}}, "required": ["id"], "anyOf": [{"required": ["is_read"]}, {"required": ["is_bookmarked"]}]}, "examples": {"read-only": {"value": {"id": 12345, "is_read": true}}, "bookmark-only": {"value": {"id": 12345, "is_bookmarked": true}}, "both": {"value": {"id": 12345, "is_read": true, "is_bookmarked": true}}}}}}, "responses": {"200": {"description": "Status erfolgreich aktualisiert", "content": {"application/json": {"schema": {"type": "object", "properties": {"state": {"type": "string", "enum": ["successful"]}, "message": {"type": "string"}}}, "example": {"state": "successful", "message": "Updated status for news item 12345"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "404": {"description": "<PERSON><PERSON><PERSON>t nicht gefunden", "content": {"application/json": {"schema": {"type": "object", "properties": {"state": {"type": "string", "enum": ["error"]}, "message": {"type": "string"}}}, "example": {"state": "error", "message": "News item with ID 12345 not found"}}}}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}