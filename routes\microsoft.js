const express = require('express');
const router = express.Router();
const todoController = require('../controllers/microsoft/todo_controller');

/**
 * Microsoft Integration Routes
 * 
 * Provides endpoints for Microsoft services integration including:
 * - Microsoft ToDo task management
 * - Future Microsoft Graph API integrations
 * 
 * All routes require API key authentication via middleware.
 */

// Microsoft ToDo Routes
/**
 * @swagger
 * /api/v1/microsoft/todo/tasks:
 *   post:
 *     tags: [Microsoft ToDo]
 *     summary: Create a new task in Microsoft ToDo
 *     description: Creates a new task in the specified Microsoft ToDo list using Microsoft Graph API
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MicrosoftTodoTaskCreate'
 *           examples:
 *             basic_task:
 *               summary: Basic task creation
 *               value:
 *                 listId: "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA=="
 *                 title: "Review quarterly reports"
 *                 importance: "normal"
 *             detailed_task:
 *               summary: Detailed task with due date
 *               value:
 *                 listId: "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA=="
 *                 title: "Prepare presentation for client meeting"
 *                 body: "Include market analysis, trading performance metrics, and future strategy recommendations"
 *                 dueDateTime: "2025-01-15T14:00:00.000Z"
 *                 importance: "high"
 *                 categories: ["Work", "Client Meeting", "Presentation"]
 *                 timeZone: "Europe/Berlin"
 *             trading_alert:
 *               summary: Trading-related task
 *               value:
 *                 listId: "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA=="
 *                 title: "Review EURUSD position"
 *                 body: "Check stop loss levels and consider profit taking based on technical analysis"
 *                 importance: "high"
 *                 categories: ["Trading", "EURUSD", "Risk Management"]
 *     responses:
 *       201:
 *         description: Task created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MicrosoftTodoTaskResponse'
 *             example:
 *               success: true
 *               message: "Task created successfully"
 *               data:
 *                 id: "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwBGAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAhHKQZHItDEOVCn8U3xuA2AABmQeVQAAAA=="
 *                 title: "Review quarterly reports"
 *                 body: null
 *                 status: "notStarted"
 *                 importance: "normal"
 *                 createdDateTime: "2025-01-02T10:30:00.000Z"
 *                 lastModifiedDateTime: "2025-01-02T10:30:00.000Z"
 *                 dueDateTime: null
 *                 categories: []
 *                 webUrl: "https://to-do.office.com/tasks/id/AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwBGAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAhHKQZHItDEOVCn8U3xuA2AABmQeVQAAAA=="
 *                 listId: "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA=="
 *               meta:
 *                 requestId: "req_1704193800000_abc123"
 *                 processingTime: 1250
 *                 timestamp: "2025-01-02T10:30:00.000Z"
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Invalid input data"
 *               error: "title is required and must be a non-empty string"
 *               details: {}
 *               meta:
 *                 requestId: "req_1704193800000_abc123"
 *                 processingTime: 45
 *                 timestamp: "2025-01-02T10:30:00.000Z"
 *       401:
 *         description: Authentication failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Microsoft authentication failed"
 *               error: "Invalid or expired credentials"
 *               meta:
 *                 requestId: "req_1704193800000_abc123"
 *                 processingTime: 2100
 *                 timestamp: "2025-01-02T10:30:00.000Z"
 *       403:
 *         description: Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: ToDo list not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       429:
 *         description: Rate limit exceeded
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/todo/tasks', todoController.createTask.bind(todoController));

// Future Microsoft Graph API routes can be added here:
// router.get('/todo/lists', todoController.getLists.bind(todoController));
// router.get('/todo/lists/:listId/tasks', todoController.getTasks.bind(todoController));
// router.patch('/todo/tasks/:taskId', todoController.updateTask.bind(todoController));
// router.delete('/todo/tasks/:taskId', todoController.deleteTask.bind(todoController));
// router.get('/calendar/events', calendarController.getEvents.bind(calendarController));
// router.post('/calendar/events', calendarController.createEvent.bind(calendarController));

module.exports = router;
