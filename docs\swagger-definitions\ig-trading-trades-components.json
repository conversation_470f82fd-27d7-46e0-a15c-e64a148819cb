{"components": {"schemas": {"IGTradeRequest": {"type": "object", "required": ["symbol", "direction", "size"], "properties": {"symbol": {"type": "string", "description": "Handelssymbol für den Trade", "example": "CS.D.EURUSD.TODAY.IP"}, "direction": {"type": "string", "enum": ["BUY", "SELL"], "description": "Handelsrichtung (Kauf oder Verkauf)"}, "size": {"type": "number", "format": "float", "description": "Größe der Position", "example": 1.5}, "limitLevel": {"type": "number", "format": "float", "description": "Take-Profit-Level (optional)", "example": 1.09}, "stopLevel": {"type": "number", "format": "float", "description": "Stop-Loss-Level (optional)", "example": 1.08}, "guaranteedStop": {"type": "boolean", "description": "G<PERSON>t an, ob ein garantierter Stop verwendet werden soll", "default": false}, "trailingStop": {"type": "boolean", "description": "Gibt an, ob ein Trailing-Stop verwendet werden soll", "default": false}, "trailingStopDistance": {"type": "number", "format": "float", "description": "Abstand für den Trailing-Stop (in Punkten)", "example": 10}, "forceOpen": {"type": "boolean", "description": "<PERSON>rz<PERSON>t das Öffnen einer neuen Position", "default": true}, "orderType": {"type": "string", "enum": ["MARKET", "LIMIT", "STOP"], "description": "Art des Auftrags", "default": "MARKET"}, "expiry": {"type": "string", "description": "Ablaufdatum für den Auftrag (nur für LIMIT und STOP)", "example": "2023-12-31T23:59:59"}, "dealReference": {"type": "string", "description": "Benutzerdefinierte Referenz für den Trade", "example": "myRef123"}, "currencyCode": {"type": "string", "description": "Währungscode für den Trade", "example": "EUR"}}}, "IGBridgeTradeRequest": {"type": "object", "required": ["symbol", "cmd", "volume"], "properties": {"symbol": {"type": "string", "description": "Handelssymbol für den Trade", "example": "EURUSD"}, "cmd": {"type": "integer", "enum": [0, 1], "description": "Handelsrichtung (0 = BUY, 1 = SELL)", "example": 0}, "volume": {"type": "number", "format": "float", "description": "Größe der Position", "example": 1.5}, "price": {"type": "number", "format": "float", "description": "Preis für den Trade (optional für Marktaufträge)", "example": 1.08543}, "sl": {"type": "number", "format": "float", "description": "Stop-Loss-Level (optional)", "example": 1.08}, "tp": {"type": "number", "format": "float", "description": "Take-Profit-Level (optional)", "example": 1.09}, "comment": {"type": "string", "description": "Kommentar für den Trade", "example": "API Trade"}, "type": {"type": "integer", "enum": [0, 1, 2, 3, 4], "description": "Auftragstyp (0 = <PERSON>t, 1 = Pending, 2 = Limit, 3 = Stop, 4 = StopLimit)", "default": 0}, "expiration": {"type": "string", "format": "date-time", "description": "Ablaufdatum für den Auftrag (nur für Pending-Aufträge)", "example": "2023-12-31T23:59:59Z"}}}, "IGClosePositionRequest": {"type": "object", "required": ["dealId"], "properties": {"dealId": {"type": "string", "description": "ID der zu schließenden Position", "example": "DIAAABC123"}, "direction": {"type": "string", "enum": ["BUY", "SELL"], "description": "Richtung zum Schließen (entgegengesetzt zur Eröffnungsrichtung)"}, "size": {"type": "number", "format": "float", "description": "Gr<PERSON>ße der zu schließenden Position (für Teilschließungen)", "example": 1.0}, "orderType": {"type": "string", "enum": ["MARKET", "LIMIT", "STOP"], "description": "Art des Schließungsauftrags", "default": "MARKET"}, "price": {"type": "number", "format": "float", "description": "Preis für den Schließungsauftrag (nur für LIMIT und STOP)", "example": 1.08643}, "timeInForce": {"type": "string", "enum": ["EXECUTE_AND_ELIMINATE", "FILL_OR_KILL"], "description": "Zeitbedingung für den Auftrag", "default": "EXECUTE_AND_ELIMINATE"}, "quoteId": {"type": "string", "description": "ID des Angebots (für bestimmte Märkte)", "example": "Q123456"}, "currencyCode": {"type": "string", "description": "Währungscode für den Trade", "example": "EUR"}}}, "IGModifyPositionRequest": {"type": "object", "required": ["dealId"], "properties": {"dealId": {"type": "string", "description": "ID der zu ändernden Position", "example": "DIAAABC123"}, "stopLevel": {"type": "number", "format": "float", "description": "Neues Stop-Loss-Level", "example": 1.081}, "limitLevel": {"type": "number", "format": "float", "description": "Neues Take-Profit-Level", "example": 1.091}, "trailingStop": {"type": "boolean", "description": "<PERSON><PERSON>t an, ob ein Trailing-Stop aktiviert werden soll", "default": false}, "trailingStopDistance": {"type": "number", "format": "float", "description": "Abstand für den Trailing-Stop (in Punkten)", "example": 10}, "guaranteedStop": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> an, ob ein garantierter Stop aktiviert werden soll", "default": false}}}, "IGTradeStatusRequest": {"type": "object", "required": ["dealReference"], "properties": {"dealReference": {"type": "string", "description": "Referenz des Trades, dessen Status abgefragt werden soll", "example": "myRef123"}}}, "IGTradeResponse": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"], "description": "Status der Anfrage"}, "dealId": {"type": "string", "description": "ID des ausgeführten Trades (bei Erfolg)", "example": "DIAAABC123"}, "dealReference": {"type": "string", "description": "Referenz des Trades", "example": "myRef123"}, "dealStatus": {"type": "string", "enum": ["ACCEPTED", "REJECTED"], "description": "Status des Trades"}, "reason": {"type": "string", "description": "Grund für Ablehnung (bei Fehler)", "example": "INSUFFICIENT_FUNDS"}, "errorCode": {"type": "string", "description": "Fehlercode (bei Fehler)", "example": "error.finance.ig-markets.insufficient-funds"}}}, "IGTradeStatusResponse": {"type": "object", "properties": {"dealId": {"type": "string", "description": "ID des Trades", "example": "DIAAABC123"}, "status": {"type": "string", "enum": ["ACCEPTED", "REJECTED", "PENDING"], "description": "Status des Trades"}, "reason": {"type": "string", "description": "Grund für den Status", "example": "SUCCESS"}, "dealStatus": {"type": "string", "description": "Detaillierter Status des Trades", "example": "OPEN"}}}}, "responses": {"IGTradeSuccess": {"description": "Trade erfolgreich ausgeführt", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IGTradeResponse"}}}}, "IGTradeError": {"description": "Fehler bei der Ausführung des Trades", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "Fehlermeldung", "example": "Insufficient funds"}}}}}}}}}