const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');
const { buildAccountSettingsQuery } = require('./queries/account_queries');
const { buildTradingModeQuery } = require('./queries/trading_mode_queries');


async function getAccountSettingsIndependent(refID) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getAccountSettingsIndependent', 'Starting account settings retrieval', {
            refID
        });

        // Validate refID
        refID = validateStringParam(refID, {
            required: true,
            minLength: 1,
            maxLength: 50,
            paramName: 'refID'
        });

        log(LOG_LEVELS.DEBUG, 'getAccountSettingsIndependent', 'Executing query', {
            refID
        });

        const query = buildAccountSettingsQuery();
        const result = await executeQuery(query, [refID]);
        
        if (!result || result.length === 0) {
            throw new DatabaseError('No account found', { refID });
        }

        log(LOG_LEVELS.INFO, 'getAccountSettingsIndependent', 'Successfully retrieved account settings', {
            refID,
            resultCount: result.length
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getAccountSettingsIndependent', 'Failed to retrieve account settings', {
            error: err.message,
            stack: err.stack,
            refID
        });
        throw err;
    } finally {
        logPerformance('getAccountSettingsIndependent', startTime);
    }
}

async function getAccountSettings(req, res) {
    try {
        const result = await withCacheWrapper(
            'ACCOUNT',
            'getAccountSettings',
            () => getAccountSettingsIndependent(req.query.refID),
            [req.query.refID]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getAccountSettings');
        res.status(errorResponse.status).json(errorResponse);
    }
}

async function getTradingModeIndependent(refID) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getTradingModeIndependent', 'Starting trading mode retrieval', {
            refID
        });

        // Validate refID
        refID = validateStringParam(refID, {
            required: true,
            minLength: 1,
            maxLength: 50,
            paramName: 'refID'
        });

        log(LOG_LEVELS.DEBUG, 'getTradingModeIndependent', 'Executing query', {
            refID
        });

        const query = buildTradingModeQuery();
        const result = await executeQuery(query, [refID]);
        
        if (!result || result.length === 0) {
            throw new DatabaseError('No trading mode found', { refID });
        }

        log(LOG_LEVELS.INFO, 'getTradingModeIndependent', 'Successfully retrieved trading mode', {
            refID,
            resultCount: result.length
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getTradingModeIndependent', 'Failed to retrieve trading mode', {
            error: err.message,
            stack: err.stack,
            refID
        });
        throw err;
    } finally {
        logPerformance('getTradingModeIndependent', startTime);
    }
}

async function getTradingMode(req, res) {
    try {
        const result = await withCacheWrapper(
            'ACCOUNT',
            'getTradingMode',
            () => getTradingModeIndependent(req.query.refID),
            [req.query.refID]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getTradingMode');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getAccountSettings,
    getAccountSettingsIndependent,
    getTradingMode,
    getTradingModeIndependent
};
