const ig = require('../../configs/config_ig');
const { LOG_LEVELS, log } = require('../../services/logging_service');
const NodeCache = require('node-cache');
const IGMarketDataProvider = require('../../services/ig_market_data_provider');

// Create cache instance with 10 second TTL
const cache = new NodeCache({ stdTTL: 120 });

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/ig-market-data-endpoints.json
 * and docs/swagger-definitions/ig-market-data-components.json
 * 
 * @tags [IG Markets]
 */
exports.getMarketInfo = async (req, res) => {
    try {
        const { symbol } = req.query;
        const cacheKey = `marketInfo_${symbol}`;
        
        // Check cache first
        const cachedData = cache.get(cacheKey);
        if (cachedData) {
            return res.json(cachedData);
        }
        
        const marketDataProvider = new IGMarketDataProvider(ig.igClientPools['IG-D1'], ig.logger); 
        if (!marketDataProvider) {
            return res.status(400).json({ error: 'Invalid refID' });
        }
        
        const marketInfo = await marketDataProvider.getMarketInfo(symbol);
        if (!marketInfo) {
            return res.status(404).json({ error: 'Market information not found' });
        }
        
        // Cache the result
        cache.set(cacheKey, marketInfo);
        
        res.json(marketInfo);
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getMarketInfo', 'Failed to get market info', { 
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: 'Failed to retrieve market information' });
    }
};

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/ig-market-data-endpoints.json
 * and docs/swagger-definitions/ig-market-data-components.json
 * 
 * @tags [IG Markets]
 */
exports.getPrices = async (req, res) => {
    try {
        const { symbol, limit, resolution } = req.query;
        const refID = req.query.refID || 'IG-P1'; 
        const cacheKey = `prices_${refID}_${symbol}`;
        
        // Check cache first
        const cachedData = cache.get(cacheKey);
        if (cachedData) {
            return res.json(cachedData);
        }
        
        const marketDataProvider = new IGMarketDataProvider(ig.igClientPools[refID], ig.logger); 
        if (!marketDataProvider) {
            return res.status(400).json({ error: 'Invalid refID' });
        }
        
        const prices = await marketDataProvider.getPricesWithLimit(symbol, resolution, limit);
        if (!prices) {
            return res.status(404).json({ error: 'Price data not found' });
        }
        
        // Calculate spread
        const spread = parseFloat((prices.ask - prices.bid).toFixed(5));
        const result = {
            ...prices,
            spread,
            timestamp: new Date().toISOString()
        };
        
        // Cache the result
        cache.set(cacheKey, result);
        
        res.json(result);
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getPrices', 'Failed to get prices', { 
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: 'Failed to retrieve price information' });
    }
};

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/ig-market-data-endpoints.json
 * and docs/swagger-definitions/ig-market-data-components.json
 * 
 * @tags [IG Markets]
 */
exports.getHistoricalPrices = async (req, res) => {
    try {
        const { symbol, resolution, from, to } = req.query;
        const refID = req.query.refID || 'IG-P1';
        
        if (!refID || !symbol || !resolution || !from) {
            return res.status(400).json({ error: 'Missing required parameters' });
        }
        
        const cacheKey = `history_${refID}_${symbol}_${resolution}_${from}_${to}`;
        
        // Check cache first
        const cachedData = cache.get(cacheKey);
        if (cachedData) {
            return res.json(cachedData);
        }
        
        const marketDataProvider = new IGMarketDataProvider(ig.igClientPools[refID], ig.logger); 
        if (!marketDataProvider) {
            return res.status(400).json({ error: 'Invalid refID' });
        }
        
        const fromDate = from;
        const toDate = to;
        
        const candles = await marketDataProvider.getPricesWithDateRange(symbol, resolution, fromDate, toDate);
        if (!candles || candles.length === 0) {
            return res.status(404).json({ error: 'No historical data found for the specified parameters' });
        }
        
        // Cache the result
        cache.set(cacheKey, candles);
        
        res.json(candles);
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getHistoricalPrices', 'Failed to get historical prices', { 
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: 'Failed to retrieve historical price data' });
    }
};

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/ig-market-data-endpoints.json
 * and docs/swagger-definitions/ig-market-data-components.json
 * 
 * @tags [IG Markets]
 */
exports.getLastCandle = async (req, res) => {
    try {
        const { symbol, resolution } = req.query;
        const refID = req.query.refID || 'IG-P1';
        
        if (!refID || !symbol || !resolution) {
            return res.status(400).json({ error: 'Missing required parameters' });
        }
        
        const cacheKey = `lastCandle_${refID}_${symbol}_${resolution}`;
        
        // Check cache first
        const cachedData = cache.get(cacheKey);
        if (cachedData) {
            return res.json(cachedData);
        }
        
        const marketDataProvider = new IGMarketDataProvider(ig.igClientPools[refID], ig.logger); 
        if (!marketDataProvider) {
            return res.status(400).json({ error: 'Invalid refID' });
        }        
        
        const candles = await marketDataProvider.getPricesWithLimit(symbol, resolution, 1);
        if (!candles || candles.length === 0) {
            return res.status(404).json({ error: 'No recent candle data found' });
        }
        const lastcandle = candles.prices[0];
        
        // Cache the result
        cache.set(cacheKey, lastcandle);        
        res.json(lastcandle);
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getLastCandle', 'Failed to get last candle', { 
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: 'Failed to retrieve last candle data' });
    }
};