class DatabaseError extends <PERSON>rror {
    constructor(message, details = {}) {
        super(message);
        this.name = 'DatabaseError';
        this.details = details;
    }
}

class ValidationError extends <PERSON>rror {
    constructor(message, details = {}) {
        super(message);
        this.name = 'ValidationError';
        this.details = details;
    }
}

class AuthenticationError extends Error {
    constructor(message, details = {}) {
        super(message);
        this.name = 'AuthenticationError';
        this.details = details;
    }
}

class BusinessLogicError extends Error {
    constructor(message, details = {}) {
        super(message);
        this.name = 'BusinessLogicError';
        this.details = details;
    }
}

class MicrosoftGraphError extends Error {
    constructor(message, details = {}) {
        super(message);
        this.name = 'MicrosoftGraphError';
        this.details = details;
        this.statusCode = details.statusCode || 500;
        this.graphErrorCode = details.graphErrorCode || 'UnknownError';
    }
}

class MicrosoftOAuthError extends Error {
    constructor(message, details = {}) {
        super(message);
        this.name = 'MicrosoftOAuthError';
        this.details = details;
        this.statusCode = details.statusCode || 401;
        this.oauthError = details.oauthError || 'unknown_error';
    }
}

class MicrosoftRateLimitError extends Error {
    constructor(message, details = {}) {
        super(message);
        this.name = 'MicrosoftRateLimitError';
        this.details = details;
        this.statusCode = 429;
        this.retryAfter = details.retryAfter || 60;
    }
}

class MicrosoftResourceNotFoundError extends Error {
    constructor(message, details = {}) {
        super(message);
        this.name = 'MicrosoftResourceNotFoundError';
        this.details = details;
        this.statusCode = 404;
        this.resourceType = details.resourceType || 'unknown';
    }
}

function handleDatabaseError(err, functionName) {
    console.error(`[${functionName}] Database Error:`, {
        message: err.message,
        stack: err.stack,
        details: err.details || {}
    });
    return {
        error: true,
        status: 500,
        message: 'Database operation failed',
        details: process.env.NODE_ENV === 'production' ? {} : err.details
    };
}

function handleValidationError(err, functionName) {
    console.warn(`[${functionName}] Validation Error:`, {
        message: err.message,
        details: err.details || {}
    });
    return {
        error: true,
        status: 400,
        message: err.message,
        details: err.details || {}
    };
}

function handleAuthenticationError(err, functionName) {
    console.warn(`[${functionName}] Authentication Error:`, {
        message: err.message,
        details: err.details || {}
    });
    return {
        error: true,
        status: 401,
        message: err.message
    };
}

function handleBusinessLogicError(err, functionName) {
    console.warn(`[${functionName}] Business Logic Error:`, {
        message: err.message,
        details: err.details || {}
    });
    return {
        error: true,
        status: 422,
        message: err.message,
        details: err.details || {}
    };
}

function handleMicrosoftGraphError(err, functionName) {
    console.error(`[${functionName}] Microsoft Graph API Error:`, {
        message: err.message,
        statusCode: err.statusCode,
        graphErrorCode: err.graphErrorCode,
        details: err.details || {},
        correlationId: err.details?.correlationId
    });

    // Map Graph API errors to appropriate HTTP status codes
    let status = err.statusCode || 500;
    let message = 'Microsoft Graph API error';

    switch (err.graphErrorCode) {
        case 'Unauthorized':
        case 'InvalidAuthenticationToken':
            status = 401;
            message = 'Microsoft authentication failed';
            break;
        case 'Forbidden':
        case 'InsufficientPermissions':
            status = 403;
            message = 'Insufficient permissions for Microsoft Graph API';
            break;
        case 'NotFound':
            status = 404;
            message = 'Resource not found';
            break;
        case 'TooManyRequests':
            status = 429;
            message = 'Rate limit exceeded';
            break;
        case 'InternalServerError':
        case 'ServiceUnavailable':
            status = 500;
            message = 'Microsoft Graph API service error';
            break;
        default:
            message = err.message || 'Microsoft Graph API error';
    }

    return {
        error: true,
        status: status,
        message: message,
        details: process.env.NODE_ENV === 'production' ? {} : {
            graphErrorCode: err.graphErrorCode,
            correlationId: err.details?.correlationId
        }
    };
}

function handleMicrosoftOAuthError(err, functionName) {
    console.error(`[${functionName}] Microsoft OAuth Error:`, {
        message: err.message,
        oauthError: err.oauthError,
        details: err.details || {},
        correlationId: err.details?.correlationId
    });

    let message = 'Microsoft authentication failed';

    switch (err.oauthError) {
        case 'invalid_client':
            message = 'Invalid or expired credentials';
            break;
        case 'invalid_scope':
            message = 'Invalid permissions scope';
            break;
        case 'unauthorized_client':
            message = 'Client not authorized';
            break;
        case 'invalid_grant':
            message = 'Invalid authentication grant';
            break;
        default:
            message = err.message || 'Microsoft authentication failed';
    }

    return {
        error: true,
        status: 401,
        message: message,
        details: process.env.NODE_ENV === 'production' ? {} : {
            oauthError: err.oauthError,
            correlationId: err.details?.correlationId
        }
    };
}

function handleMicrosoftRateLimitError(err, functionName) {
    console.warn(`[${functionName}] Microsoft Rate Limit Error:`, {
        message: err.message,
        retryAfter: err.retryAfter,
        details: err.details || {},
        correlationId: err.details?.correlationId
    });

    return {
        error: true,
        status: 429,
        message: 'Rate limit exceeded',
        details: {
            retryAfter: err.retryAfter,
            correlationId: err.details?.correlationId
        }
    };
}

function handleMicrosoftResourceNotFoundError(err, functionName) {
    console.warn(`[${functionName}] Microsoft Resource Not Found Error:`, {
        message: err.message,
        resourceType: err.resourceType,
        details: err.details || {},
        correlationId: err.details?.correlationId
    });

    let message = 'Resource not found';

    switch (err.resourceType) {
        case 'todoList':
            message = 'The specified ToDo list was not found';
            break;
        case 'task':
            message = 'The specified task was not found';
            break;
        case 'user':
            message = 'User not found or access denied';
            break;
        default:
            message = err.message || 'Resource not found';
    }

    return {
        error: true,
        status: 404,
        message: message,
        details: process.env.NODE_ENV === 'production' ? {} : {
            resourceType: err.resourceType,
            correlationId: err.details?.correlationId
        }
    };
}

function errorHandler(err, functionName) {
    if (err instanceof DatabaseError) return handleDatabaseError(err, functionName);
    if (err instanceof ValidationError) return handleValidationError(err, functionName);
    if (err instanceof AuthenticationError) return handleAuthenticationError(err, functionName);
    if (err instanceof BusinessLogicError) return handleBusinessLogicError(err, functionName);
    if (err instanceof MicrosoftGraphError) return handleMicrosoftGraphError(err, functionName);
    if (err instanceof MicrosoftOAuthError) return handleMicrosoftOAuthError(err, functionName);
    if (err instanceof MicrosoftRateLimitError) return handleMicrosoftRateLimitError(err, functionName);
    if (err instanceof MicrosoftResourceNotFoundError) return handleMicrosoftResourceNotFoundError(err, functionName);

    // Generic error handling
    console.error(`[${functionName}] Unhandled Error:`, {
        message: err.message,
        stack: err.stack
    });
    return {
        error: true,
        status: 500,
        message: 'Internal server error'
    };
}

module.exports = {
    DatabaseError,
    ValidationError,
    AuthenticationError,
    BusinessLogicError,
    MicrosoftGraphError,
    MicrosoftOAuthError,
    MicrosoftRateLimitError,
    MicrosoftResourceNotFoundError,
    errorHandler
};
