class DatabaseError extends <PERSON>rror {
    constructor(message, details = {}) {
        super(message);
        this.name = 'DatabaseError';
        this.details = details;
    }
}

class ValidationError extends <PERSON>rror {
    constructor(message, details = {}) {
        super(message);
        this.name = 'ValidationError';
        this.details = details;
    }
}

class AuthenticationError extends Error {
    constructor(message, details = {}) {
        super(message);
        this.name = 'AuthenticationError';
        this.details = details;
    }
}

class BusinessLogicError extends Error {
    constructor(message, details = {}) {
        super(message);
        this.name = 'BusinessLogicError';
        this.details = details;
    }
}

function handleDatabaseError(err, functionName) {
    console.error(`[${functionName}] Database Error:`, {
        message: err.message,
        stack: err.stack,
        details: err.details || {}
    });
    return {
        error: true,
        status: 500,
        message: 'Database operation failed',
        details: process.env.NODE_ENV === 'production' ? {} : err.details
    };
}

function handleValidationError(err, functionName) {
    console.warn(`[${functionName}] Validation Error:`, {
        message: err.message,
        details: err.details || {}
    });
    return {
        error: true,
        status: 400,
        message: err.message,
        details: err.details || {}
    };
}

function handleAuthenticationError(err, functionName) {
    console.warn(`[${functionName}] Authentication Error:`, {
        message: err.message,
        details: err.details || {}
    });
    return {
        error: true,
        status: 401,
        message: err.message
    };
}

function handleBusinessLogicError(err, functionName) {
    console.warn(`[${functionName}] Business Logic Error:`, {
        message: err.message,
        details: err.details || {}
    });
    return {
        error: true,
        status: 422,
        message: err.message,
        details: err.details || {}
    };
}

function errorHandler(err, functionName) {
    if (err instanceof DatabaseError) return handleDatabaseError(err, functionName);
    if (err instanceof ValidationError) return handleValidationError(err, functionName);
    if (err instanceof AuthenticationError) return handleAuthenticationError(err, functionName);
    if (err instanceof BusinessLogicError) return handleBusinessLogicError(err, functionName);
    
    // Generic error handling
    console.error(`[${functionName}] Unhandled Error:`, {
        message: err.message,
        stack: err.stack
    });
    return {
        error: true,
        status: 500,
        message: 'Internal server error'
    };
}

module.exports = {
    DatabaseError,
    ValidationError,
    AuthenticationError,
    BusinessLogicError,
    errorHandler
};
