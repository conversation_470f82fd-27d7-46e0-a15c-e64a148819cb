/**
 * SQL-Queries für Chart-Daten Funktionen
 */

const buildChartDataQuery = () => {
    return {
        sql: 'SELECT c.tf, c.symbol, CAST(UNIX_TIMESTAMP(c.timestamp - interval 1 hour) AS DECIMAL(20,0)) as timestamp, c.german_timestamp, \
                     CAST(c.OPEN/POWER(10,s.`precision`) AS DECIMAL(20,4)) AS open, \
                     CAST((c.close+c.OPEN)/POWER(10,s.`precision`) AS DECIMAL(20,4)) AS close, \
                     CAST((c.high+c.open)/POWER(10,s.`precision`) AS DECIMAL(20,4)) AS high, \
                     CAST((c.low+c.open)/POWER(10,s.`precision`) AS DECIMAL(20,4)) AS low, \
                     CAST(c.vol AS DECIMAL(20,0)) as vol \
                  FROM chartdata c, symbols s\
                  WHERE c.symbol=? \
                    AND c.symbol=s.symbol  \
                    AND c.tf=? \
                    AND german_timestamp>  DATE_SUB(CURDATE(), INTERVAL ? day)',
        bigIntAsNumber: true,
        timezone: 'de_de'
    };
};

module.exports = {
    buildChartDataQuery
};
