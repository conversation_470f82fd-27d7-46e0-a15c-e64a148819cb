const rateLimit = require('express-rate-limit');
const { LoggingService } = require('../services/logging_service');

/**
 * Microsoft API Security Middleware
 * 
 * Provides enhanced security measures for Microsoft Graph API integration:
 * - Rate limiting specific to Microsoft API calls
 * - Request validation and sanitization
 * - Security headers enforcement
 * - Audit logging for Microsoft API access
 */

const logger = LoggingService.getInstance();

/**
 * Rate limiter specifically for Microsoft API endpoints
 * More restrictive than general API rate limiting due to external service dependency
 */
const microsoftApiRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 50, // Limit each IP to 50 requests per windowMs for Microsoft APIs
    message: {
        error: 'Too many Microsoft API requests',
        message: 'Rate limit exceeded for Microsoft Graph API endpoints',
        retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        // Use combination of IP and API key for rate limiting
        const apiKey = req.header('X-API-Key') || 'anonymous';
        return `${req.ip}_${apiKey.substring(0, 8)}`;
    },
    onLimitReached: (req, res, options) => {
        const correlationId = req.headers['x-correlation-id'] || `rate_limit_${Date.now()}`;
        logger.warn('Microsoft API rate limit exceeded', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            endpoint: req.originalUrl,
            correlationId,
            apiKey: req.header('X-API-Key')?.substring(0, 8) + '...'
        });
    }
});

/**
 * Security headers middleware for Microsoft API routes
 */
const microsoftSecurityHeaders = (req, res, next) => {
    // Security headers
    res.set({
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
    });
    
    next();
};

/**
 * Request validation middleware for Microsoft API routes
 */
const microsoftRequestValidation = (req, res, next) => {
    const correlationId = req.headers['x-correlation-id'] || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    req.correlationId = correlationId;
    
    // Log incoming request for audit trail
    logger.info('Microsoft API request received', {
        method: req.method,
        endpoint: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        correlationId,
        contentLength: req.get('Content-Length'),
        apiKey: req.header('X-API-Key')?.substring(0, 8) + '...'
    });
    
    // Validate Content-Type for POST/PUT requests
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
        const contentType = req.get('Content-Type');
        if (!contentType || !contentType.includes('application/json')) {
            logger.warn('Invalid Content-Type for Microsoft API request', {
                method: req.method,
                contentType,
                correlationId
            });
            return res.status(400).json({
                success: false,
                message: 'Invalid Content-Type',
                error: 'Content-Type must be application/json for POST/PUT/PATCH requests',
                meta: {
                    correlationId,
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    
    // Validate request body size
    const contentLength = parseInt(req.get('Content-Length') || '0');
    const maxBodySize = 1024 * 1024; // 1MB limit
    if (contentLength > maxBodySize) {
        logger.warn('Request body too large for Microsoft API', {
            contentLength,
            maxBodySize,
            correlationId
        });
        return res.status(413).json({
            success: false,
            message: 'Request body too large',
            error: `Request body must not exceed ${maxBodySize} bytes`,
            meta: {
                correlationId,
                timestamp: new Date().toISOString()
            }
        });
    }
    
    next();
};

/**
 * Input sanitization middleware for Microsoft API routes
 */
const microsoftInputSanitization = (req, res, next) => {
    if (req.body && typeof req.body === 'object') {
        // Sanitize string inputs to prevent injection attacks
        const sanitizeString = (str) => {
            if (typeof str !== 'string') return str;
            
            // Remove potentially dangerous characters
            return str
                .replace(/[<>]/g, '') // Remove HTML tags
                .replace(/javascript:/gi, '') // Remove javascript: protocol
                .replace(/on\w+=/gi, '') // Remove event handlers
                .trim();
        };
        
        const sanitizeObject = (obj) => {
            if (Array.isArray(obj)) {
                return obj.map(item => 
                    typeof item === 'object' ? sanitizeObject(item) : sanitizeString(item)
                );
            }
            
            if (obj && typeof obj === 'object') {
                const sanitized = {};
                for (const [key, value] of Object.entries(obj)) {
                    if (typeof value === 'object') {
                        sanitized[key] = sanitizeObject(value);
                    } else {
                        sanitized[key] = sanitizeString(value);
                    }
                }
                return sanitized;
            }
            
            return sanitizeString(obj);
        };
        
        req.body = sanitizeObject(req.body);
    }
    
    next();
};

/**
 * Response security middleware for Microsoft API routes
 */
const microsoftResponseSecurity = (req, res, next) => {
    const originalJson = res.json;
    
    res.json = function(data) {
        // Ensure sensitive data is not leaked in responses
        if (data && typeof data === 'object') {
            // Remove any potential sensitive fields from error responses
            if (data.error && typeof data.error === 'object') {
                delete data.error.stack;
                delete data.error.config;
                delete data.error.request;
                delete data.error.response;
            }
            
            // Add security metadata
            if (!data.meta) {
                data.meta = {};
            }
            data.meta.correlationId = req.correlationId;
            data.meta.timestamp = new Date().toISOString();
        }
        
        return originalJson.call(this, data);
    };
    
    next();
};

/**
 * Combined Microsoft security middleware stack
 */
const microsoftSecurityStack = [
    microsoftApiRateLimit,
    microsoftSecurityHeaders,
    microsoftRequestValidation,
    microsoftInputSanitization,
    microsoftResponseSecurity
];

module.exports = {
    microsoftApiRateLimit,
    microsoftSecurityHeaders,
    microsoftRequestValidation,
    microsoftInputSanitization,
    microsoftResponseSecurity,
    microsoftSecurityStack
};
