{"paths": {"/api/v1/ig/markets/info": {"get": {"summary": "Marktinformationen abrufen", "description": "Ruft detaillierte Informationen über einen bestimmten Markt/Instrument ab.\n\nTechnische Details:\n- Gecachte Antworten (120 Sekunden TTL)\n- Enthält Instrumentenspezifikationen und aktuelle Marktpreise\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Handelsplanung\n- Marktanalyse\n- Instrumentenspezifikationen prüfen\n- Margin-Anforderungen berechnen", "tags": ["IG Market Data"], "parameters": [{"name": "symbol", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Handelssymbol, für das Informationen abgerufen werden sollen", "example": "CS.D.EURUSD.TODAY.IP"}], "responses": {"200": {"description": "Marktinformationen erfolgreich abgerufen", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarketInfo"}}}}, "404": {"description": "Marktinformationen nicht gefunden", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Market information not found"}}}}}}, "500": {"$ref": "#/components/responses/MarketDataError"}}}}, "/api/v1/ig/markets/prices": {"get": {"summary": "Aktuelle Preise abrufen", "description": "Ruft aktuelle Geld-/Briefkurse für ein bestimmtes Instrument ab.\n\nTechnische Details:\n- Gecachte Antworten (120 Sekunden TTL)\n- Berechnet den Spread zwischen Geld- und Briefkurs\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Preisüberwachung\n- Handelsausführung\n- Spread-Analyse", "tags": ["IG Market Data"], "parameters": [{"name": "symbol", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Handelssymbol, für das Preise abgerufen werden sollen", "example": "CS.D.EURUSD.TODAY.IP"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "Anzahl der abzurufenden Preise"}, {"name": "resolution", "in": "query", "schema": {"type": "string", "default": "MINUTE"}, "description": "Zeitauflösung für die Preise (z.B. MINUTE, HOUR, DAY)"}, {"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "responses": {"200": {"description": "<PERSON>ise erfolgreich abgerufen", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceResponse"}}}}, "404": {"description": "Preisdaten nicht gefunden", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Price data not found"}}}}}}, "500": {"$ref": "#/components/responses/MarketDataError"}}}}, "/api/v1/ig/markets/prices/history": {"get": {"summary": "Historische Preise abrufen", "description": "Ruft historische Preisdaten für ein bestimmtes Instrument und einen bestimmten Zeitraum ab.\n\nTechnische Details:\n- Gecachte Antworten (120 Sekunden TTL)\n- Unterstützt verschiedene Zeitauflösungen\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Technische Analyse\n- Backtesting von Handelsstrategien\n- Chartdarstellung\n- Musteranalyse", "tags": ["IG Market Data"], "parameters": [{"name": "symbol", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Handelssymbol, für das historische Preise abgerufen werden sollen", "example": "CS.D.EURUSD.TODAY.IP"}, {"name": "resolution", "in": "query", "required": true, "schema": {"type": "string", "enum": ["MINUTE", "MINUTE_5", "MINUTE_15", "MINUTE_30", "HOUR", "HOUR_4", "DAY", "WEEK", "MONTH"]}, "description": "Zeitauflösung für die historischen Preise"}, {"name": "from", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time"}, "description": "Startdatum für den Abfragezeitraum", "example": "2023-01-01T00:00:00Z"}, {"name": "to", "in": "query", "schema": {"type": "string", "format": "date-time"}, "description": "Enddatum für den Abfragezeitraum (Standard: aktuelles Datum/Uhrzeit)", "example": "2023-01-31T23:59:59Z"}, {"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "responses": {"200": {"description": "Historische Preise erfolgreich abgerufen", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HistoricalPriceResponse"}}}}, "400": {"description": "Ungültige Anfrageparameter", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Missing required parameters"}}}}}}, "404": {"description": "<PERSON>ine historischen Daten gefunden", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "No historical data found for the specified parameters"}}}}}}, "500": {"$ref": "#/components/responses/MarketDataError"}}}}, "/api/v1/ig/markets/prices/last": {"get": {"summary": "Letzte Kerze abrufen", "description": "Ruft die neueste Preiskerze für ein bestimmtes Instrument und eine bestimmte Zeitauflösung ab.\n\nTechnische Details:\n- Gecachte Antworten (120 Sekunden TTL)\n- Liefert OHLC-Daten für die letzte abgeschlossene Kerze\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Aktuelle Marktbedingungen überwachen\n- Handelssignale generieren\n- Technische Indikatoren aktualisieren", "tags": ["IG Market Data"], "parameters": [{"name": "symbol", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Handelssymbol, für das die letzte Kerze abgerufen werden soll", "example": "CS.D.EURUSD.TODAY.IP"}, {"name": "resolution", "in": "query", "required": true, "schema": {"type": "string", "enum": ["MINUTE", "MINUTE_5", "MINUTE_15", "MINUTE_30", "HOUR", "HOUR_4", "DAY", "WEEK", "MONTH"]}, "description": "Zeitauflösung für die Kerze"}, {"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "responses": {"200": {"description": "Letzte Kerze erfolgreich abgerufen", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LastCandleResponse"}}}}, "400": {"description": "Ungültige Anfrageparameter", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Missing required parameters"}}}}}}, "404": {"description": "<PERSON><PERSON>n gefunden", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "No recent candle data found"}}}}}}, "500": {"$ref": "#/components/responses/MarketDataError"}}}}}}