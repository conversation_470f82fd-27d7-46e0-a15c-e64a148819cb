{"components": {"schemas": {"RiskManagementState": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time", "description": "Zeitpunkt der letzten Aktualisierung", "example": "2023-04-15T14:30:00Z"}, "symbol": {"type": "string", "description": "Trading-Symbol, für das der Risikomanagement-Status gilt", "example": "EURUSD"}, "risk_on_mode": {"type": "string", "description": "Aktueller Risikomodus", "enum": ["Low-Risk", "Standard", "High-Risk"], "example": "Standard"}, "market_situation": {"type": "string", "description": "Aktuelle Marktsituation", "enum": ["Short", "Neutral-Wait", "<PERSON>"], "example": "Neutral-Wait"}, "reasoning": {"type": "string", "description": "Begründung für den aktuellen Status", "example": "Market volatility has decreased, switching to neutral stance"}, "vola_15": {"type": "number", "format": "float", "description": "Volatilität der letzten 15 Minuten", "example": 0.0012}, "vola_30": {"type": "number", "format": "float", "description": "Volatilität der letzten 30 Minuten", "example": 0.0018}, "vola_60": {"type": "number", "format": "float", "description": "Volatilität der letzten 60 Minuten", "example": 0.0025}}}, "RiskManagementStateHistory": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time", "description": "Zeitpunkt der Statusänderung", "example": "2023-04-15T14:30:00Z"}, "symbol": {"type": "string", "description": "Trading-Symbol", "example": "EURUSD"}, "risk_on_mode": {"type": "string", "description": "Risiko<PERSON><PERSON>", "enum": ["Low-Risk", "Standard", "High-Risk"], "example": "Standard"}, "market_situation": {"type": "string", "description": "Marktsituation", "enum": ["Short", "Neutral-Wait", "<PERSON>"], "example": "Neutral-Wait"}, "reasoning": {"type": "string", "description": "Begründung für die Statusänderung", "example": "Market volatility has decreased, switching to neutral stance"}}}, "RiskModeUpdateResponse": {"type": "object", "properties": {"status": {"type": "string", "description": "Status der Aktualisierung", "enum": ["success", "error"], "example": "success"}, "message": {"type": "string", "description": "Meldung zur Aktualisierung", "example": "Risk mode updated to Standard for EURUSD"}}}, "MarketSituationUpdateResponse": {"type": "object", "properties": {"status": {"type": "string", "description": "Status der Aktualisierung", "enum": ["success", "error"], "example": "success"}, "message": {"type": "string", "description": "Meldung zur Aktualisierung", "example": "Market situation updated to Neutral-Wait for EURUSD"}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Symbol is required"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getRiskManagementState"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Symbol is required", "function": "getRiskManagementState"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch risk management state", "function": "getRiskManagementState"}}}}}}}