const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { TIME_CONSTANTS, QUERY_LIMITS } = require('../../configs/constants');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam, validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');
const {
    buildTradeHistoryQuery,
    buildTradeHistoryStopLevelQuery,
    buildTradeHistoryEquityQuery
} = require('./queries/trade_history_queries');

async function getTradeHistoryIndependent(refID, symbol, days) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getTradeHistoryIndependent', 'Starting trade history retrieval', { refID, symbol, days });

        // Validate refID
        if (!refID) {
            throw new ValidationError('refID is required');
        }
        refID = validateStringParam(refID, { required: true, paramName: 'refID' });

        // Validate symbol if provided
        if (symbol !== undefined) {
            symbol = validateStringParam(symbol, { minLength: 1, maxLength: 50, paramName: 'symbol' });
        }

        // Calculate and validate days
        const defaultDays = symbol === undefined ?
            TIME_CONSTANTS.DEFAULT_DAYS.TRADE_HISTORY :
            TIME_CONSTANTS.DEFAULT_DAYS.TRADE_HISTORY_SYMBOL;

        let parsedDays;
        if (days !== undefined) {
            parsedDays = parseInt(days);
            if (isNaN(parsedDays) || parsedDays < 0) {
                throw new ValidationError('days must be a non-negative number');
            }
            // If days=0, set it to 1 to show at least the current day
            parsedDays = Math.max(1, parsedDays);
            if (parsedDays > 365) {
                throw new ValidationError('days cannot exceed 365');
            }
        } else {
            parsedDays = defaultDays;
        }

        const sql = buildTradeHistoryQuery(symbol);
        const params = symbol === undefined ? [refID, parsedDays] : [refID, parsedDays, symbol];
        log(LOG_LEVELS.INFO, 'getTradeHistoryIndependent', 'SQL Parameters:', params);
        const options = { bigIntAsNumber: true };

        log(LOG_LEVELS.DEBUG, 'getTradeHistoryIndependent', 'Executing query', { refID, symbol, days: parsedDays });

        let result = await executeQuery(sql, params, options);

        // Transform comment field by splitting at first hyphen
        if (result && Array.isArray(result)) {
            result = result.map(row => {
                if (row.comment) {
                    const parts = row.comment.split('-');
                    if (parts.length > 1) {
                        row.comment = parts.slice(1).join('-');
                    }
                }
                return row;
            });
        }

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getTradeHistoryIndependent', 'Failed to retrieve trade history', {
            error: err.message,
            stack: err.stack,
            refID,
            symbol,
            days
        });
        throw err;
    } finally {
        logPerformance('getTradeHistoryIndependent', startTime);
    }
}

async function getTradeHistory(req, res) {

    try {
        // Validate required parameters first
        if (!req.query.refID) {
            throw new ValidationError('refID is required');
        }

        // Set default days if not provided
        const defaultDays = req.query.symbol === undefined ?
            TIME_CONSTANTS.DEFAULT_DAYS.TRADE_HISTORY :
            TIME_CONSTANTS.DEFAULT_DAYS.TRADE_HISTORY_SYMBOL;

        const days = req.query.days || defaultDays;

        const result = await withCacheWrapper(
            'TRADE',
            'getTradeHistory',
            () => getTradeHistoryIndependent(req.query.refID, req.query.symbol, req.query.days),
            [req.query.refID, req.query.symbol, req.query.days]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getTradeHistory');
        res.status(errorResponse.status).json(errorResponse);
    }
}

async function getTradeHistoryStopLevelIndependent(refID, symbol, days) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getTradeHistoryStopLevelIndependent', 'Starting trade history stop level retrieval', {
            refID,
            symbol,
            days
        });

        refID = validateStringParam(refID, {
            required: true,
            minLength: 1,
            maxLength: 50,
            paramName: 'refID'
        });

        if (symbol !== undefined) {
            symbol = validateStringParam(symbol, {
                minLength: 1,
                maxLength: 50,
                paramName: 'symbol'
            });
        }

        const defaultDays = symbol === undefined ?
            TIME_CONSTANTS.DEFAULT_DAYS.TRADE_HISTORY :
            TIME_CONSTANTS.DEFAULT_DAYS.TRADE_HISTORY_SYMBOL;

        days = validateNumericParam(days, {
            defaultValue: defaultDays,
            min: 1,
            max: 365,
            paramName: 'days'
        });

        const query = buildTradeHistoryStopLevelQuery(symbol);
        const params = symbol === undefined ? [refID, days] : [refID, days, symbol];
        const options = { bigIntAsNumber: true };

        const result = await executeQuery(query, params, options);

        if (!result) {
            throw new DatabaseError('Failed to fetch trade history stop level', { refID, symbol, days });
        }

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getTradeHistoryStopLevelIndependent', 'Failed to retrieve trade history stop level', {
            error: err.message,
            stack: err.stack,
            refID,
            symbol,
            days
        });
        throw err;
    } finally {
        logPerformance('getTradeHistoryStopLevelIndependent', startTime);
    }
}

/**
 * API-Endpunkt für Trade History Stop Level
 */
async function getTradeHistoryStopLevel(req, res) {
    try {
        // Validiere Parameter vor dem Cache-Wrapper
        const p_refID = validateStringParam(req.query.refID, {
            required: true,
            minLength: 1,
            maxLength: 50,
            paramName: 'refID'
        });

        let p_symbol;
        if (req.query.symbol !== undefined) {
            p_symbol = validateStringParam(req.query.symbol, {
                minLength: 1,
                maxLength: 50,
                paramName: 'symbol'
            });
        }

        const defaultDays = p_symbol === undefined ?
            TIME_CONSTANTS.DEFAULT_DAYS.TRADE_HISTORY :
            TIME_CONSTANTS.DEFAULT_DAYS.TRADE_HISTORY_SYMBOL;

        let p_days;
        if (req.query.days !== undefined) {
            const parsedDays = parseInt(req.query.days);
            if (isNaN(parsedDays) || parsedDays < 0) {
                throw new ValidationError('days must be a non-negative number');
            }
            // If days=0, set it to 1 to show at least the current day
            p_days = Math.max(1, parsedDays);
        } else {
            p_days = defaultDays;
        }

        if (p_days > 365) {
            throw new ValidationError('days cannot exceed 365');
        }

        const result = await withCacheWrapper(
            'TRADE',
            'getTradeHistoryStopLevel',
            () => getTradeHistoryStopLevelIndependent(p_refID, p_symbol, p_days),
            [p_refID, p_symbol, p_days]
        );

        res.send(result);
    } catch (err) {
        if (!res.headersSent) {
            const errorResponse = errorHandler(err, 'getTradeHistoryStopLevel');
            res.status(errorResponse.status).json(errorResponse);
        }
    }
}

/**
 * @swagger
 * /api/v1/trade_history_equity:
 *   get:
 *     summary: Retrieve historical equity curve
 *     description: |
 *       Calculates detailed historical equity curve for a trading account.
 *       Includes running balance, floating P/L, and daily performance metrics.
 *       
 *       Technical Details:
 *       - Cached response (5 min TTL)
 *       - 1-minute data resolution
 *       - Includes margin utilization
 *       - Tracks drawdown periods
 *       
 *       Analysis Features:
 *       - Maximum drawdown calculation
 *       - Equity curve smoothing
 *       - Performance metrics
 *       - Risk-adjusted returns
 *     tags: [Trade History]
 *     parameters:
 *       - in: query
 *         name: refID
 *         required: true
 *         schema:
 *           type: string
 *         description: Account reference ID for filtering data
 *       - in: query
 *         name: account
 *         schema:
 *           type: string
 *         description: Optional sub-account identifier
 *       - in: query
 *         name: currentbalance
 *         required: true
 *         schema:
 *           type: number
 *           minimum: 0
 *           format: float
 *         description: Current account balance for calculations
 *     responses:
 *       200:
 *         description: Successfully retrieved equity history
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   exit_time:
 *                     type: string
 *                     format: date-time
 *                     description: Timestamp of equity point (UTC)
 *                   equity:
 *                     type: number
 *                     format: float
 *                     description: Total equity at this point
 *                   day_profit:
 *                     type: number
 *                     format: float
 *                     description: Profit/loss for the day
 *                   balance:
 *                     type: number
 *                     format: float
 *                     description: Account balance without floating P/L
 *                   floating_pl:
 *                     type: number
 *                     format: float
 *                     description: Current floating profit/loss
 *                   margin_used:
 *                     type: number
 *                     format: float
 *                     description: Margin currently in use
 *                   free_margin:
 *                     type: number
 *                     format: float
 *                     description: Available margin for new trades
 *                   margin_level:
 *                     type: number
 *                     format: float
 *                     description: Current margin level percentage
 *                   drawdown:
 *                     type: number
 *                     format: float
 *                     description: Current drawdown from peak
 *                   high_watermark:
 *                     type: number
 *                     format: float
 *                     description: Highest recorded equity level
 *       400:
 *         description: Invalid parameters provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: true
 *                 status:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: "Invalid currentbalance parameter"
 *       500:
 *         description: Database or server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: true
 *                 status:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "Database operation failed"
 */
async function getTradeHistoryEquityIndependent(refID, account, currentBalance) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getTradeHistoryEquityIndependent', 'Starting trade history equity retrieval', {
            refID,
            account,
            currentBalance
        });

        refID = validateStringParam(refID, {
            required: true,
            paramName: 'refID'
        });

        account = validateStringParam(account, {
            required: false,
            paramName: 'account'
        });

        currentBalance = validateNumericParam(currentBalance, {
            required: true,
            min: 0,
            paramName: 'currentBalance'
        });

        const query = buildTradeHistoryEquityQuery();
        const result = await executeQuery(query, [currentBalance, refID]);

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getTradeHistoryEquityIndependent', 'Failed to retrieve equity history', {
            error: err.message,
            stack: err.stack,
            refID,
            account,
            currentBalance
        });
        throw err;
    } finally {
        logPerformance('getTradeHistoryEquityIndependent', startTime);
    }
}

/**
 * API-Endpunkt für Trade History Equity
 */
async function getTradeHistoryEquity(req, res) {
    try {
        const result = await withCacheWrapper(
            'TRADE',
            'getTradeHistoryEquity',
            () => getTradeHistoryEquityIndependent(
                req.query.refID,
                req.query.account,
                req.query.currentbalance
            ),
            [req.query.refID, req.query.account, req.query.currentbalance]
        );

        res.send(result);
    } catch (err) {
        if (!res.headersSent) {
            const errorResponse = errorHandler(err, 'getTradeHistoryEquity');
            res.status(errorResponse.status).json(errorResponse);
        }
    }
}

module.exports = {
    getTradeHistory,
    getTradeHistoryIndependent,
    getTradeHistoryStopLevel,
    getTradeHistoryStopLevelIndependent,
    getTradeHistoryEquity,
    getTradeHistoryEquityIndependent
};
