const { LOG_LEVELS, log } = require('./logging_service');
const moment = require('moment');
const { executeQuery } = require('./database_service');

class IGTradeExecutor {
    constructor(igApiClient, logger) {
        this.igApiClient = igApiClient;
        this.logger = logger;
    }

    enrichContext(data) {
        return {
            ...data,
            correlation_id: this.igApiClient.correlationId,
            component: 'trade-executor',
            timestamp: Date.now(),
            environment: this.igApiClient.isDemo() ? 'demo' : 'live'
        };
    }

    /**
     * Executes a trade with the provided parameters
     */
    async executeTrade(tradeParameters) {
        const orderData = this.mapTradeParameters(tradeParameters);

        log(LOG_LEVELS.INFO, 'executeTrade', 'Trade validation', this.enrichContext({
            order_data: orderData
        }));

        const result = await this.igApiClient.makeRequest('POST', 'positions/otc', orderData, 2);

        if (result.code === 200) {
            const dealReference = result.response.dealReference;

            // Wait briefly for deal processing
            await new Promise(resolve => setTimeout(resolve, 500));

            const confirmResult = await this.getConfirmation(dealReference);

            log(LOG_LEVELS.INFO, 'executeTrade', 'Trade executed', this.enrichContext({
                order_data: orderData,
                deal_reference: dealReference,
                confirmation: confirmResult
            }));

            return confirmResult;
        }

        log(LOG_LEVELS.ERROR, 'executeTrade', 'Trade execution failed', this.enrichContext({
            order_data: orderData,
            error: result
        }));

        return result;
    }

    /**
     * Closes an existing position
     */
    async closePosition(dealId, closeParameters) {
        const data = {
            dealId,
            direction: closeParameters.cmd === 1 ? 'BUY' : 'SELL', // Opposite direction
            size: closeParameters.size.toString(),
            orderType: 'MARKET',
            timeInForce: 'FILL_OR_KILL',
            expiry: '-',
            epic: closeParameters.symbol,
            guaranteedStop: 'false',
            forceOpen: 'false',
            currencyCode: closeParameters.currencyCode || 'EUR',
        };

        log(LOG_LEVELS.INFO, 'closePosition', 'Closing position', this.enrichContext({ order_data: data }));
        
        const result = await this.igApiClient.makeRequest('POST', 'positions/otc', data, 1);

        if (result.code === 200 && result.response && result.response.dealReference) {
            const dealReference = result.response.dealReference;

            // Wait briefly for deal processing
            await new Promise(resolve => setTimeout(resolve, 500));

            const confirmResult = await this.getConfirmation(dealReference);

            log(LOG_LEVELS.INFO, 'closePosition', 'Position closed', this.enrichContext({
                order_data: data,
                deal_reference: dealReference,
                confirmation: confirmResult
            }));

            return confirmResult;
        }

        log(LOG_LEVELS.ERROR, 'closePosition', 'Position closing failed', this.enrichContext({
            order_data: data,
            error: result
        }));

        return result;
    }

    /**
     * Modifies an existing position
     */
    async modifyPosition(dealId, modifyParameters) {
        return this.igApiClient.makeRequest('PUT', `positions/otc/${dealId}`, modifyParameters);
    }

    /**
     * Retrieves all open positions
     */
    async getOpenPositions() {
        const result = await this.igApiClient.makeRequest('GET', 'positions');

        if (result.code === 200) {
            const positions = [];
            for (const position of result.response.positions) {
                const positionData = await this._processOpenPosition(position);
                positions.push(positionData);
            }
            return positions;
        }

        return [ { error: result }];
    }

    /**
     * Process an individual open position from the IG API
     * @param {Object} position - The position object from IG API
     * @returns {Object} - Processed position data
     * @private
     */
    async _processOpenPosition(position) {
        const cmd = position.position.direction === 'BUY' ? 0 : 1;
        const positionData = {
            cmd,
            symbol: position.market.epic,
            volume: position.position.size,
            order: position.position.dealId,
            open_price: position.position.level,
            sl: position.position.stopLevel || 0,
            tp: position.position.limitLevel || 0,
            profit: cmd === 0 ? (position.market.bid - position.position.level) * position.position.size* position.position.contractSize :
                (position.position.level - position.market.bid) * position.position.size* position.position.contractSize,
            comment: position.position.dealReference || ''
        };

        try {
            // Fetch trade details and enhance position data
            await this._enhancePositionWithTradeDetails(positionData, position);
            
            // Fetch symbol details
            await this._enhancePositionWithSymbolDetails(positionData, position.market.epic);
        } catch (error) {
            log(LOG_LEVELS.ERROR, '_processOpenPosition', 'Failed to fetch additional data', this.enrichContext({
                dealId: position.position.dealId,
                symbol: position.market.epic,
                error: error.message
            }));
        }

        return positionData;
    }
 
    /**
     * Enhance position data with trade details from database or IG activity
     * @param {Object} positionData - The position data to enhance
     * @param {Object} position - Original position from IG API
     * @private
     */
    async _enhancePositionWithTradeDetails(positionData, position) {
        const dealId = position.position.dealId;
        
        // Try to get trade details from database
        const tradeDetails = await this.getTradeDetailsByDealId(dealId);
        if (tradeDetails && tradeDetails.length > 0) {
            // If we have trade details in the database, use them
            Object.assign(positionData, tradeDetails[0]);
        } else {
            // Only fetch activity data if we don't have trade details in the database
            await this._fetchAndProcessActivityData(positionData, position);
        }
    }

    /**
     * Fetch activity data from IG API and process it
     * @param {Object} positionData - The position data to enhance
     * @param {Object} position - Original position from IG API
     * @private
     */
    async _fetchAndProcessActivityData(positionData, position) {
        const dealId = position.position.dealId;
        // Calculate date range (10 days before today until today)
        const toDate = moment().format('YYYY-MM-DD');
        const fromDate = moment().subtract(10, 'days').format('YYYY-MM-DD');
        
        log(LOG_LEVELS.DEBUG, '_fetchAndProcessActivityData', 'Fetching position activity data', this.enrichContext({
            dealId,
            fromDate,
            toDate
        }));
        
        const activityResult = await this.igApiClient.makeRequest(
            'GET',
            `history/activity?from=${fromDate}&to=${toDate}&detailed=true&dealId=${dealId}&pageSize=10`,
            {},
            3
        );
        
        if (activityResult.code === 200 && activityResult.response.activities?.length > 0) {
            positionData.activity = activityResult.response.activities;
            
            // Extract data from activity and save to database if needed
            await this._processActivityData(positionData, position, activityResult.response.activities);
        } else {
            log(LOG_LEVELS.WARN, '_fetchAndProcessActivityData', 'No activity data found for position', {
                dealId,
                responseCode: activityResult.code
            });
        }
    }

    /**
     * Process activity data to extract useful information and store in database
     * @param {Object} positionData - The position data to enhance
     * @param {Object} position - Original position from IG API
     * @param {Array} activities - Activities retrieved from IG API
     * @private
     */
    async _processActivityData(positionData, position, activities) {
        const dealId = position.position.dealId;
        
        // Find the position opening activity
        const positionOpenedActivity = activities.find(activity => 
            activity.type === 'POSITION' && 
            activity.status === 'ACCEPTED' && 
            activity.details?.actions?.some(action => 
                action.actionType === 'POSITION_OPENED' && 
                action.affectedDealId === dealId
            )
        );
        
        if (positionOpenedActivity) {
            // Extract useful data from activity
            await this._extractDataFromActivity(positionData, position, positionOpenedActivity);
            
            // Save data to database
            await this._savePositionToDatabase(positionData, position, positionOpenedActivity);
        }
        
        log(LOG_LEVELS.DEBUG, '_processActivityData', 'Activity data processed', {
            dealId,
            activityCount: activities.length,
            positionOpenedActivityFound: !!positionOpenedActivity
        });
    }

    /**
     * Extract useful data from activity
     * @param {Object} positionData - The position data to enhance
     * @param {Object} position - Original position from IG API
     * @param {Object} activity - The position opening activity
     * @private
     */
    async _extractDataFromActivity(positionData, position, activity) {
        const dealId = position.position.dealId;
        
        // Extract opening date and format as string (YYYY-MM-DD HH:mm:ss)
        if (activity.date) {
            positionData.open_time = moment(activity.date).format('YYYY-MM-DD HH:mm:ss');
            log(LOG_LEVELS.DEBUG, '_extractDataFromActivity', 'Extracted opening date', {
                dealId,
                openDate: activity.date,
                formattedOpenTime: positionData.open_time
            });
        }
        
        // Extract stop level if available
        if (activity.details?.stopLevel) {
            positionData.sl = activity.details.stopLevel;
            log(LOG_LEVELS.DEBUG, '_extractDataFromActivity', 'Extracted stop level', {
                dealId,
                stopLevel: positionData.sl
            });
        }
    }

    /**
     * Save position data to the database
     * @param {Object} positionData - The position data to enhance
     * @param {Object} position - Original position from IG API
     * @param {Object} activity - The position opening activity
     * @private
     */
    async _savePositionToDatabase(positionData, position, activity) {
        const dealId = position.position.dealId;
        
        try {
            const openTime = moment(activity.date).format('YYYY-MM-DD HH:mm:ss');
            const insertQuery = `INSERT INTO trades (
                account,
                symbol,
                cmd,
                mode,
                type,
                \`create\`,
                stopploss,
                price,
                timeframe,
                xtb_orderid,
                trade_volume,
                strategy,
                refID
            ) VALUES (
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
            )`;
            
            const account = 1000; // Default account ID - adjust based on your system
            const cmdValue = position.position.direction === 'BUY' ? '0' : '1';
            const comment = "@self"; // Set comment to @self as required
            
            // Log identifier to ensure it's correctly available
            const alias = this.igApiClient.alias || 'unknown';
            log(LOG_LEVELS.DEBUG, '_savePositionToDatabase', 'Using IG client identifier', {
                dealId,
                alias,
                clientType: typeof this.igApiClient,
                hasAlias: !!this.igApiClient.alias
            });
            
            const insertParams = [
                account,
                position.market.epic,
                cmdValue,
                0, // Default mode - adjust based on your system
                0, // Default type - adjust based on your system
                openTime,
                activity.details?.stopLevel || 0,
                position.position.level,
                15, // Default timeframe - adjust based on your system
                dealId,
                position.position.size,
                comment, // Using @self for strategy field
                alias // Using the IG client identifier (e.g., IG-P1 or IG-D1)
            ];
            
            const insertResult = await executeQuery(insertQuery, insertParams);
            
            if (insertResult && insertResult.insertId) {
                // Set the formatted date string for the position data
                positionData.open_time = openTime;
                positionData.DBtradeID = insertResult.insertId;
                positionData.account = account;
                positionData.comment = comment; // Set comment to @self in the returned data
                positionData.refID = alias; // Add refID to position data for reference
                
                log(LOG_LEVELS.INFO, '_savePositionToDatabase', 'Added trade to database', {
                    dealId,
                    tradeId: insertResult.insertId,
                    account,
                    comment,
                    refID: alias
                });
            }
        } catch (dbError) {
            log(LOG_LEVELS.ERROR, '_savePositionToDatabase', 'Failed to insert trade into database', this.enrichContext({
                dealId,
                error: dbError.message
            }));
        }
    }

    /**
     * Enhance position with symbol details
     * @param {Object} positionData - The position data to enhance
     * @param {string} epic - The symbol epic
     * @private
     */
    async _enhancePositionWithSymbolDetails(positionData, epic) {
        const symbolDetails = await this.getSymbolDetails(epic);
        // Initialize symbol_details as an empty object by default
        positionData.symbol_details = {};
        if (symbolDetails && symbolDetails.length > 0) {
            positionData.symbol_details = symbolDetails[0];
        }
    }

    /**
     * Fetches trade details from database by deal ID
     * @param {string} dealId - The IG deal ID to query
     * @returns {Promise<Array>} - Array of trade records
     */
    async getTradeDetailsByDealId(dealId) {
        try {
            const query = "SELECT `create` as open_time, id as DBtradeID, account FROM trades WHERE xtb_orderid=?";
            return await executeQuery(query, [dealId]);
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'getTradeDetailsByDealId', 'Database query failed', this.enrichContext({
                dealId,
                error: error.message
            }));
            return [];
        }
    }

    /**
     * Fetches symbol details from database
     * @param {string} symbol - The symbol epic to query
     * @returns {Promise<Array>} - Array of symbol details
     */
    async getSymbolDetails(symbol) {
        try {
            const query = `
                SELECT s.tickSize, s.tickValue, s.currency, s.contractSize, 
                       sm.ig_epic, sm.ig_instrumentName, sm.xtb_symbol, sm.fmp_symbol 
                FROM symbols s, symbols_mappings sm 
                WHERE s.symbol=sm.ig_epic
                AND s.symbol=?`;
            return await executeQuery(query, [symbol]);
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'getSymbolDetails', 'Database query failed', this.enrichContext({
                symbol,
                error: error.message
            }));
            return [];
        }
    }

    /**
     * Gets the status of a specific transaction
     */
    async getTransactionStatus(dealReference) {
        return this.getConfirmation(dealReference);
    }

    /**
     * Maps trade parameters from XTB format to IG format
     */
    mapTradeParameters(params) {
        if (params.tradeTransInfo) {
            const tradeInfo = params.tradeTransInfo;
            const strategy = params.strategy || 'STRATEGY';
            const timeframe = params.timeframe || 'TF';
            const comment = `${strategy}_${timeframe}`;

            const orderData = {
                epic: tradeInfo.symbol,
                expiry: '-',
                direction: tradeInfo.cmd === 0 ? 'BUY' : 'SELL',
                size: tradeInfo.volume.toString(),
                orderType: 'MARKET',
                timeInForce: 'FILL_OR_KILL',
                guaranteedStop: 'false',
                forceOpen: 'true',
                currencyCode: 'EUR'
            };

            // Add deal reference
            if (comment) {
                orderData.dealReference = this.formatDealReference(comment);
            }

            // Set stop loss
            if (tradeInfo.sl && tradeInfo.sl > 0) {
                orderData.stopLevel = tradeInfo.sl.toString();
            } else if (params.stopOffset && params.stopOffset !== 0) {
                const stopLevel = this.calculateStopLevel(tradeInfo, params.stopOffset);
                if (stopLevel !== null) {
                    orderData.stopLevel = stopLevel.toString();
                }
            }

            // Set trailing stop if configured
            if (tradeInfo.trailingStop) {
                orderData.trailingStop = 'true';
                orderData.trailingStopIncrement = tradeInfo.trailingStopDistance.toString();
            }

            return orderData;
        }

        return params;
    }

    /**
     * Calculates stop level based on price and offset
     */
    calculateStopLevel(tradeInfo, stopOffset) {
        if (tradeInfo.cmd === 0 && stopOffset > 0) {
            stopOffset = -stopOffset;
        } else if (tradeInfo.cmd === 1 && stopOffset < 0) {
            stopOffset = -stopOffset;
        }

        if (tradeInfo.price) {
            return Number((tradeInfo.price + stopOffset).toFixed(2));
        }

        return null;
    }

    /**
     * Gets confirmation details for a deal
     */
    async getConfirmation(dealReference) {
        const result = await this.igApiClient.makeRequest('GET', `confirms/${dealReference}`, {}, 1);

        if (result.code === 200) {            
            
            return this.enrichContext({
                status: 'success',
                dealId: result.response.dealId || '',
                dealStatus: result.response.dealStatus || 'UNKNOWN',
                reason: result.response.reason || 'UNKNOWN',
                dealReference,
                details: result.response
            });
        }

        return this.enrichContext({
            status: 'unclear',
            dealReference,
            message: 'Trade confirmation details unavailable',
            result
        });
    }

    /**
     * Formats a deal reference to comply with IG requirements
     */
    formatDealReference(comment) {
        const timestamp = Date.now().toString().slice(-4);
        return (timestamp + '-' + comment)
            .replace(/[^A-Za-z0-9_\-]/g, '-')
            .substring(0, 30);
    }

    /**
     * Retrieves transaction history for a date range
     */
    async getTransactionHistory(fromDate, toDate) {
        const historyContext = {
            from_date: fromDate,
            to_date: toDate,
            correlation_id: this.igApiClient.correlationId,
            process_start: moment().format('YYYY-MM-DD HH:mm:ss')
        };

        log(LOG_LEVELS.DEBUG, 'getTransactionHistory', 'Fetching history', this.enrichContext(historyContext));

        const transactionsResult = await this.igApiClient.makeRequest(
            'GET',
            `history/transactions?type=ALL_DEAL&from=${fromDate}&to=${toDate}&pageSize=500`,
            {},
            2
        );

        if (transactionsResult.code !== 200 || !transactionsResult.response.transactions) {
            log(LOG_LEVELS.ERROR, 'getTransactionHistory', 'Failed to retrieve transactions', this.enrichContext({
                error: 'Invalid response',
                response: transactionsResult
            }));
            return { trades: [], error: true };
        }

        const trades = [];
        let processedCount = 0;
        let skippedCount = 0;

        for (const transaction of transactionsResult.response.transactions) {
            if (transaction.transactionType !== 'TRADE') {
                continue;
            }

            const reference = transaction.reference;
            if (!reference) {
                continue;
            }

            // Get dynamic deal ID prefix
            const prefix = await this.getDealIdPrefix();
            const dealId = `${prefix}${reference}`;

            // const existingTrade = await this.getTradeDetailsByDealId(reference);
            // if (existingTrade) {
            //     skippedCount++;
            //     continue;
            // }

            // Get detailed activity
            const detailedActivityResult = await this.igApiClient.makeRequest(
                'GET',
                `history/activity?from=${fromDate}&to=${toDate}&detailed=true&dealId=${dealId}`,
                {},
                3
            );

            if (detailedActivityResult.code !== 200 || !detailedActivityResult.response.activities?.length) {
                continue;
            }

            const detailedActivity = detailedActivityResult.response.activities[0];
            if (!detailedActivity.details?.actions) {
                continue;
            }

            // Find closing action
            let affectedDealId = null;
            for (const action of detailedActivity.details.actions) {
                if (action.actionType === 'POSITION_CLOSED' && action.affectedDealId) {
                    affectedDealId = action.affectedDealId;
                    break;
                }
            }

            if (!affectedDealId) {
                continue;
            }

            // Get opening position data
            const openingActivityResult = await this.igApiClient.makeRequest(
                'GET',
                `history/activity?from=${fromDate}&to=${toDate}&detailed=true&dealId=${affectedDealId}`,
                {},
                3
            );

            if (openingActivityResult.code !== 200 || !openingActivityResult.response.activities?.length) {
                continue;
            }

            const openingActivity = openingActivityResult.response.activities[0];

            try {
                const trade = this.extractTradeData(transaction, detailedActivity, openingActivity);
                if (trade) {
                    trades.push(trade);
                    processedCount++;
                }
            } catch (error) {
                log(LOG_LEVELS.ERROR, 'getTransactionHistory', 'Trade processing error', this.enrichContext({
                    dealId,
                    error: error.message
                }));
            }
        }

        return {
            trades,
            processedCount,
            skippedCount
        };
    }

    /**
     * Extracts trade data from transaction and activity records
     */
    extractTradeData(transaction, closingActivity, openingActivity) {
        const symbol = openingActivity.epic;
        const comment = openingActivity.details?.dealReference || '';

        // Get volume and direction
        const volume = Math.abs(parseFloat(transaction.size || '0'));
        const tradeDirection = transaction.size?.startsWith('-') ? 1 : 0;

        // Extract timestamps
        const openTime = moment.utc(transaction.openDateUtc).valueOf();
        const closeTime = moment.utc(transaction.dateUtc).valueOf();

        // Get price levels
        const openPrice = parseFloat(transaction.openLevel || 0);
        const closePrice = parseFloat(transaction.closeLevel || 0);

        // Extract profit/loss
        let profitLoss = 0;
        if (transaction.profitAndLoss) {
            profitLoss = parseFloat(transaction.profitAndLoss.replace(/[^-0-9.]/g, ''));
        }

        if (!symbol || !openTime || !closeTime) {
            return null;
        }

        return {
            position: transaction.reference,
            order2: transaction.reference,
            open_time: openTime,
            close_time: closeTime,
            cmd: tradeDirection,
            symbol,
            volume,
            open_price: openPrice,
            close_price: closePrice,
            profit: profitLoss,
            comment
        };
    }
    /**
     * Gets a dynamic deal ID prefix based on the latest trade in the database
     * @param {string} [transactionDate=null] - Optional transaction date to filter by
     * @returns {Promise<string>} - The deal ID prefix to use
     */
    async getDealIdPrefix(transactionDate = null) {
        // Default prefixes as fallback if database query fails
        const defaultDemoPrefix = 'DIAAAAS';
        const defaultLivePrefix = 'DIAAAAN';
        
        try {
            // Determine which refID to look for based on environment
            const refID = this.igApiClient.isDemo() ? 'IG-D1' : 'IG-P1';
            
            // Use the transaction date if provided, otherwise use current date
            const queryDate = transactionDate ? transactionDate : moment().format('YYYY-MM-DD');
            
            log(LOG_LEVELS.DEBUG, 'getDealIdPrefix', 'Fetching deal ID prefix', this.enrichContext({
                refID,
                queryDate,
                environment: this.igApiClient.isDemo() ? 'demo' : 'live'
            }));
            
            // SQL query to get the latest trade with the specified refID from the specified date
            let query = "SELECT xtb_orderid FROM trades WHERE refID = ? AND xtb_orderid IS NOT NULL AND DATE(`create`) = ? ORDER BY id DESC LIMIT 1";
            let result = await executeQuery(query, [refID, queryDate]);
            
            // If we found a result, extract the prefix
            if (result && result.length > 0 && result[0].xtb_orderid) {
                // Extract the first 7 characters as the prefix
                const prefix = result[0].xtb_orderid.substring(0, 7);
                
                // Validate the prefix format (should be all uppercase letters and digits)
                if (/^[A-Z0-9]{7}$/.test(prefix)) {
                    log(LOG_LEVELS.DEBUG, 'getDealIdPrefix', 'Using dynamic deal prefix', this.enrichContext({
                        prefix,
                        source_orderid: result[0].xtb_orderid,
                        date: queryDate
                    }));
                    
                    return prefix;
                }
            }
            
            // If no results found for the specified date, try without the date constraint
            query = "SELECT xtb_orderid FROM trades WHERE refID = ? AND xtb_orderid IS NOT NULL ORDER BY id DESC LIMIT 1";
            result = await executeQuery(query, [refID]);
            
            if (result && result.length > 0 && result[0].xtb_orderid) {
                const prefix = result[0].xtb_orderid.substring(0, 7);
                
                if (/^[A-Z0-9]{7}$/.test(prefix)) {
                    log(LOG_LEVELS.DEBUG, 'getDealIdPrefix', 'Using dynamic deal prefix from fallback', this.enrichContext({
                        prefix,
                        source_orderid: result[0].xtb_orderid,
                        requested_date: queryDate
                    }));
                    
                    return prefix;
                }
            }
            
            // If we couldn't get a valid prefix from the database, use the default
            log(LOG_LEVELS.WARN, 'getDealIdPrefix', 'Using default deal prefix', this.enrichContext({
                reason: 'no_valid_prefix_in_db',
                default_prefix: this.igApiClient.isDemo() ? defaultDemoPrefix : defaultLivePrefix,
                requested_date: queryDate
            }));
            
            return this.igApiClient.isDemo() ? defaultDemoPrefix : defaultLivePrefix;
            
        } catch (error) {
            // Log the error and use default prefix
            log(LOG_LEVELS.ERROR, 'getDealIdPrefix', 'Deal prefix error', this.enrichContext({
                error: error.message,
                fallback_prefix: this.igApiClient.isDemo() ? defaultDemoPrefix : defaultLivePrefix
            }));
            
            return this.igApiClient.isDemo() ? defaultDemoPrefix : defaultLivePrefix;
        }
    }
}

module.exports = IGTradeExecutor;