/**
 * SQL-Queries für Calendar Funktionen
 */

const { FILTERS } = require('../../../configs/constants');

const buildCalendarListQuery = () => {
    return {
        sql: 'SELECT c.*, \
                   ci.`description`, \
                   ci.`key_de` as description_key_de, \
                   round((c.current-c.forecast)*100/c.current,1) as `delta`,\
                   t.regEx, \
                   t.toggle_deactivate_new_trades, t.toggle_deactivate_high_volume_trades,\
                   t.toggle_close_all_positions_before,\
                   t.toggle_actiontime_minutes_window_after, \
                   t.toggle_actiontime_minutes_window_before\
              from calendar c \
         LEFT JOIN calendar_indicators ci ON c.title LIKE ci.`key` and c.country=ci.country\
         LEFT JOIN calendar_toggles t ON c.title LIKE t.regEx AND c.country=t.country\
            WHERE timestamp between DATE_SUB(CURDATE(),INTERVAL ? DAY) and DATE_ADD(CURDATE(),INTERVAL ? DAY) \
              AND impact >= ? \
              AND FIND_IN_SET(c.country, ?) > 0 \
         ORDER BY timestamp',
        bigIntAsNumber: true,
        timezone: 'de_de'
    };
};

module.exports = {
    buildCalendarListQuery
};
