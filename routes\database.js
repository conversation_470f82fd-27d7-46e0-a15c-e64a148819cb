const express = require('express');
const router = express.Router();

// Import controllers
const tradeLogsController = require('../controllers/database/trade_logs_controller');
const symbolSetupController = require('../controllers/database/symbol_setup_controller');
const aiPredictionsController = require('../controllers/database/ai_predictions_controller');
const symbolController = require('../controllers/database/symbol_controller');
const pivotController = require('../controllers/database/pivot_controller');
const riskManagementController = require('../controllers/database/risk_management_controller');
const tradeHistoryController = require('../controllers/database/trade_history_controller');
const calendarController = require('../controllers/database/calendar_controller');
const chartController = require('../controllers/database/chart_controller');
const accountController = require('../controllers/database/account_controller');
const newsController = require('../controllers/database/news_controller');
const mirrorTradingController = require('../controllers/database/mirror_trading_controller');
const timeBasedController = require('../controllers/database/statistics/time_based_controller');
const currentPeriodController = require('../controllers/database/statistics/current_period_controller');
const analysisController = require('../controllers/database/statistics/analysis_controller');
const controller_db_strategies = require('../controllers/database/common/controller_db_get_strategies');
const controller_db_mirrorTradingLogs = require('../controllers/database/common/controller_db_mirrorTradingLogs');
const controller_db_cud = require('../controllers/database/common/controller_db_put_post_delete');
const controller_ai_summary = require('../controllers/database/common/controller_ai_summary');

// Basic Database Routes
router.get('/chartdata', chartController.getChartData);
router.get('/trade_history', tradeHistoryController.getTradeHistory);
router.get('/trade_history_stops', tradeHistoryController.getTradeHistoryStopLevel);
router.get('/trade_history_equity', tradeHistoryController.getTradeHistoryEquity);
router.get('/calendar/deltareport', calendarController.getDeltaReport);
router.get('/calendar/list', calendarController.getCalendarList);
router.get('/pivotpoints', pivotController.getPivotpoints);
router.get('/tradingmode', accountController.getTradingMode);
router.get('/symbol_setups', symbolSetupController.getSymbolSetups);
router.get('/symbol_setup/:symbol', symbolSetupController.getSymbolSetups);

// Mirror Trading Routes
router.get('/mirrorTradingSettings', mirrorTradingController.getMirrorTradingSettings);
router.get('/mirrorTradingLogs', mirrorTradingController.getMirrorTradingLogs);
router.get('/mirrorTradingLogs/profits', controller_db_mirrorTradingLogs.getMirrorTradingLogProfits);
router.get('/mirrorTradingLogs/losts', controller_db_mirrorTradingLogs.getMirrorTradingLogLosts);
router.get('/mirrorTradingLogs/losts/stats', controller_db_mirrorTradingLogs.getMirrorTradingLogLostsStats);
router.get('/mirrorTradingLogs/bes/stats', controller_db_mirrorTradingLogs.getMirrorTradingLogBESStats);
router.get('/mirrorTradingLogs/profitcurves', controller_db_mirrorTradingLogs.getMirrorTradingLogProfitCurves);
router.get('/mirrorTradingLogs/optimizations', controller_db_mirrorTradingLogs.getMirrorTradingOptimizations);

// Trade and Account Routes
router.get('/tradeLogs', tradeLogsController.getTradeLogs);
router.get('/accountSettings', accountController.getAccountSettings);

// Strategy Routes
router.get('/strategies/report', controller_db_strategies.getStrategyReportForRefID);
router.get('/strategies/documentation', controller_db_strategies.getStrategyDocumentation);
router.get('/strategies/report_aggregated', controller_db_strategies.getStrategyActivationReportAggregated);
router.get('/strategies/last_events', controller_db_strategies.getStrategyLastEvents);

// Statistics Routes
router.get('/statistics/day', timeBasedController.getDayStatistics);
router.get('/statistics/week', timeBasedController.getWeekStatistics);
router.get('/statistics/month', timeBasedController.getMonthStatistics);
router.get('/statistics/current_day', currentPeriodController.getCurrentDayStatistics);
router.get('/statistics/current_week', currentPeriodController.getCurrentWeekStatistics);
router.get('/statistics/current_month', currentPeriodController.getCurrentMonthStatistics);
router.get('/statistics/current_year', currentPeriodController.getCurrentYearStatistics);
router.get('/statistics/weekday', analysisController.getWeekDayStats);
router.get('/statistics/dayhour', analysisController.getDayHourStats);
router.get('/statistics/strategy_symbols', analysisController.getStrategySymbolStats);
router.get('/statistics/symbols_strategy', analysisController.getSymbolStrategyStats);

// News and AI Routes
router.get('/latest_factor_map', newsController.getLatestFactorMap);
router.get('/news', newsController.getNewsFromDB);
router.get('/symbol_ai_predictions', aiPredictionsController.getSymbolAIPredictions);
router.get('/all_used_symbols_with_pip_values', symbolController.getAllUsedSymbolsWithPipValues);
router.get('/ai_daily_summary_vars', controller_ai_summary.getAiDailySummaryVars);
router.get('/ai_daily_summary', controller_ai_summary.getAiDailySummary);

// Risk Management Routes
router.get('/riskmgmt/state', riskManagementController.getRiskManagementState);
router.get('/riskmgmt/state/:symbol', riskManagementController.getRiskManagementStates);
router.put('/riskmgmt/state/:symbol/risk_on_mode', riskManagementController.updateRiskOnMode);  
router.put('/riskmgmt/state/:symbol/market_situation', riskManagementController.updateMarketSituation);

// Write Operations
router.put('/tradingmode', controller_db_cud.putTradingMode);
router.put('/calendar/toggles/:calendarID', controller_db_cud.putCalendarToggles);
router.put('/strategies/:strategyID', controller_db_cud.putStrategyToggles);
router.put('/symbol_setup/:symbolID', controller_db_cud.putSymbolSetup); 
router.put('/mirror_trading_settings', controller_db_cud.putMirrorTradingSettings);
router.post('/news/status', controller_db_cud.postNewsStatus); 

// Demo Operations
router.post('/db/xtb_trade', controller_db_cud.postXtbTrades);
router.delete('/db/xtb_trade', controller_db_cud.deleteXtbTrades);

module.exports = router;