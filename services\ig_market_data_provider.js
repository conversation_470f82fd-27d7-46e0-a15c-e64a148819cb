const { LOG_LEVELS, log } = require('./logging_service');
const NodeCache = require('node-cache');

class IGMarketDataProvider {
    constructor(igApiClient, logger, dbHandler = null) {
        this.igApiClient = igApiClient;
        this.logger = logger;
        this.dbHandler = dbHandler;
        this.cache = new NodeCache({ stdTTL: 300 }); // 5 minute TTL
        this.PRECISION = 2;
    }

    enrichContext(data) {
        return {
            ...data,
            correlation_id: this.igApiClient.correlationId,
            component: 'market-data',
            timestamp: Date.now(),
            environment: this.igApiClient.isDemo() ? 'demo' : 'live'
        };
    }

    async getMarketInfo(symbol) {
        const startTime = process.hrtime.bigint();
        const cacheKey = `market_info_${symbol}`;
        
        // Check cache first
        const cachedData = this.cache.get(cacheKey);
        if (cachedData) {
            log(LOG_LEVELS.DEBUG, 'getMarketInfo', 'Cache hit', this.enrichContext({
                symbol,
                cached: true
            }));
            return cachedData;
        }

        try {
            const result = await this.igApiClient.makeRequest('GET', `markets/${symbol}`, {}, 3);
            const duration = Number(process.hrtime.bigint() - startTime) / 1e6;

            log(LOG_LEVELS.DEBUG, 'getMarketInfo', 'Market info retrieved', this.enrichContext({
                symbol,
                duration_ms: duration,
                success: result.code === 200
            }));

            if (result.code === 200) {
                const marketData = {
                    ...result.response,
                    snapshot: {
                        bid: result.response?.snapshot?.bid,
                        offer: result.response?.snapshot?.offer,
                        scalingFactor: result.response?.instrument?.scalingFactor
                    }
                };
                this.cache.set(cacheKey, marketData);
                return marketData;
            }

            return {};
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'getMarketInfo', 'Error fetching market info', this.enrichContext({
                symbol,
                error: error.message
            }));
            throw error;
        }
    }

    async getPricesWithLimit(symbol, resolution = 'MINUTE_15', max = 100) {
        const startTime = process.hrtime.bigint();
        const cacheKey = `prices_${symbol}_${resolution}_${max}`;

        // Check cache first
        const cachedData = this.cache.get(cacheKey);
        if (cachedData) {
            return cachedData;
        }

        try {
            const endpoint = `prices/${symbol}?resolution=${resolution}&max=${max}`;
            const result = await this.igApiClient.makeRequest('GET', endpoint, {}, 3);
            const duration = Number(process.hrtime.bigint() - startTime) / 1e6;

            log(LOG_LEVELS.DEBUG, 'getPrices', 'Price data retrieved', this.enrichContext({
                symbol,
                resolution,
                max,
                duration_ms: duration,
                data_points: result.response?.prices?.length || 0
            }));

            if (result.code === 200) {
                this.cache.set(cacheKey, result);
            }

            return result.response;
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'getPrices', 'Error fetching prices', this.enrichContext({
                symbol,
                resolution,
                max,
                error: error.message
            }));
            throw error;
        }
    }

    async getPricesWithDateRange(symbol, resolution, from, to = null) {
        const startTime = process.hrtime.bigint();
        const cacheKey = `historical_${symbol}_${resolution}_${from}_${to}`;

        // Check cache first
        const cachedData = this.cache.get(cacheKey);
        if (cachedData) {
            return cachedData;
        }

        try {
            let endpoint = `prices/${symbol}?resolution=${resolution}&from=${from}&pageSize=100`;
            if (to) {
                endpoint += `&to=${to}`;
            }

            const result = await this.igApiClient.makeRequest('GET', endpoint, {}, 3);
            const duration = Number(process.hrtime.bigint() - startTime) / 1e6;

            log(LOG_LEVELS.DEBUG, 'getHistoricalPrices', 'Historical data retrieved', this.enrichContext({
                symbol,
                resolution,
                from,
                to,
                duration_ms: duration,
                data_points: result.response?.prices?.length || 0
            }));

            if (result.code === 200) {
                this.cache.set(cacheKey, result);
            }

            return result;
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'getHistoricalPrices', 'Error fetching historical prices', this.enrichContext({
                symbol,
                resolution,
                from,
                to,
                error: error.message
            }));
            throw error;
        }
    }

    async getLastCandle(symbol, period) {
        const startTime = process.hrtime.bigint();
        const resolution = this.mapPeriodToResolution(period);
        const result = await this.getPricesWithLimit(symbol, resolution, 1);

        if (result.code === 200 && result.response?.prices?.length > 0) {
            const lastPrice = result.response.prices[0];
            const data = {
                open: lastPrice.openPrice.ask,
                high: lastPrice.highPrice.ask,
                low: lastPrice.lowPrice.ask,
                close: lastPrice.closePrice.ask,
                volume: lastPrice.lastTradedVolume,
                timestamp: new Date(lastPrice.snapshotTime).getTime(),
                timeString: lastPrice.snapshotTime,
                precision: this.PRECISION
            };

            log(LOG_LEVELS.DEBUG, 'getLastCandle', 'Last candle retrieved', this.enrichContext({
                symbol,
                period,
                resolution,
                duration_ms: Number(process.hrtime.bigint() - startTime) / 1e6,
                data
            }));

            return data;
        }

        log(LOG_LEVELS.WARN, 'getLastCandle', 'No data available', this.enrichContext({
            symbol,
            period,
            resolution
        }));

        return {};
    }

    transformPriceData(prices, symbol, timeframe) {
        const startTime = process.hrtime.bigint();
        
        try {
            const transformedData = prices.map(price => ({
                ctm: new Date(price.snapshotTimeUTC).getTime(),
                ctmString: price.snapshotTime,
                open: price.openPrice.ask,
                low: price.lowPrice.ask - price.openPrice.ask,
                high: price.highPrice.ask - price.openPrice.ask,
                close: price.closePrice.ask - price.openPrice.ask,
                vol: price.lastTradedVolume || 0
            }));

            log(LOG_LEVELS.DEBUG, 'transformPriceData', 'Price data transformed', this.enrichContext({
                symbol,
                timeframe,
                input_count: prices.length,
                output_count: transformedData.length,
                duration_ms: Number(process.hrtime.bigint() - startTime) / 1e6
            }));

            return transformedData;
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'transformPriceData', 'Error transforming price data', this.enrichContext({
                symbol,
                timeframe,
                error: error.message
            }));
            throw error;
        }
    }

    mapPeriodToResolution(period) {
        const PERIOD_RESOLUTION_MAP = {
            1: 'MINUTE',
            5: 'MINUTE_5',
            15: 'MINUTE_15',
            30: 'MINUTE_30',
            60: 'HOUR',
            240: 'HOUR_4',
            1440: 'DAY'
        };

        return PERIOD_RESOLUTION_MAP[period] || 'MINUTE_15';
    }

    async getLastChartData(symbol, period) {
        const resolution = this.mapPeriodToResolution(period);
        const result = await this.igApiClient.makeRequest(
            'GET',
            `prices/${symbol}?resolution=${resolution}&max=1`,
            {},
            3
        );

        if (result.code === 200 && result.response?.prices?.length > 0) {
            const lastPrice = result.response.prices[0];
            return {
                open: lastPrice.openPrice.ask,
                high: lastPrice.highPrice.ask,
                low: lastPrice.lowPrice.ask,
                close: lastPrice.closePrice.ask,
                vol: lastPrice.lastTradedVolume,
                ctm: new Date(lastPrice.snapshotTime).getTime(),
                ctmString: lastPrice.snapshotTime,
                precision: this.PRECISION
            };
        }

        return {};
    }
}

module.exports = IGMarketDataProvider;