const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { TIME_CONSTANTS } = require('../../configs/constants');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam, validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');
const { buildChartDataQuery } = require('./queries/chart_data_queries');

async function getChartDataIndependent(symbol, period, days) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.DEBUG, 'getChartDataIndependent', 'Starting chart data retrieval', {
            symbol,
            period,
            days
        });

        // Validate symbol
        symbol = validateStringParam(symbol, {
            required: true,
            minLength: 1,
            maxLength: 50,
            paramName: 'symbol'
        });

        // Validate period
        period = validateNumericParam(period, {
            required: true,
            min: 1,
            max: 10080, // Maximum minutes in a day
            paramName: 'period'
        });

        // Validate days
        days = validateNumericParam(days, {
            required: true,
            min: 1,
            max: 1800,
            paramName: 'days'
        });

        log(LOG_LEVELS.DEBUG, 'getChartDataIndependent', 'Executing query', {
            symbol,
            period,
            days
        });

        const query = buildChartDataQuery();
        const result = await executeQuery(query, [symbol, period, days]);
        
        log(LOG_LEVELS.INFO, 'getChartDataIndependent', 'Successfully retrieved chart data', {
            symbol,
            period,
            days,
            result: result
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getChartDataIndependent', 'Failed to fetch chart data', {
            error: err.message,
            stack: err.stack,
            symbol,
            period,
            days
        });
        throw err;
    } finally {
        logPerformance('getChartDataIndependent', startTime);
    }
}

async function getChartData(req, res) {
    try {
        const period = parseInt(req.query.period);
        const days = parseInt(req.query.days);

        const result = await withCacheWrapper(
            'CHART',
            'getChartData',
            () => getChartDataIndependent(req.query.symbol, period, days),
            [req.query.symbol, period, days]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getChartData');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getChartData,
    getChartDataIndependent
};
