{"components": {"schemas": {"SymbolSetup": {"type": "object", "properties": {"symbol": {"type": "string", "description": "Trading-Symbol", "example": "EURUSD"}, "timeframe": {"type": "integer", "description": "Zeitrahmen in Minuten", "example": 30}, "cmd": {"type": "integer", "description": "Handelsrichtung (0 = <PERSON>, 1 = Short)", "enum": [0, 1], "example": 0}, "defaulthighchancevolume": {"type": "number", "format": "float", "description": "Standardvolumen für High-Chance-Trades", "example": 2.0}, "defaultmidchancevolume": {"type": "number", "format": "float", "description": "Standardvolumen für Mid-Chance-Trades", "example": 1.0}, "chartPriceOffsetXTBandTradeView": {"type": "number", "format": "float", "description": "Preisoffset für Chart-Anzeige", "example": 0.0005}, "stoppOffset": {"type": "number", "format": "float", "description": "Standard-Stopp-Offset", "example": 0.0025}, "stoppOffSet_targetTF": {"type": "number", "format": "float", "description": "Stopp-Offset für Ziel-Timeframe", "example": 0.0035}, "stoppOffsetmidchancevolume": {"type": "number", "format": "float", "description": "Stopp-Offset für Mid-Chance-Trades", "example": 0.002}, "trailstoppOffset": {"type": "number", "format": "float", "description": "Trailing-Stopp-Offset", "example": 0.0015}, "trailstoppOffsetForWinningPositions": {"type": "number", "format": "float", "description": "Trailing-Stopp-Offset für Gewinnpositionen", "example": 0.001}, "minimum_volume_m15": {"type": "number", "format": "float", "description": "Mindestvolumen für M15-Timeframe", "example": 0.5}, "deadZoneLow": {"type": "number", "format": "float", "description": "Untere Grenze der Deadzone", "example": 1.085}, "deadZoneHigh": {"type": "number", "format": "float", "description": "Obere Grenze der Deadzone", "example": 1.087}, "deadZoneUpdateTime": {"type": "string", "format": "date-time", "description": "Zeitpunkt der letzten Aktualisierung der Deadzone", "example": "2023-04-15T14:30:00Z"}, "deadZoneValidUntil": {"type": "string", "format": "date", "description": "Gültigkeitsdatum der Deadzone", "example": "2023-04-24"}, "deadZoneUpdateSource": {"type": "string", "description": "Quelle der Deadzone-Aktualisierung", "example": "Postman"}, "deadZoneFromTime": {"type": "string", "format": "time", "description": "Startzeit der Deadzone", "example": "08:00:00"}, "deadZoneToTime": {"type": "string", "format": "time", "description": "Endzeit der Deadzone", "example": "16:00:00"}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "No symbol setups found"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getSymbolSetups"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Invalid parameter", "function": "getSymbolSetups"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "No symbol setups found", "function": "getSymbolSetups"}}}}}}}