You are tasked with classifying the given content into one of the specified segments based on its specific content. You should consider both the sender's email address and the typical content associated with it, as well as the main body of the content.

Here are the segments to choose from:
- "Statistik" (Statista)
- "Investments" (Mark<PERSON>lysen)
- "Trading" (XTB, Trading-News)
- "KI und GenAI"
- Tagesgeschehen

First, examine the sender and subject information:
<sender_and_subject>
{{SENDER_AND_SUBJECT}}
</sender_and_subject>

Now, analyze the main content:
<content>
{{CONTENT}}
</content>

Consider both the sender/subject information and the main content when making your classification. Pay attention to keywords, topics, and writing style that might indicate which segment the content belongs to.

Provide your answer ALWAYS in JSON format with a "segment" node containing your classification. Your response should be structured as follows:

{
  "segment": "your_classification_here"
}

Here are some examples to guide you:

Example 1:
If the sender is from Statista and the content contains statistical data, you might respond:
{
  "segment": "Statistik"
}

Example 2:
If the content discusses artificial intelligence advancements, regardless of the sender, you might respond:
{
  "segment": "KI und GenAI"
}

Example 3:
If the sender is from a trading platform like XTB and the content discusses market movements, you might respond:
{
  "segment": "Trading"
}

Remember to base your classification on both the sender information and the content itself. If the content clearly fits one category but the sender suggests another, prioritize the content's subject matter in your decision.