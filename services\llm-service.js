const { OpenAI } = require("openai");
const Anthropic = require("@anthropic-ai/sdk");
const Groq = require("groq-sdk");
const { Mistral } = require("@mistralai/mistralai");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const { LangfuseTraceManager } = require('./langfuse-service');
const { v4: uuidv4 } = require('uuid');
const { logAIRequest } = require('../controllers/gpt/controller_gpt_databaseHelper');
require('dotenv').config();

const DEFAULT_SYSTEM_MESSAGE = "You are a helpful assistant."; 
const MAX_TOKENS = 8192;
const TEMPERATURE = 0.2;
const PREVIEW_LENGTH = 250;

// LLM Provider Instances
const llmProviders = {
    openai: new OpenAI({ apiKey: process.env.OPENAI_API_KEY }),
    anthropic: new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY }),
    groq: new Groq({ apiKey: process.env.GROQ_API_KEY }),
    mistral: new Mistral({ apiKey: process.env.MISTRAL_API_KEY }),
    gemini: new GoogleGenerativeAI(process.env.GEMINI_API_KEY)
}; 

// LLM Model Types
const MODEL_TYPES = {
    GPT: 'gpt-',
    CLAUDE: 'claude-',
    LLAMA: 'llama',
    MISTRAL: 'mistral',
    GEMINI: 'gemini'
};

/**
 * Request LLM with Langfuse integration and optional prompt
 * @param {Array} messages - Array of messages
 * @param {string} llmModel - LLM model to use
 * @param {string} [promptID] - The title of the prompt in Langfuse (optional)
 * @param {number} [promptVersion=1] - The version of the prompt (optional)
 * @param {Object} [promptVariables={}] - Variables for the prompt template (optional)
 * @returns {string} - LLM response
 */
// Helper functions for different LLM providers
async function handleOpenAIRequest(messages, llmModel) {
    const response = await llmProviders.openai.chat.completions.create({
        model: llmModel,
        messages: messages,
    });
    
    // Extract token counts from response
    const tokenCounts = {
        total: response.usage?.total_tokens || null,
        input: response.usage?.prompt_tokens || null,
        output: response.usage?.completion_tokens || null
    };
    
    return {
        content: response.choices[0].message.content,
        tokenCounts
    };
}

async function handleGroqRequest(messages, llmModel) {
    const response = await llmProviders.groq.chat.completions.create({
        model: llmModel,
        messages: messages,
        temperature: TEMPERATURE,
        max_tokens: MAX_TOKENS,
        top_p: 1,
        stream: false
    });
    
    // Extract token counts from response
    const tokenCounts = {
        total: response.usage?.total_tokens || null,
        input: response.usage?.prompt_tokens || null,
        output: response.usage?.completion_tokens || null
    };
    
    return {
        content: response.choices[0].message.content,
        tokenCounts
    };
}

async function handleAnthropicRequest(messages, llmModel) {
    const response = await llmProviders.anthropic.messages.create({
        model: llmModel,
        max_tokens: MAX_TOKENS,
        temperature: TEMPERATURE,
        messages: messages
    }, {
        headers: {
            "anthropic-beta": "max-tokens-3-5-sonnet-2024-07-15"
        }
    });
    
    // Extract token counts from response
    const tokenCounts = {
        total: (response.usage?.input_tokens || 0) + (response.usage?.output_tokens || 0),
        input: response.usage?.input_tokens || null,
        output: response.usage?.output_tokens || null
    };
    
    return {
        content: response.content[0].text,
        tokenCounts
    };
}

async function handleMistralRequest(messages, llmModel) {
    const response = await llmProviders.mistral.chat.complete({
        model: llmModel,
        messages: messages,
        max_tokens: MAX_TOKENS,
        temperature: TEMPERATURE
    });
    
    // Extract token counts from response
    const tokenCounts = {
        total: response.usage?.total_tokens || null,
        input: response.usage?.prompt_tokens || null,
        output: response.usage?.completion_tokens || null
    };
    
    return {
        content: response.choices[0].message.content,
        tokenCounts
    };
}

async function handleGeminiRequest(messages, llmModel) {
    const model = llmProviders.gemini.getGenerativeModel({ model: llmModel });
    
    // Nachrichten in das richtige Format konvertieren
    const formattedMessages = messages.map(msg => ({
        role: msg.role === 'system' ? 'user' : msg.role,
        parts: [{ text: msg.content }]
    }));

    const chat = model.startChat();
    let response;
    
    for (const msg of formattedMessages) {
        response = await chat.sendMessage(msg.parts[0].text);
    }
    
    // Gemini doesn't provide token counts in the same way, so we'll estimate
    // based on a rough approximation of 4 characters per token
    const inputText = formattedMessages.reduce((acc, msg) => acc + msg.parts[0].text, '');
    const outputText = response.response.text();
    
    const estimatedInputTokens = Math.ceil(inputText.length / 4);
    const estimatedOutputTokens = Math.ceil(outputText.length / 4);
    
    const tokenCounts = {
        total: estimatedInputTokens + estimatedOutputTokens,
        input: estimatedInputTokens,
        output: estimatedOutputTokens
    };
    
    let responseText = outputText.replace(/```/g, '').replace(/`/g, '').replace(/json/g, '');
    
    return {
        content: responseText,
        tokenCounts
    };
}

async function requestLLM(messages, llmModel, assistentID = null, promptID = null, promptVersion = 1, promptVariables = "{}") {
    // Generate a unique request ID
    const requestId = uuidv4();
    
    // Record start time for performance measurement
    const startTime = process.hrtime.bigint();
    
    if (process.env.NODE_ENV !== 'production')
        console.log("[llm-service][requestLLM] Requesting LLM:", { requestId, llmModel, promptID: promptID });
    const traceName = promptID ? `langfuse-${promptID}` : `ai-assistant-${llmModel}`;
    const traceManager = new LangfuseTraceManager(
        traceName,
        { messages, promptVariables },
        `api-algotrader-api-${promptID || llmModel}`
    );

    try {
        let compiledPrompt = "You are a helpful assistant.";
        let prompt = null;
        let fullMessages;
        if (assistentID) {
            try {
                if (process.env.NODE_ENV !== 'production')
                  console.log("[llm-service][requestLLM] Fetching assistant instructions...");
                const assistant = await llmProviders.openai.beta.assistants.retrieve(assistentID);
                compiledPrompt = assistant.instructions;
                if (process.env.NODE_ENV !== 'production')
                  console.log("[llm-service][requestLLM] Assistant instructions:", compiledPrompt);

            } catch (promptError) {
                console.error(`[llm-service][requestLLM] Error fetching assistent-prompt "${assistentID}":`, promptError);
            }
        }
        if (promptID) {
            try {
                console.log("[llm-service][requestLLM] Fetching prompt with ID:", promptID);
                prompt = await LangfuseTraceManager.getPrompt(promptID);
                
                if (!prompt || !prompt.prompt) {
                    console.warn(`[llm-service][requestLLM] No valid prompt found for ID "${promptID}". Using default system message.`);
                } else {
                    console.log("[llm-service][requestLLM] Retrieved prompt:", {
                        preview: prompt.prompt.substring(0, 50) + '...',
                        version: prompt.version
                    });
                    
                    try {
                        compiledPrompt = await LangfuseTraceManager.compilePrompt(prompt, promptVariables);
                    } catch (error) {
                        console.warn(`[llm-service][requestLLM] Error compiling prompt "${promptID}": ${error.message}. Using default system message.`);
                    }
                }
            } catch (promptError) {
                console.error(`[llm-service][requestLLM] Error fetching prompt "${promptID}":`, promptError);
            }
        }

        traceManager.startGeneration(
            traceName,
            llmModel,
            promptID ? 0.1 : 0.4,
            prompt || traceName
        );

        let result;
        let tokenCounts = {
            total: null,
            input: null,
            output: null
        };
        
        // Prepare messages based on model type
        if (llmModel.startsWith(MODEL_TYPES.GPT)) {
            fullMessages = [
                { role: "system", content: DEFAULT_SYSTEM_MESSAGE },
                { role: "assistant", content: compiledPrompt },
                ...messages
            ];
            const response = await handleOpenAIRequest(fullMessages, llmModel);
            result = response.content;
            tokenCounts = response.tokenCounts;
        } else if (llmModel.startsWith(MODEL_TYPES.LLAMA)) {
            fullMessages = [
                { role: "system", content: compiledPrompt },
                ...messages
            ];
            const response = await handleGroqRequest(fullMessages, llmModel);
            result = response.content;
            tokenCounts = response.tokenCounts;
        } else if (llmModel.startsWith(MODEL_TYPES.CLAUDE)) {
            fullMessages = Array.isArray(messages) ? messages : [messages];
            fullMessages = fullMessages.map(message => {
                if (typeof message.content === 'string') {
                    return {
                        role: message.role,
                        content: [{ type: "text", text: message.content }]
                    };
                }
                return message;
            });
            
            if (fullMessages.length === 0) {
                fullMessages.push({ role: "user", content: compiledPrompt });
            }
            
            const response = await handleAnthropicRequest(fullMessages, llmModel);
            result = response.content;
            tokenCounts = response.tokenCounts;
        } else if (llmModel.startsWith(MODEL_TYPES.MISTRAL)) {
            fullMessages = [
                { role: "system", content: compiledPrompt },
                ...messages
            ];
            const response = await handleMistralRequest(fullMessages, llmModel);
            result = response.content;
            tokenCounts = response.tokenCounts;
        } else if (llmModel.startsWith(MODEL_TYPES.GEMINI)) {
            fullMessages = [
                { role: "system", content: DEFAULT_SYSTEM_MESSAGE },
                { role: "assistant", content: compiledPrompt },
                ...messages
            ];
            // console.log("[llm-service][requestLLM] Messages prepared for Gemini model:", fullMessages, "PromptID:", promptID, "Prompt:", prompt, "LLM Model:", llmModel, "Compiled Prompt:", compiledPrompt, "Full Messages:", fullMessages)
            const response = await handleGeminiRequest(fullMessages, llmModel);
            result = response.content;
            tokenCounts = response.tokenCounts;
        } else {
            throw new Error(`Unsupported model: ${llmModel}`);
        }

        if (process.env.NODE_ENV !== 'production') {
            console.log("[llm-service][requestLLM] Messages prepared for model:", {
                model: llmModel,
                messageCount: fullMessages.length,
                preview: JSON.stringify(fullMessages).substring(0, PREVIEW_LENGTH) + '...'
            });
        }

        traceManager
            .updateGeneration(fullMessages)
            .endGeneration(result, promptID, prompt)
            .updateTrace(result);

        // Remove JSON code block if present
        if (result.startsWith("```json")) {
            result = result.substring(7, result.length - 3);
        }

        // Calculate processing time
        const endTime = process.hrtime.bigint();
        const processingTimeMs = Number(endTime - startTime) / 1000000;
        
        // Log the successful request
        try {
            await logAIRequest({
                request_id: requestId,
                llm_model: llmModel,
                prompt: fullMessages,
                response: result,
                processing_time_ms: processingTimeMs,
                status: 'success',
                assistant_id: assistentID,
                prompt_id: promptID,
                prompt_version: promptVersion,
                token_count: tokenCounts.total,
                token_input_count: tokenCounts.input,
                token_output_count: tokenCounts.output
            });
        } catch (logError) {
            // Just log the error but don't let it affect the main process
            console.error("[llm-service][requestLLM] Error logging successful request:", logError);
        }
        
        console.log("[llm-service][requestLLM] LLM response:", JSON.stringify(result).substring(0,250)+'...');
        return result;

    } catch (error) {
        // Calculate processing time even for errors
        const endTime = process.hrtime.bigint();
        const processingTimeMs = Number(endTime - startTime) / 1000000;
        
        console.error("[llm-service][requestLLM] Error in requestLLM:", error, "PromptID:", promptID);
        console.error("[llm-service][requestLLM] Messages:", JSON.stringify(messages, null, 2));
        if (error.response) {
            console.error("[llm-service][requestLLM] Error details:", {
                data: error.response.data,
                status: error.response.status,
                error: error.response.error
            });
        }
        
        // Log the failed request
        try {
            await logAIRequest({
                request_id: requestId,
                llm_model: llmModel,
                prompt: messages,
                response: null,
                processing_time_ms: processingTimeMs,
                status: 'error',
                error_message: error.message || 'Unknown error',
                assistant_id: assistentID,
                prompt_id: promptID,
                prompt_version: promptVersion,
                token_count: tokenCounts.total,
                token_input_count: tokenCounts.input,
                token_output_count: tokenCounts.output
            });
        } catch (logError) {
            // Just log the error but don't let it affect the main process
            console.error("[llm-service][requestLLM] Error logging failed request:", logError);
        }
        
        throw error;
    }
}

module.exports = {
    requestLLM
};
