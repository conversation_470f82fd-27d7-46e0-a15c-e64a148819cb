{"paths": {"/api/v1/ig/trades/bridge": {"post": {"summary": "Trade im Bridge-Format ausführen", "description": "Führt einen Trade im Legacy-Bridge-Format aus, das mit älteren Systemen kompatibel ist.", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-D1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["tradeTransInfo"], "properties": {"tradeTransInfo": {"type": "object", "required": ["cmd", "symbol", "volume"], "properties": {"cmd": {"type": "integer", "description": "Handelsrichtung (0 = BUY, 1 = SELL)", "enum": [0, 1]}, "symbol": {"type": "string", "description": "Trading-Symbol"}, "volume": {"type": "number", "format": "float", "description": "Handelsvolumen"}, "sl": {"type": "number", "format": "float", "description": "Stop-Loss-Level"}}}, "simulation": {"type": "boolean", "description": "Gibt an, ob der Trade simuliert werden soll"}, "strategy": {"type": "string", "description": "Name der Handelsstrategie"}, "timeframe": {"type": "string", "description": "Zeitrahmen für den Trade"}}}}}}, "responses": {"200": {"description": "Trade erfolgreich ausgeführt", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"]}, "dealId": {"type": "string", "description": "ID des ausgeführten Trades"}, "dealReference": {"type": "string", "description": "Referenz des Trades"}}}}}}, "400": {"description": "Ungültige Anfrage"}, "500": {"description": "Fehler bei der Ausführung des Trades"}}}}, "/api/v1/ig/trades/execute": {"post": {"summary": "Trade ausführen", "description": "Führt einen Trade auf der IG Markets-Plattform aus.", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-D1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["epic", "direction", "size", "orderType"], "properties": {"epic": {"type": "string", "description": "Epic-Code des Instruments"}, "direction": {"type": "string", "enum": ["BUY", "SELL"], "description": "Handelsrichtung"}, "size": {"type": "string", "description": "Größe der Position"}, "expiry": {"type": "string", "description": "Ablaufdatum für den Auftrag"}, "orderType": {"type": "string", "enum": ["MARKET", "LIMIT", "STOP"], "description": "Art des Auftrags"}, "timeInForce": {"type": "string", "enum": ["EXECUTE_AND_ELIMINATE", "FILL_OR_KILL"], "description": "Zeitbedingung für den Auftrag"}, "guaranteedStop": {"type": "string", "enum": ["true", "false"], "description": "G<PERSON>t an, ob ein garantierter Stop verwendet werden soll"}, "forceOpen": {"type": "string", "enum": ["true", "false"], "description": "<PERSON>rz<PERSON>t das Öffnen einer neuen Position"}, "stopLevel": {"type": "string", "description": "Stop-Loss-Level"}, "dealReference": {"type": "string", "description": "Benutzerdefinierte Referenz für den Trade"}, "currencyCode": {"type": "string", "description": "Währungscode für den Trade"}}}}}}, "responses": {"200": {"description": "Trade erfolgreich ausgeführt", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"]}, "dealId": {"type": "string", "description": "ID des ausgeführten Trades"}, "dealReference": {"type": "string", "description": "Referenz des Trades"}}}}}}, "400": {"description": "Ungültige Anfrage"}, "500": {"description": "Fehler bei der Ausführung des Trades"}}}}, "/api/v1/ig/trades/status": {"post": {"summary": "Trade-Status abrufen", "description": "Prüft den Status eines Trades anhand seiner Deal-Referenz.", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-D1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["dealReference"], "properties": {"dealReference": {"type": "string", "description": "Referenz des Trades"}}}}}}, "responses": {"200": {"description": "Trade-Status erfolgreich abgerufen", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "description": "Status des Trades"}, "dealReference": {"type": "string", "description": "Referenz des Trades"}}}}}}, "400": {"description": "Ungültige Anfrage"}, "500": {"description": "Fehler bei der Statusabfrage"}}}}, "/api/v1/ig/trades/modify": {"post": {"summary": "Position ändern", "description": "Ändert eine bestehende Position auf der IG Markets-Plattform.", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-D1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["dealId"], "properties": {"dealId": {"type": "string", "description": "ID der zu ändernden Position"}, "stopLevel": {"type": "string", "description": "Neues Stop-Loss-Level"}}}}}}, "responses": {"200": {"description": "Position erfolgreich geändert", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"]}}}}}}, "400": {"description": "Ungültige Anfrage"}, "500": {"description": "Fehler bei der Änderung der Position"}}}}, "/api/v1/ig/trades/close": {"post": {"summary": "Position schließen", "description": "Sc<PERSON><PERSON><PERSON>t eine bestehende Position auf der IG Markets-Plattform.", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-D1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["dealId", "direction"], "properties": {"dealId": {"type": "string", "description": "ID der zu schließenden Position"}, "direction": {"type": "string", "enum": ["BUY", "SELL"], "description": "Richtung zum Schließen (entgegengesetzt zur Eröffnungsrichtung)"}, "epic": {"type": "string", "description": "Epic-Code des Instruments"}, "size": {"type": "string", "description": "Größe der zu schließenden Position"}, "orderType": {"type": "string", "enum": ["MARKET", "LIMIT", "STOP"], "description": "Art des Schließungsauftrags"}}}}}}, "responses": {"200": {"description": "Position erfolgreich geschlossen", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"]}}}}}}, "400": {"description": "Ungültige Anfrage"}, "500": {"description": "Fehler beim Schließen der Position"}}}}}}