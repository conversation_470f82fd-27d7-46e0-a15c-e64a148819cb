###

// @name mirrorTradingLatestLog
GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs?targetRefID=P1&sourceRefID=D1&limit=2
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}



###

// @name mirrorTradingLatestLog
GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/profits?targetRefID=IG-D1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name mirrorTradingLatestLog
GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/profits?targetRefID=D2&refreshData=true
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name mirrorTradingLatestLog
GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/profits?targetRefID=NONE&refreshData=true
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name mirrorTradingLatestLog
GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/losts?targetRefID=D2&refreshData=true
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name mirrorTradingLatestLog
GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/losts/stats
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name mirrorTradingBES-Stats
GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/bes/stats?refID=D2
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name mirrorTradingLogProfitCurves
GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/profitcurves?targetRefID=D1&days=1&timeframe=1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Response has results", function() {
        client.assert(response.body.hasOwnProperty('results'), "Response does not have 'results' property");
    });

    client.test("Results is an array", function() {
        client.assert(Array.isArray(response.body.results), "Results is not an array");
    });

    if (response.body.results.length > 0) {
        client.test("Result items have correct properties", function() {
            const firstItem = response.body.results[0];
            client.assert(firstItem.hasOwnProperty('targetRefID'), "Item does not have 'targetRefID' property");
            client.assert(firstItem.hasOwnProperty('log_timestamp'), "Item does not have 'log_timestamp' property");
            client.assert(firstItem.hasOwnProperty('log_order2'), "Item does not have 'log_order2' property");
            client.assert(firstItem.hasOwnProperty('profit'), "Item does not have 'profit' property");
        });
    }
%}

###

// @name mirrorTradingLogProfitCurves
GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/optimizations?targetRefID=D1&days=1&timeframe=1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Response has results", function() {
        client.assert(response.body.hasOwnProperty('results'), "Response does not have 'results' property");
    });

    client.test("Results is an array", function() {
        client.assert(Array.isArray(response.body.results), "Results is not an array");
    });

    if (response.body.results.length > 0) {
        client.test("Result items have correct properties", function() {
            const firstItem = response.body.results[0];
            client.assert(firstItem.hasOwnProperty('targetRefID'), "Item does not have 'targetRefID' property");
            client.assert(firstItem.hasOwnProperty('log_timestamp'), "Item does not have 'log_timestamp' property");
            client.assert(firstItem.hasOwnProperty('log_order2'), "Item does not have 'log_order2' property");
            client.assert(firstItem.hasOwnProperty('profit'), "Item does not have 'profit' property");
        });
    }
%}
