/**
 * SQL-Queries für Trade-Logs Funktionen
 */

const { FILTERS } = require('../../../configs/constants');

const buildTradeLogsQuery = (reducemessage = false) => {
    return {
        sql: reducemessage ?
            'SELECT id, strategy, type, mode, cmd, symbol, `create`, timeframe, trade_volume FROM trades WHERE symbol <> \'BITCOIN\' AND type NOT IN (3,4) AND MODE NOT IN (\'E: BES Exists:0\', \'E:NO-Modification\') AND ((refID = ?) OR (refID = ?)) AND `create` > DATE_SUB(CURDATE(), INTERVAL ? DAY) ORDER BY `create` DESC LIMIT ?' :
            `SELECT * FROM trades WHERE symbol <> '${FILTERS.EXCLUDED_SYMBOLS[0]}' AND type NOT IN (${FILTERS.EXCLUDED_TYPES.join(',')}) AND MODE NOT IN ('${FILTERS.EXCLUDED_MODES.join("','")}') AND ((refID = ?) OR (refID = ?)) AND \`create\` > DATE_SUB(CURDATE(), INTERVAL ? DAY) ORDER BY \`create\` DESC LIMIT ?`,
        bigIntAsNumber: true,
        timezone: 'de_de'
    };
};

module.exports = {
    buildTradeLogsQuery
};
