const db = require('../../../configs/config_db');
const { LOG_LEVELS, log } = require('../../../services/logging_service');

exports.getAiDailySummaryVars = async (req, res) => {
    try {
        const requestedDate = req.query.date ? new Date(req.query.date) : new Date();
        const dateStr = requestedDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        
        // Get basic trade statistics for the day
        const statsQuery = `
            SELECT 
                COUNT(*) as tradeCount,
                SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as winCount,
                SUM(CASE WHEN profit <= 0 THEN 1 ELSE 0 END) as lossCount,
                SUM(profit) as profit,
                ROUND(SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0) * 100, 2) as winRate
            FROM trades_history
            WHERE DATE(trading_day) = ?
        `;
        
        // Get best and worst trades of the day
        const bestTradeQuery = `
            SELECT symbol, profit, strategy
            FROM trades_history
            WHERE DATE(trading_day) = ?
            ORDER BY profit DESC
            LIMIT 1
        `;
        
        const worstTradeQuery = `
            SELECT symbol, profit, strategy
            FROM trades_history
            WHERE DATE(trading_day) = ?
            ORDER BY profit ASC
            LIMIT 1
        `;
        
        // Get strategy performance for the day
        const strategyQuery = `
            SELECT 
                strategy,
                COUNT(*) as tradeCount,
                SUM(profit) as profit,
                ROUND(SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0) * 100, 2) as winRate
            FROM trades_history
            WHERE DATE(trading_day) = ?
            GROUP BY strategy
            ORDER BY SUM(profit) DESC
        `;
        
        // Get market conditions for the day
        const marketConditionsQuery = `
            SELECT 
                type, 
                value, 
                confidence
            FROM market_conditions
            WHERE DATE(timestamp) = ?
        `;
        
        // Execute all queries
        const [statsRows] = await db.pool.query(statsQuery, [dateStr]);
        const [bestTradeRows] = await db.pool.query(bestTradeQuery, [dateStr]);
        const [worstTradeRows] = await db.pool.query(worstTradeQuery, [dateStr]);
        const [strategyRows] = await db.pool.query(strategyQuery, [dateStr]);
        const [marketConditionRows] = await db.pool.query(marketConditionsQuery, [dateStr]);
        
        // Process market conditions into a structured object
        const marketConditions = {};
        marketConditionRows.forEach(row => {
            marketConditions[row.type] = {
                value: row.value,
                confidence: row.confidence
            };
        });
        
        const result = {
            date: dateStr,
            ...statsRows[0],
            bestTrade: bestTradeRows[0] || null,
            worstTrade: worstTradeRows[0] || null,
            marketConditions,
            tradingStrategies: strategyRows
        };
        
        res.json(result);

    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getAiDailySummaryVars', 'Error retrieving AI daily summary variables', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: 'Database error when retrieving AI daily summary variables' });
    }
};

exports.getAiDailySummary = async (req, res) => {
    try {
        const requestedDate = req.query.date ? new Date(req.query.date) : new Date();
        const dateStr = requestedDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        const regenerate = req.query.regenerate === 'true' || req.query.regenerate === true;
        
        // Check if summary already exists for this date
        const checkQuery = `
            SELECT 
                id, date, summary, insights, key_metrics as keyMetrics, 
                recommendations, generated_at as generatedAt
            FROM ai_daily_summaries
            WHERE date = ?
        `;
        
        const [existingRows] = await db.pool.query(checkQuery, [dateStr]);
        
        // If summary exists and no regeneration requested, return it
        if (existingRows.length > 0 && !regenerate) {
            const result = existingRows[0];
            
            // Parse JSON fields
            try { result.insights = JSON.parse(result.insights); } catch (e) { result.insights = []; }
            try { result.keyMetrics = JSON.parse(result.keyMetrics); } catch (e) { result.keyMetrics = {}; }
            try { result.recommendations = JSON.parse(result.recommendations); } catch (e) { result.recommendations = []; }
            
            return res.json(result);
        }
        
        // If we need to regenerate or no summary exists, call the AI service
        // Here we would typically call an external AI service, but for now we'll return a placeholder
        
        // Get the summary variables first
        const summaryVarsUrl = `${req.protocol}://${req.get('host')}/api/v1/ai_daily_summary_vars?date=${dateStr}`;
        
        try {
            // In a real implementation, we would use these variables to call an AI service
            // For now, we'll just generate a placeholder summary
            
            const placeholder = {
                summary: `Trading summary for ${dateStr}. This is a placeholder that would be generated by an AI service.`,
                insights: [
                    { type: 'performance', content: 'Overall performance analysis would go here.' },
                    { type: 'strategy', content: 'Strategy-specific insights would go here.' }
                ],
                keyMetrics: {
                    profitability: 'Profit metrics would go here',
                    efficiency: 'Trading efficiency metrics would go here'
                },
                recommendations: [
                    'Recommendation 1 would go here',
                    'Recommendation 2 would go here'
                ]
            };
            
            // Store the generated summary in the database
            const insertQuery = `
                INSERT INTO ai_daily_summaries 
                    (date, summary, insights, key_metrics, recommendations, generated_at)
                VALUES (?, ?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                    summary = VALUES(summary),
                    insights = VALUES(insights),
                    key_metrics = VALUES(key_metrics),
                    recommendations = VALUES(recommendations),
                    generated_at = NOW()
            `;
            
            const params = [
                dateStr,
                placeholder.summary,
                JSON.stringify(placeholder.insights),
                JSON.stringify(placeholder.keyMetrics),
                JSON.stringify(placeholder.recommendations)
            ];
            
            const [insertResult] = await db.pool.query(insertQuery, params);
            
            const result = {
                id: insertResult.insertId || existingRows[0]?.id,
                date: dateStr,
                ...placeholder,
                generatedAt: new Date().toISOString()
            };
            
            res.json(result);
            
        } catch (aiError) {
            log(LOG_LEVELS.ERROR, 'getAiDailySummary', 'Error generating AI summary', {
                error: aiError.message
            });
            res.status(500).json({ error: 'Error generating AI summary' });
        }

    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getAiDailySummary', 'Error retrieving AI daily summary', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: 'Database error when retrieving AI daily summary' });
    }
};
