{"paths": {"/api/v1/db/mirrorTradingSettings": {"get": {"summary": "Mirror-Trading-Einstellungen abrufen", "description": "Ruft die Konfigurationseinstellungen für das Mirror Trading zwischen einem Quell- und einem Zielkonto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält alle Konfigurationsparameter für das Mirror Trading\n- Validiert die Existenz beider Konten\n\nAnwendungsfälle:\n- Anzeige der aktuellen Mirror-Trading-Konfiguration im Dashboard\n- Überprüfung des Mirror-Trading-Status\n- Anzeige der Volumen-Faktoren und Limits\n- Überprüfung der gespiegelten und ausgeschlossenen Symbole", "tags": ["Mirror Trading"], "parameters": [{"in": "query", "name": "targetRefID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Zielkontos", "example": "IG-P1"}, {"in": "query", "name": "sourceRefID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Quellkontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Mirror-Trading-Einstellungen", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MirrorTradingSettings"}}, "example": [{"source_account_id": 1, "source_refId": "IG-D1", "target_account_id": 2, "target_refId": "IG-P1", "mirror_active": true, "volume_factor": 0.5, "max_volume": 5.0, "min_volume": 0.1, "mirror_since": "2023-04-01T10:00:00Z", "mirror_symbols": "EURUSD,GBPUSD,USDJPY", "exclude_symbols": "USDCAD,AUDUSD"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/mirrorTradingLogs": {"get": {"summary": "Mirror-Trading-Logs abrufen", "description": "Ruft die Protokolleinträge für das Mirror Trading zwischen einem Quell- und einem Zielkonto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Informationen zu jedem gespiegelten Trade\n- Sortiert nach Zeitstempel (neueste zu<PERSON>t)\n- Limitierbar auf eine bestimmte Anzahl von Einträgen\n\nAnwendungsfälle:\n- Überwachung der Mirror-Trading-Aktivitäten\n- Fehleranalyse bei fehlgeschlagenen Mirror-Trades\n- Überprüfung der korrekten Ausführung von Trades\n- Historische Analyse der Mirror-Trading-Performance", "tags": ["Mirror Trading"], "parameters": [{"in": "query", "name": "targetRefID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Zielkontos", "example": "IG-P1"}, {"in": "query", "name": "sourceRefID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Quellkontos", "example": "IG-D1"}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 1000}, "description": "Maximale Anzahl der zurückzugebenden Einträge", "example": 10}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Mirror-Trading-Logs", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MirrorTradingLog"}}, "example": [{"log_id": 12345, "timestamp": "2023-04-15T14:30:00Z", "source_refId": "IG-D1", "target_refId": "IG-P1", "action": "OPEN_TRADE", "symbol": "EURUSD", "source_volume": 2.0, "target_volume": 1.0, "source_direction": "BUY", "target_direction": "BUY", "source_price": 1.0865, "target_price": 1.0867, "status": "SUCCESS", "error_message": null}, {"log_id": 12344, "timestamp": "2023-04-15T13:45:00Z", "source_refId": "IG-D1", "target_refId": "IG-P1", "action": "CLOSE_TRADE", "symbol": "GBPUSD", "source_volume": 1.5, "target_volume": 0.75, "source_direction": "SELL", "target_direction": "SELL", "source_price": 1.245, "target_price": 1.2448, "status": "SUCCESS", "error_message": null}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}