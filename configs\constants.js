// Time Constants
const TIME_CONSTANTS = {
    DEFAULT_DAYS: {
        TRADE_HISTORY: 61,
        TRADE_HISTORY_SYMBOL: 3,
        CALENDAR_PAST: 7,
        CALENDAR_FUTURE: 7,
        TRADE_LOGS: 365,
        NEWS: 3,
        PIVOT_POINTS: 3000
    },
    DATES: {
        EQUITY_START: '2023-07-01'
    }
};

// Query Limits
const QUERY_LIMITS = {
    DEFAULT_TRADE_LOGS: 400,
    DEFAULT_NEWS: 50,
    DEFAULT_FACTOR_MAP: 1,
    MAX_TRADES: 5000
};

// Filter Constants
const FILTERS = {
    EXCLUDED_SYMBOLS: ['BITCOIN'],
    EXCLUDED_TYPES: [3, 4],
    EXCLUDED_MODES: ['E: BES Exists:0', 'E:NO-Modification'],
    ALLOWED_COUNTRIES: ['EU', 'US', 'DE', 'CA', 'GB', 'CN'],
    MIN_IMPACT: 1
};

// Cache TTL Values
const CACHE_TTL = {
    DEFAULT: 20,
    TRADE_HISTORY: 20,
    CALENDAR: 20,
    ACCOUNT: 20
};

const LLM = {
    DEFAULT_MODEL : "claude-3-7-sonnet-********",
    OPENAI_GPT_4o_MINI : "gpt-4o-mini",
    MISTRAL_LARGE_LATEST : "mistral-large-latest",
    ANTHROPIC_CLAUDE_HAIKU : "claude-3-5-haiku-********",
    ANTHROPIC_CLAUDE_SONNET : "claude-3-5-sonnet-********",
    GOOGLE_GEMINI_2_5_PRO: "gemini-2.5-pro-preview-06-05",
    GOOGLE_GEMINI_2_5_FLASH_EXP: "gemini-2.5-flash-preview-05-20",
}

// FMP Config
const FMP_CONFIG = {
    API_BASE_URL: 'https://financialmodelingprep.com/api/',
    API_KEY: process.env.FMP_API_KEY,
    MAX_RETRIES: 3,
    RETRY_DELAY: 60,
    ENDPOINTS: {
        news: 'v4/general_news',
        calendar: 'v3/economic_calendar',
        stocks: 'stock/list',
        quote: 'v3/historical-chart',
        profile: 'profile'
    },
    CACHE_TTL: {
        news: 3600,        // 1 hour
        calendar: 7200,    // 2 hours
        stocks: 86400,     // 1 day
        quote: 300,        // 5 minutes
        profile: 86400     // 1 day
    }
};

module.exports = {
    TIME_CONSTANTS,
    QUERY_LIMITS,
    FILTERS,
    CACHE_TTL,
    LLM,
    FMP_CONFIG
};
