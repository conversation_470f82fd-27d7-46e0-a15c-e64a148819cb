{"components": {"schemas": {"PivotPoint": {"type": "object", "properties": {"pivot_id": {"type": "integer", "description": "Eindeutige ID des Pivot-Punkts", "example": 12345}, "symbol": {"type": "string", "description": "Trading-Symbol, für das der Pivot-Punkt gilt", "example": "EURUSD"}, "date": {"type": "string", "format": "date", "description": "Datum des Pivot-Punkts", "example": "2023-04-15"}, "timeframe": {"type": "string", "description": "Zeitrahmen des Pivot-Punkts (D1, W1, M1)", "enum": ["D1", "W1", "M1"], "example": "D1"}, "pivot": {"type": "number", "format": "float", "description": "Zentraler Pivot-Punkt", "example": 1.0865}, "r1": {"type": "number", "format": "float", "description": "Widerstandslevel 1", "example": 1.0892}, "r2": {"type": "number", "format": "float", "description": "Widerstandslevel 2", "example": 1.0918}, "r3": {"type": "number", "format": "float", "description": "Widerstandslevel 3", "example": 1.0945}, "s1": {"type": "number", "format": "float", "description": "Unterstützungslevel 1", "example": 1.0838}, "s2": {"type": "number", "format": "float", "description": "Unterstützungslevel 2", "example": 1.0812}, "s3": {"type": "number", "format": "float", "description": "Unterstützungslevel 3", "example": 1.0785}, "high": {"type": "number", "format": "float", "description": "Höchstkurs der vorherigen Periode", "example": 1.0925}, "low": {"type": "number", "format": "float", "description": "Tiefstkurs der vorherigen Periode", "example": 1.0805}, "close": {"type": "number", "format": "float", "description": "Schlusskurs der vorherigen Periode", "example": 1.0865}, "calculation_method": {"type": "string", "description": "Verwendete Berechnungsmethode", "enum": ["Standard", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "example": "Standard"}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Invalid days parameter"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getPivotpoints"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Invalid days parameter", "function": "getPivotpoints"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch pivot points", "function": "getPivotpoints"}}}}}}}