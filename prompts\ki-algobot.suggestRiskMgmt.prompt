You are tasked with analyzing market conditions and generating a JSON output with recommendations for various assets. Your analysis should be based on current market factors and the latest news information provided.

First, review the current market factors:

<factor-map>
{{FACTOR_MAP}}
</factor-map>

Next, consider the latest news summary:

<gpt-day-summary>
{{GPT_DAY_SUMMARY}}
</gpt-day-summary>

Next, consider the latest volatily-values of assets:
<volatiliy>
{{VOLATILITY}}
</volatiliy>

Analyze this information carefully, paying attention to how these factors and news items might affect different assets. Consider both short-term and long-term implications for each asset.

Generate a JSON output with the following structure:

1. An "assets" array containing objects for each of the following assets (if relevant): US100, US500, US2000, US30, UK100, DE40, BITCOIN, GOLD, SILVER.

2. For each asset, include:
   - "symbol": The asset's symbol (e.g., "US100")
   - "name": The full name of the asset (e.g., "Nasdaq 100")
   - "type": The asset type (e.g., "index", "cryptocurrency", "commodity")
   - "riskMode": Choose from "low-risk", "standard", or "high-risk"
   - "marketSituation": Choose from "short", "neutral", "wait", or "long"
   - "reasoning": A brief explanation for your assessment in German!

3. A "metadata" object containing:
   - "riskModes": An array of possible risk modes
   - "marketSituations": An array of possible market situations
   - "lastUpdate": The current date and time in ISO 8601 format

When determining the risk mode:
- "low-risk" indicates uncertainty or sideways market conditions
- "standard" is for normal market conditions
- "high-risk" suggests a clear market direction, suitable for larger trading volumes

For market situation:
- "short" indicates a recommendation to sell or short the asset
- "neutral" suggests no clear direction
- "wait" advises holding off on trading
- "long" recommends buying or going long on the asset

Provide concise but informative reasoning for each asset, explaining how the current factors and news affect your assessment. 
Your output should be a valid JSON object containing only the described structure. Do not include any additional text or explanations outside of the JSON object.