const { executeQuery } = require('../../../services/database_service');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../../services/cache_service');
const { LOG_LEVELS, log, logPerformance } = require('../../../services/logging_service');
const { DatabaseError, ValidationError, errorHandler } = require('../errors/database_errors');
const { validateNumericParam, validateStringParam } = require('../../../services/validation_service');

class CurrentPeriodController {
    /**
     * @swagger
     * /api/v1/statistics/day:
     *   get:
     *     summary: Get daily statistics
     *     description: |
     *       Retrieves trading statistics aggregated by day for a specific account.
     *       Provides daily performance metrics including:
     *       
     *       - Minimum and maximum profit per day
     *       - Number of trades executed
     *       - Total profit/loss
     *       
     *       This data is useful for:
     *       - Analyzing daily trading performance
     *       - Identifying profitable/unprofitable days
     *       - Tracking trading frequency
     *       - Monitoring profit consistency
     *     tags: [Statistics]
     *     parameters:
     *       - in: query
     *         name: refID
     *         required: true
     *         schema:
     *           type: string
     *           enum: [P1, P2, D1, D2, SimD1, SimD2, SimD3]
     *         description: Account reference ID
     *       - in: query
     *         name: startDate
     *         schema:
     *           type: string
     *           format: date
     *         description: Start date for statistics (defaults to 14 days ago)
     *     responses:
     *       200:
     *         description: Daily trading statistics retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: array
     *               items:
     *                 type: object
     *                 properties:
     *                   date:
     *                     type: string
     *                     format: date
     *                     description: Trading day
     *                   min_profit:
     *                     type: number
     *                     description: Minimum profit achieved that day
     *                     example: -50.5
     *                   max_profit:
     *                     type: number
     *                     description: Maximum profit achieved that day
     *                     example: 120.75
     *                   cnt_trades:
     *                     type: integer
     *                     description: Number of trades executed
     *                     example: 15
     *                   sum_profit:
     *                     type: number
     *                     description: Total profit/loss for the day
     *                     example: 350.25
     *       400:
     *         description: Invalid parameters provided
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 error:
     *                   type: string
     *                   example: refID is required
     *       500:
     *         description: Database or server error
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 error:
     *                   type: string
     *                   example: Failed to retrieve daily statistics
     */
    async getCurrentDayStatistics(req, res) {
        try {
            const { refID } = req.query;
            const result = await getCurrentDayStatisticsIndependent(refID);
            res.send(result);
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getCurrentDayStatistics', 'Failed to get current day statistics', { error: err });
            const errorResponse = errorHandler(err, 'getCurrentDayStatistics');
            res.status(errorResponse.status).json(errorResponse);
        }
    }

    /**
     * @swagger
     * /api/v1/statistics/week:
     *   get:
     *     summary: Get weekly statistics
     *     description: |
     *       Retrieves trading statistics aggregated by week for a specific account.
     *       Provides weekly performance metrics including:
     *       
     *       - Minimum and maximum profit per week
     *       - Number of trades executed
     *       - Total profit/loss
     *       
     *       This data is useful for:
     *       - Analyzing weekly trading patterns
     *       - Identifying profitable/unprofitable weeks
     *       - Tracking trading consistency
     *       - Monitoring weekly performance trends
     *     tags: [Statistics]
     *     parameters:
     *       - in: query
     *         name: refID
     *         required: true
     *         schema:
     *           type: string
     *           enum: [P1, P2, D1, D2, SimD1, SimD2, SimD3]
     *         description: Account reference ID
     *     responses:
     *       200:
     *         description: Weekly trading statistics retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: array
     *               items:
     *                 type: object
     *                 properties:
     *                   year:
     *                     type: integer
     *                     description: Year of the trading week
     *                     example: 2024
     *                   week:
     *                     type: integer
     *                     description: Week number (1-52)
     *                     example: 2
     *                   min_profit:
     *                     type: number
     *                     description: Minimum profit achieved that week
     *                     example: -150.5
     *                   max_profit:
     *                     type: number
     *                     description: Maximum profit achieved that week
     *                     example: 320.75
     *                   cnt_trades:
     *                     type: integer
     *                     description: Number of trades executed
     *                     example: 45
     *                   sum_profit:
     *                     type: number
     *                     description: Total profit/loss for the week
     *                     example: 850.25
     *       400:
     *         description: Invalid parameters provided
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 error:
     *                   type: string
     *                   example: refID is required
     *       500:
     *         description: Database or server error
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 error:
     *                   type: string
     *                   example: Failed to retrieve weekly statistics
     */
    async getCurrentWeekStatistics(req, res) {
        const startTime = process.hrtime.bigint();
        try {
            const { refID } = req.query;

            if (!refID) {
                throw new ValidationError('refID is required');
            }

            const validatedRefID = validateStringParam(refID, {
                required: true,
                minLength: 2,
                maxLength: 50,
                paramName: 'refID'
            });


            log(LOG_LEVELS.INFO, 'getCurrentWeekStatistics', 'Starting current week statistics retrieval', {
                refID: validatedRefID
            });
            return await withCacheWrapper(
                'TRADE',
                'getCurrentWeekStatistics',
                async () => {
                    const query = {
                        sql: 'SELECT YEAR(t.exit_time) AS year, WEEKOFYEAR(t.exit_time) AS week, ' +
                            'ROUND(MIN(profit),0) AS min_profit, ROUND(MAX(profit),0) AS max_profit, ' +
                            'COUNT(*) AS cnt_trades, ROUND(SUM(profit),0) AS sum_profit ' +
                            'FROM `trades_history` t ' +
                            'WHERE t.refId=? ' +
                            'AND YEAR(t.exit_time)=YEAR(NOW()) ' +
                            'AND WEEKOFYEAR(t.exit_time)=WEEKOFYEAR(NOW())',
                        bigIntAsNumber: true
                    };
                    const result = await executeQuery(query, [refID]);
                    res.send(result);
                },
                [refID]
            );
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getCurrentWeekStatistics', 'Failed to get current week statistics', { error: err });
            const errorResponse = errorHandler(err, 'getCurrentWeekStatistics');
            res.status(errorResponse.status).json(errorResponse);
        } finally {
            logPerformance('getCurrentWeekStatistics', startTime);
        }
    }

    
    async getCurrentMonthStatistics(req, res) {
        const startTime = process.hrtime.bigint();
        try {
            const { refID } = req.query;

            if (!refID) {
                throw new ValidationError('refID is required');
            }

            const validatedRefID = validateStringParam(refID, {
                required: true,
                minLength: 2,
                maxLength: 50,
                paramName: 'refID'
            });


            log(LOG_LEVELS.INFO, 'getCurrentMonthStatistics', 'Starting current month statistics retrieval', {
                refID: validatedRefID
            });
            return await withCacheWrapper(
                'TRADE',
                'getCurrentMonthStatistics',
                async () => {
                    const query = {
                        sql: 'SELECT YEAR(t.exit_time) AS year, month(t.exit_time) AS month, ' +
                            'ROUND(MIN(profit),0) AS min_profit, ROUND(MAX(profit),0) AS max_profit, ' +
                            'COUNT(*) AS cnt_trades, ROUND(SUM(profit),0) AS sum_profit ' +
                            'FROM `trades_history` t, `accounts` a ' +
                            'WHERE t.account=a.account_id AND a.refId=? ' +
                            'AND month(t.exit_time)=month(NOW()) ' +
                            'AND year(t.exit_time)=year(NOW()) ' +
                            'ORDER BY YEAR DESC, month DESC',
                        bigIntAsNumber: true
                    };

                    const result = await executeQuery(query, [refID]);
                    res.send(result);
                },
                [refID]
            );
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getCurrentMonthStatistics', 'Failed to get current month statistics', { error: err });
            const errorResponse = errorHandler(err, 'getCurrentMonthStatistics');
            res.status(errorResponse.status).json(errorResponse);
        } finally {
            logPerformance('getCurrentMonthStatistics', startTime);
        }
    }

    /**
     * Lädt Statistiken des aktuellen Jahres
     */
    async getCurrentYearStatistics(req, res) {
        const startTime = process.hrtime.bigint();
        try {
            const { refID } = req.query;

            if (!refID) {
                throw new ValidationError('refID is required');
            }

            const validatedRefID = validateStringParam(refID, {
                required: true,
                minLength: 2,
                maxLength: 50,
                paramName: 'refID'
            });


            log(LOG_LEVELS.INFO, 'getCurrentYearStatistics', 'Starting current year statistics retrieval', {
                refID: validatedRefID
            });
            return await withCacheWrapper(
                'TRADE',
                'getCurrentYearStatistics',
                async () => {
                    const query = {
                        sql: 'SELECT YEAR(t.exit_time) AS year, ' +
                            'ROUND(MIN(profit),0) AS min_profit, ROUND(MAX(profit),0) AS max_profit, ' +
                            'COUNT(*) AS cnt_trades, ROUND(SUM(profit),0) AS sum_profit ' +
                            'FROM `trades_history` t, `accounts` a ' +
                            'WHERE t.account=a.account_id AND a.refId=? ' +
                            'AND t.exit_time>\'2023-07-01\' ' +
                            'AND year(t.exit_time)=year(NOW())',
                        bigIntAsNumber: true
                    };

                    const result = await executeQuery(query, [refID]);
                    res.send(result);
                },
                [refID]
            );
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getCurrentYearStatistics', 'Failed to get current year statistics', { error: err });
            const errorResponse = errorHandler(err, 'getCurrentYearStatistics');
            res.status(errorResponse.status).json(errorResponse);
        } finally {
            logPerformance('getCurrentYearStatistics', startTime);
        }
    }

}

/**
 * Lädt Statistiken des aktuellen Tages unabhängig von Request/Response
 */
async function getCurrentDayStatisticsIndependent(refID) {
    const startTime = process.hrtime.bigint();
    try {
        if (!refID) {
            throw new ValidationError('refID is required');
        }

        log(LOG_LEVELS.INFO, 'getCurrentDayStatisticsIndependent', 'Starting current day statistics retrieval', {
            refID
        });

        return await withCacheWrapper(
            'TRADE',
            'getCurrentDayStatistics',
            async () => {
                const query = {
                    sql: `SELECT nvl(YEAR(t.exit_time), year(now())) AS YEAR, nvl(MONTH(t.exit_time), month(now())) AS MONTH, nvl(DATE(t.exit_time), date(now())) AS DATE, nvl(ROUND(MIN(profit),0),0) AS min_profit, nvl(ROUND(MAX(profit),0),0) AS max_profit, COUNT(*) AS cnt_trades, nvl(ROUND(SUM(profit),0),0) AS sum_profit FROM \`trades_history\` t, \`accounts\` a WHERE t.account=a.account_id AND a.refId=? AND DATE(t.exit_time)=current_date()`,
                    bigIntAsNumber: true
                };

                return await executeQuery(query, [refID]);
            },
            [refID]
        );
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getCurrentDayStatisticsIndependent', 'Failed to get current day statistics', { error: err });
        throw err;
    } finally {
        logPerformance('getCurrentDayStatisticsIndependent', startTime);
    }
}

const controller = new CurrentPeriodController();
module.exports = controller;
module.exports.getCurrentDayStatisticsIndependent = getCurrentDayStatisticsIndependent;
