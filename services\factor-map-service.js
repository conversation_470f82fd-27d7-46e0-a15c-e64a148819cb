const { requestLLM } = require('./llm-service');
const databaseHelper = require('../controllers/gpt/controller_gpt_databaseHelper');
const {LLM} = require("../configs/constants");
const { 
    getMergedSymbolTeaserStructured,
    getOrCreateDatabaseEntry,
    shouldUpdateSummary,
    getUpdatedSummaryObject
} = require('./news-summary-service');


async function refreshFactorMap(limitUsedGPTSummaries = 5, articleLimit = 10, refreshNews = false) {
    if (process.env.NODE_ENV !== 'production')
        console.log("[factor-map-service] Starting refresh:", { limitUsedGPTSummaries, articleLimit, refreshNews });

/*     if (refreshNews) {
        const xtbController = require('../controllers/ig/controller_ig_post');
        await xtbController.xtb_store2database_news({ query: { minus_days: 1 } }, { send: () => {} });
        await doUpdateKISummaryAndStoreInDatabase(articleLimit);
    }
 */
    const processedSummaries = await databaseHelper.getLatestDaySummariesForAI(limitUsedGPTSummaries);
    
    const factorMapOutput = await requestLLM([], LLM.GOOGLE_GEMINI_2_5_PRO, null,
        "ki-algobot.analyze_influencing_factors", 2, { "NACHRICHTEN": processedSummaries });
    
    const scoreOutput = await requestLLM([], LLM.GOOGLE_GEMINI_2_5_PRO, null,
        "ki-algobot.analyse_market_with_factors", 2, { "MARKET_DATA": factorMapOutput });
    
    return await databaseHelper.createFactorMapEntry(factorMapOutput, 2, scoreOutput);
}

async function doUpdateKISummaryAndStoreInDatabase(articleLimit) {
    const structuredSummaries = await getMergedSymbolTeaserStructured(articleLimit, 150000);
    const key = `GPTDaySummary_${new Date().toDateString()}`;
    
    let result = await getOrCreateDatabaseEntry({ key, time: new Date(), body: structuredSummaries });
    
    if (shouldUpdateSummary(result, { body: structuredSummaries }, true)) {
        await getUpdatedSummaryObject(result, { body: structuredSummaries }, -1);
        result.gpt_summarize_date = new Date();
    }
    
    result.optimized_textbody = structuredSummaries;
    await databaseHelper.database_update(result);
    return result;
}

module.exports = {
    refreshFactorMap
};
