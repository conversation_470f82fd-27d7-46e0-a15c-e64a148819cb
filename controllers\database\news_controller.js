const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { TIME_CONSTANTS, QUERY_LIMITS } = require('../../configs/constants');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam, validateBooleanParam, validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');
const { buildNewsQuery } = require('./queries/news_queries');
const { buildLatestFactorMapQuery } = require('./queries/factor_map_queries');

async function getNewsFromDBIndependent(limit = 1000, days = 30, onlyUnread = null, onlyBookmarked = null, searchKeywords = null) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getNewsFromDBIndependent', 'Starting news retrieval', {
            limit,
            days,
            onlyUnread,
            onlyBookmarked,
            searchKeywords
        });

        // Validate limit
        limit = validateNumericParam(limit, {
            defaultValue: QUERY_LIMITS.DEFAULT_NEWS,
            min: 1,
            max: 1000,
            paramName: 'limit'
        });

        // Validate days
        days = validateNumericParam(days, {
            defaultValue: TIME_CONSTANTS.DEFAULT_DAYS.NEWS,
            required: true,
            min: 0,
            max: 365,
            paramName: 'days'
        });

        // Validate onlyUnread (optional boolean)
        onlyUnread = validateBooleanParam(onlyUnread, {
            required: false,
            defaultValue: null,
            paramName: 'onlyUnread'
        });

        // Validate onlyBookmarked (optional boolean)
        onlyBookmarked = validateBooleanParam(onlyBookmarked, {
            required: false,
            defaultValue: null,
            paramName: 'onlyBookmarked'
        });

        // Validate searchKeywords (optional string)
        searchKeywords = validateStringParam(searchKeywords, {
            required: false,
            defaultValue: null,
            maxLength: 500,
            paramName: 'searchKeywords'
        });

        log(LOG_LEVELS.DEBUG, 'getNewsFromDBIndependent', 'Executing query', {
            limit,
            days,
            onlyUnread,
            onlyBookmarked,
            searchKeywords
        });

        const query = buildNewsQuery(days, limit, onlyUnread, onlyBookmarked, searchKeywords);
        console.log('Executing query:', query);
        const result = await executeQuery(query);
        
        log(LOG_LEVELS.INFO, 'getNewsFromDBIndependent', 'Successfully retrieved news', {
            days,
            limit,
            onlyUnread,
            onlyBookmarked,
            searchKeywords,
            resultCount: result?result.length:0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getNewsFromDBIndependent', 'Failed to fetch news', {
            error: err.message,
            stack: err.stack,
            days,
            limit,
            onlyUnread,
            onlyBookmarked,
            searchKeywords
        });
        throw err;
    } finally {
        logPerformance('getNewsFromDBIndependent', startTime);
    }
}

async function getNewsFromDB(req, res) {
    try {
        const p_limit = parseInt(req.query.limit);
        const p_days = parseInt(req.query.days);
        const p_onlyUnread = req.query.onlyUnread;
        const p_onlyBookmarked = req.query.onlyBookmarked;
        const p_searchKeywords = req.query.searchKeywords;

        const result = await withCacheWrapper(
            'NEWS',
            'getNewsFromDB',
            () => getNewsFromDBIndependent(p_limit, p_days, p_onlyUnread, p_onlyBookmarked, p_searchKeywords),
            [p_limit, p_days, p_onlyUnread, p_onlyBookmarked, p_searchKeywords]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getNewsFromDB');
        res.status(errorResponse.status).json(errorResponse);
    }
}

async function getLatestFactorMapIndependent(limit = 1) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getLatestFactorMapIndependent', 'Starting factor map retrieval', {
            limit
        });

        // Validate limit
        limit = validateNumericParam(limit, {
            defaultValue: QUERY_LIMITS.DEFAULT_FACTOR_MAP,
            min: 1,
            max: 100,
            paramName: 'limit'
        });

        log(LOG_LEVELS.DEBUG, 'getLatestFactorMapIndependent', 'Executing query', {
            limit
        });

        const query = buildLatestFactorMapQuery();
        const result = await executeQuery(query, [limit]);
        
        log(LOG_LEVELS.INFO, 'getLatestFactorMapIndependent', 'Successfully retrieved factor map', {
            limit,
            resultCount: result?result.length:0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getLatestFactorMapIndependent', 'Failed to fetch factor map', {
            error: err.message,
            stack: err.stack,
            limit
        });
        throw err;
    } finally {
        logPerformance('getLatestFactorMapIndependent', startTime);
    }
}

async function getLatestFactorMap(req, res) {
    try {
        let p_limit = req.query.limit ? parseInt(req.query.limit) : 1;
        const result = await withCacheWrapper(
            'NEWS',
            'getLatestFactorMap',
            () => getLatestFactorMapIndependent(p_limit),
            [p_limit]
        );
        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getLatestFactorMap');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getNewsFromDB,
    getNewsFromDBIndependent,
    getLatestFactorMap,
    getLatestFactorMapIndependent
};
