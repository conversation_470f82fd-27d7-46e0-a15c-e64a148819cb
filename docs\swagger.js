const swaggerJsdoc = require('swagger-jsdoc');
const fs = require('fs');
const path = require('path');

// Lade die ausgelagerten Swagger-Definitionen
const loadSwaggerDefinition = (fileName) => {
  try {
    const filePath = path.join(__dirname, 'swagger-definitions', fileName);
    return JSON.parse(fs.readFileSync(filePath, 'utf8'));
  } catch (error) {
    console.log(`Hinweis: ${fileName} nicht gefunden oder ungültig.`);
    // Rückgabe eines leeren Objekts mit der erwarteten Struktur
    return { components: {}, paths: {} };
  }
};

// Basis-Konfiguration
const options = {
  definition: {
    openapi: '3.0.0', 
    info: {
      title: 'ML Algotrader API',
      version: '2.0.0',
      description: 'REST API for algorithmic trading with IG Markets and integrated AI/ML capabilities',
      contact: {
        name: 'API Support',
        url: 'https://app.ml-algotrader.com',
      },
      license: {
        name: 'Private License',
      },
    },
    servers: [
      {
        url: 'https://api.ml-algotrader.com',
        description: 'Production server',
      },
      {
        url: 'http://localhost:8080',
        description: 'Development server',
      },
    ],
    tags: [
      {
        name: 'IG Trading',
        description: 'Execute and manage trades on IG Markets platform'
      },
      {
        name: 'IG Market Data',
        description: 'Market data and price information from IG Markets platform'
      },
      {
        name: 'Statistics',
        description: 'Trading performance and analysis statistics'
      },
      {
        name: 'AI Integration',
        description: 'AI-powered market analysis and predictions'
      },
      {
        name: 'Mirror Trading',
        description: 'Copy trading configuration and monitoring'
      },         
      {
        name: 'Chart Data',
        description: 'Historical OHLCV data for technical analysis'
      },
      {
        name: 'Trade History',
        description: 'Trading history and performance data'
      },
      {
        name: 'Financial Data',
        description: 'Financial Modeling Prep (FMP) market data and financial information'
      }
    ],
    components: {
      securitySchemes: {
        ApiKeyAuth: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-KEY',
          description: 'API key authentication'
        }
      } 
    },
    security: [{
      ApiKeyAuth: []
    }]
  },
  apis: ['./controllers/**/*.js'] // Pfad zu den API-Route-Definitionen
};

// Lade und integriere die ausgelagerten Definitionen
try {
  // Lade die Basis-Komponenten
  const aiComponents = loadSwaggerDefinition('ai-components.json');
  const fmpComponents = loadSwaggerDefinition('fmp-components.json');
  
  // Lade die Basis-Endpunkte
  const aiEndpoints = loadSwaggerDefinition('ai-endpoints.json');
  const fmpEndpoints = loadSwaggerDefinition('fmp-endpoints.json');
  
  // Definiere die Datenbankcontroller
  const databaseControllers = [
    'account',
    'ai-predictions',
    'calendar',
    'chart',
    'day-statistics',
    'mirror-trading',
    'month-statistics',
    'news',
    'pivot',
    'risk-management',
    'symbol',
    'symbol-setup',
    'trade-history',
    'trade-logs',
    'week-statistics'
  ];
  
  // Definiere die IG-Controller und ihre Definitionsdateien
  const igControllers = [
    {
      name: 'trading-get',
      componentFile: 'ig-trading-trades-and-accounts-components.json',
      endpointFile: 'ig-trading-trades-and-accounts-endpoints.json'
    },
    {
      name: 'trading',
      componentFile: 'ig-trading-trades-components.json',
      endpointFile: 'ig-trading-trades-endpoints.json'
    },
    {
      name: 'market-data',
      componentFile: 'ig-market-data-components.json',
      endpointFile: 'ig-market-data-endpoints.json'
    }
  ];
  
  // Initialisiere leere Objekte für die Zusammenführung
  let databaseComponents = { components: {} };
  let databaseEndpoints = { paths: {} };
  let igSpecificComponents = { components: {} };
  let igSpecificEndpoints = { paths: {} };
  
  // Lade und kombiniere alle Datenbankdefinitionen
  databaseControllers.forEach(controller => {
    try {
      const componentFile = `database-${controller}-components.json`;
      const endpointFile = `database-${controller}-endpoints.json`;
      
      // Lade die Komponenten
      const component = loadSwaggerDefinition(componentFile);
      if (component && component.components) {
        databaseComponents.components = {
          ...databaseComponents.components,
          ...component.components
        };
      }
      
      // Lade die Endpunkte
      const endpoint = loadSwaggerDefinition(endpointFile);
      if (endpoint && endpoint.paths) {
        databaseEndpoints.paths = {
          ...databaseEndpoints.paths,
          ...endpoint.paths
        };
      }
    } catch (controllerError) {
      console.log(`Fehler beim Laden der Definitionen für Controller ${controller}:`, controllerError);
    }
  });
  
  // Lade und kombiniere alle IG-spezifischen Definitionen
  igControllers.forEach(controller => {
    try {
      // Lade die Komponenten
      const component = loadSwaggerDefinition(controller.componentFile);
      if (component && component.components) {
        igSpecificComponents.components = {
          ...igSpecificComponents.components,
          ...component.components
        };
      }
      
      // Lade die Endpunkte
      const endpoint = loadSwaggerDefinition(controller.endpointFile);
      if (endpoint && endpoint.paths) {
        igSpecificEndpoints.paths = {
          ...igSpecificEndpoints.paths,
          ...endpoint.paths
        };
      }
    } catch (controllerError) {
      console.log(`Fehler beim Laden der Definitionen für IG-Controller ${controller.name}:`, controllerError);
    }
  });
  
  // Kombiniere die Komponenten
  options.definition.components = {
    ...options.definition.components,
    ...(aiComponents.components || {}),
    ...(fmpComponents.components || {}),
    ...databaseComponents.components,
    ...igSpecificComponents.components
  };

  // Kombiniere die Endpunkte
  options.definition.paths = {
    ...options.definition.paths,
    ...(aiEndpoints.paths || {}),
    ...(fmpEndpoints.paths || {}),
    ...databaseEndpoints.paths,
    ...igSpecificEndpoints.paths
  };
} catch (error) {
  console.error('Fehler beim Laden der Swagger-Definitionen:', error);
}

const swaggerSpec = swaggerJsdoc(options);

module.exports = swaggerSpec;
