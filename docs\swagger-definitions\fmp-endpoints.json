{"paths": {"/api/v1/fmp/calendar": {"get": {"summary": "Get economic calendar events", "description": "Retrieves economic calendar events for a specified date range.\n\nTechnical Details:\n- Date range defaults to current month if not specified\n- Maximum range of 30 days when only one date is provided\n- Returns events with their estimates and actual values\n- Data is sourced from Financial Modeling Prep API\n\nUse Cases:\n- Economic event monitoring\n- Market impact analysis\n- Trading decision support\n- Fundamental analysis", "tags": ["Financial Data"], "parameters": [{"in": "query", "name": "from", "schema": {"type": "string", "format": "date"}, "description": "Start date for calendar events (YYYY-MM-DD format)", "example": "2025-01-01"}, {"in": "query", "name": "to", "schema": {"type": "string", "format": "date"}, "description": "End date for calendar events (YYYY-MM-DD format)", "example": "2025-01-31"}], "responses": {"200": {"description": "Successfully retrieved calendar events", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "data": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "format": "date", "description": "Date of the economic event", "example": "2025-01-15"}, "event": {"type": "string", "description": "Name of the economic event", "example": "US CPI Data Release"}, "symbol": {"type": "string", "nullable": true, "description": "Related market symbol if applicable", "example": "USD"}, "time": {"type": "string", "nullable": true, "description": "Time of the event", "example": "08:30:00"}, "estimate": {"type": "string", "nullable": true, "description": "Estimated value for the event", "example": "0.3%"}, "actual": {"type": "string", "nullable": true, "description": "Actual reported value for the event", "example": "0.4%"}}}}, "count": {"type": "integer", "description": "Number of events returned", "example": 42}}}}}}, "500": {"description": "Server error occurred", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "error"}, "message": {"type": "string", "example": "Failed to fetch calendar data"}, "error": {"type": "string", "example": "External API connection timeout"}}}}}}}}}, "/api/v1/fmp/news": {"get": {"summary": "Get financial news articles", "description": "Retrieves financial news articles with pagination support.\n\nTechnical Details:\n- Paginated results with configurable page size\n- Default page size is 50 articles\n- Data is sourced from Financial Modeling Prep API\n- Returns full article content and metadata\n\nUse Cases:\n- Market news monitoring\n- Sentiment analysis\n- Event-driven trading\n- Research and analysis", "tags": ["Financial Data"], "parameters": [{"in": "query", "name": "size", "schema": {"type": "integer", "default": 50, "minimum": 1, "maximum": 1000}, "description": "Number of news articles to return per page", "example": 50}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 0, "minimum": 0}, "description": "Page number for pagination (0-based)", "example": 0}], "responses": {"200": {"description": "Successfully retrieved news articles"}, "500": {"description": "Server error occurred"}}}}, "/api/v1/fmp/chart": {"get": {"summary": "Get intraday price chart data", "description": "Retrieves intraday OHLCV (Open-High-Low-Close-Volume) chart data for a specific symbol and timeframe.", "tags": ["Financial Data"], "parameters": [{"in": "query", "name": "symbol", "schema": {"type": "string"}, "required": true, "description": "Trading symbol to retrieve chart data for", "example": "AAPL"}, {"in": "query", "name": "timeframe", "schema": {"type": "string", "enum": ["1min", "5min", "15min", "30min", "1hour", "4hour"]}, "required": true, "description": "Chart timeframe/interval", "example": "15min"}], "responses": {"200": {"description": "Successfully retrieved chart data"}, "400": {"description": "Invalid parameters provided"}, "500": {"description": "Server error occurred"}}}}}}