{"paths": {"/api/v1/db/calendar/list": {"get": {"summary": "Wirtschaftskalender abrufen", "description": "Ruft eine Liste von Wirtschaftskalendereinträgen für einen bestimmten Zeitraum ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Filtert nach Mindest-Impact-Level (konfigurierbar)\n- Filtert nach erlaubten Ländern (konfigurierbar)\n- Zeitstempel im deutschen Format\n\nAnwendungsfälle:\n- Anzeige des Wirtschaftskalenders im Trading-Dashboard\n- Planung von Trading-Aktivitäten um wichtige Wirtschaftsereignisse\n- Identifizierung von Hochvolatilitätsperioden\n- Risikomanagement durch Deaktivierung von Trades während wichtiger Ereignisse", "tags": ["Calendar"], "parameters": [{"in": "query", "name": "minus_days", "schema": {"type": "integer", "default": 7, "minimum": 0, "maximum": 365}, "description": "<PERSON>zahl der Tage in der Vergangenheit, für die Kalendereinträge abgerufen werden sollen", "example": 7}, {"in": "query", "name": "plus_days", "schema": {"type": "integer", "default": 14, "minimum": 0, "maximum": 365}, "description": "<PERSON>zahl der Tage in der Zukunft, für die Kalendereinträge abgerufen werden sollen", "example": 14}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Kalenderliste", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CalendarEntry"}}, "example": [{"id": 1436, "event_date": "2023-04-15T14:30:00Z", "country": "US", "impact": 3, "event_name": "Non-Farm Payrolls", "actual": "236K", "forecast": "240K", "previous": "311K", "currency": "USD", "is_today": false, "is_tomorrow": true, "toggle_deactivate_high_volume_trades": true}, {"id": 1437, "event_date": "2023-04-16T12:00:00Z", "country": "EU", "impact": 2, "event_name": "ECB Interest Rate Decision", "actual": null, "forecast": "3.5%", "previous": "3.5%", "currency": "EUR", "is_today": false, "is_tomorrow": false, "toggle_deactivate_high_volume_trades": false}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/calendar/delta_report": {"get": {"summary": "Delta-Report für Wirtschaftsereignisse abrufen", "description": "Ruft einen detaillierten Delta-Report ab, der die Preisänderungen eines bestimmten Symbols vor und nach einem Wirtschaftsereignis zeigt.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Berechnet Preisänderungen für verschiedene Zeitintervalle (1, 5, 15, 30, 60 Minuten)\n- Enthält Volatilitätsdaten vor und nach dem Ereignis\n\nAnwendungsfälle:\n- Analyse der Marktreaktion auf bestimmte Wirtschaftsereignisse\n- Entwicklung von Trading-Strategien basierend auf historischen Ereignisreaktionen\n- Risikobewertung für bestimmte Ereignistypen\n- Backtesting von ereignisbasierten Trading-Strategien", "tags": ["Calendar"], "parameters": [{"in": "query", "name": "calendar_id", "required": true, "schema": {"type": "string"}, "description": "ID des Kalendereintrags", "example": "1436"}, {"in": "query", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das der Delta-Report abgerufen werden soll", "example": "EURUSD"}], "responses": {"200": {"description": "Erfolgreiche Abfrage des Delta-Reports", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeltaReport"}}, "example": [{"calendar_id": 1436, "symbol": "EURUSD", "event_date": "2023-04-15T14:30:00Z", "event_name": "Non-Farm Payrolls", "delta_1min": 0.0012, "delta_5min": 0.0025, "delta_15min": 0.0018, "delta_30min": -0.0005, "delta_60min": -0.0022, "price_before": 1.0865, "price_after_1min": 1.0877, "price_after_60min": 1.0843, "volatility_before": 0.0003, "volatility_after": 0.0012}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}