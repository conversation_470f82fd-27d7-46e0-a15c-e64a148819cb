{"version": "2.0.0", "tasks": [{"type": "npm", "script": "start", "problemMatcher": [], "label": "npm: start", "detail": "node server.js"}, {"type": "npm", "script": "upload", "problemMatcher": [], "label": "npm: upload", "detail": "wsl -d Ubuntu rsync -avz -e 'ssh -i ~/.ssh/id_rsa_leon' --exclude-from='./.rsyncignore' --delete ./ <EMAIL>:/var/www/vhosts/ml-algotrader.com/api.ml-algotrader.com/"}, {"type": "npm", "script": "dev", "problemMatcher": [], "label": "npm: dev", "detail": "nodemon --exec 'npm start'"}, {"type": "npm", "script": "sync-local", "problemMatcher": [], "label": "npm: sync-local", "detail": "robocopy \".\" \"Z:\\www\\vhosts\\ml-algotrader.com\\api.ml-algotrader.com\" /MIR /W:1 /R:1 /MT:16 /XD node_modules .git .github .vscode coverage dist tests __tests__ /XF *.log .env* *.test.* *.spec.* .DS_Store /NFL /NDL /NP /LOG:sync-log.txt"}]}