{"paths": {"/api/v1/db/chartdata": {"get": {"summary": "Chart-<PERSON><PERSON> a<PERSON>", "description": "Ruft historische OHLCV-Daten (Open-High-Low-Close-Volume) für ein bestimmtes Symbol, eine Zeitperiode und einen Zeitraum ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Daten werden für die angegebene Zeitperiode aggregiert\n- Unterstützt verschiedene Zeitperioden (1, 5, 15, 30, 60, 240, 1440 Minuten)\n- Optimierte Datenbankabfragen für schnelle Antwortzeiten\n\nAnwendungsfälle:\n- Anzeige von Preischarts in Trading-Anwendungen\n- Technische Analyse und Mustererkennung\n- Backtesting von Trading-Strategien\n- Volatilitätsanalyse über verschiedene Zeiträume", "tags": ["Chart Data"], "parameters": [{"in": "query", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das Chart-Daten abgerufen werden sollen", "example": "EURUSD"}, {"in": "query", "name": "period", "required": true, "schema": {"type": "integer", "minimum": 1, "maximum": 1440}, "description": "Zeitperiode in Minuten (z.B. 1, 5, 15, 30, 60, 240, 1440)", "example": 15}, {"in": "query", "name": "days", "required": true, "schema": {"type": "integer", "minimum": 1, "maximum": 365}, "description": "<PERSON><PERSON><PERSON> der Tage in der Vergangenheit, für die Daten abgerufen werden sollen", "example": 30}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Chart-Daten", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChartDataPoint"}}, "example": [{"timestamp": "2023-04-15T14:30:00Z", "open": 1.0865, "high": 1.0878, "low": 1.086, "close": 1.0872, "volume": 1250.5}, {"timestamp": "2023-04-15T14:45:00Z", "open": 1.0872, "high": 1.088, "low": 1.0868, "close": 1.0875, "volume": 980.2}, {"timestamp": "2023-04-15T15:00:00Z", "open": 1.0875, "high": 1.0882, "low": 1.087, "close": 1.0878, "volume": 1120.8}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}