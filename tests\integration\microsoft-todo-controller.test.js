const request = require('supertest');
const express = require('express');
const nock = require('nock');
const microsoftRoutes = require('../../routes/microsoft');

// Mock the config
jest.mock('../../configs/constants', () => ({
  MICROSOFT_TODO_CONFIG: {
    API_BASE_URL: 'https://graph.microsoft.com/v1.0',
    AUTH_URL: 'https://login.microsoftonline.com',
    TENANT_ID: 'test-tenant-id',
    CLIENT_ID: 'test-client-id',
    CLIENT_SECRET: 'test-client-secret',
    SCOPES: ['https://graph.microsoft.com/.default'],
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,
    TIMEOUT: 30000,
    CACHE_TTL: {
      token: 3600
    },
    ENDPOINTS: {
      token: '/oauth2/v2.0/token',
      tasks: '/me/todo/lists/{listId}/tasks'
    },
    TASK_IMPORTANCE: {
      LOW: 'low',
      NORMAL: 'normal',
      HIGH: 'high'
    }
  }
}));

// Mock LoggingService
jest.mock('../../services/logging_service', () => ({
  LoggingService: {
    getInstance: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  }
}));

describe('Microsoft ToDo Controller Integration Tests', () => {
  let app;

  beforeAll(() => {
    // Create Express app for testing
    app = express();
    app.use(express.json());
    app.use('/api/v1/microsoft', microsoftRoutes);
  });

  beforeEach(() => {
    nock.cleanAll();
  });

  afterEach(() => {
    nock.cleanAll();
  });

  describe('POST /api/v1/microsoft/todo/tasks', () => {
    const mockTokenResponse = {
      access_token: 'mock-access-token',
      expires_in: 3600,
      token_type: 'Bearer'
    };

    const mockCreatedTask = {
      id: 'task-id-123',
      title: 'Test Task',
      body: { content: 'Test Description', contentType: 'text' },
      status: 'notStarted',
      importance: 'normal',
      createdDateTime: '2025-01-02T10:30:00.000Z',
      lastModifiedDateTime: '2025-01-02T10:30:00.000Z',
      dueDateTime: null,
      categories: [],
      webUrl: 'https://to-do.office.com/tasks/id/task-id-123'
    };

    beforeEach(() => {
      // Mock OAuth token endpoint
      nock('https://login.microsoftonline.com')
        .post('/test-tenant-id/oauth2/v2.0/token')
        .reply(200, mockTokenResponse);
    });

    it('should create task successfully with valid data', async () => {
      // Mock Graph API task creation
      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(201, mockCreatedTask);

      const taskData = {
        listId: 'test-list-id',
        title: 'Test Task',
        body: 'Test Description',
        importance: 'normal'
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(taskData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Task created successfully',
        data: {
          id: 'task-id-123',
          title: 'Test Task',
          status: 'notStarted',
          importance: 'normal'
        },
        meta: {
          requestId: expect.any(String),
          processingTime: expect.any(Number),
          timestamp: expect.any(String)
        }
      });
    });

    it('should create task with due date and categories', async () => {
      const taskWithDueDate = {
        ...mockCreatedTask,
        dueDateTime: { dateTime: '2025-01-15T14:00:00.000Z', timeZone: 'UTC' },
        categories: ['Work', 'Important']
      };

      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(201, taskWithDueDate);

      const taskData = {
        listId: 'test-list-id',
        title: 'Task with Due Date',
        dueDateTime: '2025-01-15T14:00:00.000Z',
        categories: ['Work', 'Important'],
        importance: 'high'
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(taskData)
        .expect(201);

      expect(response.body.data.dueDateTime).toBe('2025-01-15T14:00:00.000Z');
      expect(response.body.data.categories).toEqual(['Work', 'Important']);
    });

    it('should return 400 for missing required fields', async () => {
      const invalidData = {
        listId: 'test-list-id'
        // Missing title
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Invalid input data',
        error: 'title is required and must be a non-empty string',
        meta: {
          requestId: expect.any(String),
          processingTime: expect.any(Number),
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 for invalid listId', async () => {
      const invalidData = {
        listId: '',
        title: 'Test Task'
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('listId is required and must be a non-empty string');
    });

    it('should return 400 for invalid importance', async () => {
      const invalidData = {
        listId: 'test-list-id',
        title: 'Test Task',
        importance: 'invalid'
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('importance must be one of: low, normal, high');
    });

    it('should return 400 for invalid due date format', async () => {
      const invalidData = {
        listId: 'test-list-id',
        title: 'Test Task',
        dueDateTime: 'invalid-date'
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('dueDateTime must be a valid ISO 8601 date string if provided');
    });

    it('should return 400 for title too long', async () => {
      const invalidData = {
        listId: 'test-list-id',
        title: 'A'.repeat(256) // Exceeds 255 character limit
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('title must not exceed 255 characters');
    });

    it('should return 400 for body too long', async () => {
      const invalidData = {
        listId: 'test-list-id',
        title: 'Test Task',
        body: 'A'.repeat(4001) // Exceeds 4000 character limit
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('body must not exceed 4000 characters');
    });

    it('should return 400 for too many categories', async () => {
      const invalidData = {
        listId: 'test-list-id',
        title: 'Test Task',
        categories: Array(26).fill('Category') // Exceeds 25 category limit
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('categories must not exceed 25 items');
    });

    it('should return 401 for Microsoft authentication failure', async () => {
      // Mock OAuth error
      nock.cleanAll();
      nock('https://login.microsoftonline.com')
        .post('/test-tenant-id/oauth2/v2.0/token')
        .reply(401, {
          error: 'invalid_client',
          error_description: 'Invalid client credentials'
        });

      const taskData = {
        listId: 'test-list-id',
        title: 'Test Task'
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(taskData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Microsoft authentication failed',
        error: 'Invalid or expired credentials'
      });
    });

    it('should return 404 for non-existent list', async () => {
      // Mock Graph API 404 error
      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/non-existent-list/tasks')
        .reply(404, {
          error: {
            code: 'NotFound',
            message: 'The specified object was not found'
          }
        });

      const taskData = {
        listId: 'non-existent-list',
        title: 'Test Task'
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(taskData)
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Resource not found',
        error: 'The specified ToDo list was not found'
      });
    });

    it('should return 429 for rate limiting', async () => {
      // Mock Graph API rate limit error
      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(429, {
          error: {
            code: 'TooManyRequests',
            message: 'Rate limit exceeded'
          }
        }, {
          'retry-after': '60'
        });

      const taskData = {
        listId: 'test-list-id',
        title: 'Test Task'
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(taskData)
        .expect(429);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Rate limit exceeded',
        error: 'Too many requests to Microsoft Graph API'
      });
    });

    it('should return 500 for internal server errors', async () => {
      // Mock Graph API server error
      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(500, {
          error: {
            code: 'InternalServerError',
            message: 'Internal server error'
          }
        });

      const taskData = {
        listId: 'test-list-id',
        title: 'Test Task'
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(taskData)
        .expect(500);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Internal server error'
      });
    });

    it('should handle malformed JSON request body', async () => {
      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400);

      // Express will handle malformed JSON and return 400
      expect(response.status).toBe(400);
    });

    it('should sanitize long text fields in logs', async () => {
      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(201, mockCreatedTask);

      const taskData = {
        listId: 'test-list-id',
        title: 'A'.repeat(150), // Long title that should be truncated in logs
        body: 'B'.repeat(300),  // Long body that should be truncated in logs
        importance: 'normal'
      };

      const response = await request(app)
        .post('/api/v1/microsoft/todo/tasks')
        .send(taskData)
        .expect(201);

      expect(response.body.success).toBe(true);
    });
  });
});
