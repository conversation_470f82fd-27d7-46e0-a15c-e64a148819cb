###

// @name gpt_news_summarize: Limit 10 | Override
// limit 5 is used in production-setup and should also be used in test-setup
GET {{API_BASE_URL}}/api/v1/gpt_news_summarize?override=true&limit=10
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name AI-assistent-refresh-function
PUT {{API_BASE_URL}}/api/v1/ai/refresh_factor_map?refreshNews=false

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###
