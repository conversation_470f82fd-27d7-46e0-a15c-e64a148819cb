# API Endpoint Best Practices

This document outlines the best practices for creating and maintaining API endpoints in the Algotrader API, based on the current implementation patterns.

## Table of Contents

1. [Endpoint Structure](#endpoint-structure)
2. [Controller Design](#controller-design)
3. [Parameter Validation](#parameter-validation)
4. [Caching Strategy](#caching-strategy)
5. [Logging Standards](#logging-standards)
6. [Error Handling](#error-handling)
7. [Route Organization](#route-organization)
8. [Performance Considerations](#performance-considerations)

## Endpoint Structure

### RESTful Design

- **Resource-Based URLs**: Organize endpoints around resources (e.g., `/symbol_setup`, `/trade_history`)
- **HTTP Methods**: Use appropriate HTTP methods:
  - `GET` for retrieving data
  - `PUT` for updating existing resources
  - `POST` for creating new resources
  - `DELETE` for removing resources
- **Versioning**: Include API version in the URL path (e.g., `/api/v1/db/...`)
- **Query Parameters**: Use for filtering, pagination, and optional parameters
- **Path Parameters**: Use for identifying specific resources (e.g., `/symbol_setup/:symbol`)

### Response Format

- **JSON Structure**: Return consistent JSON structures
- **Status Codes**: Use appropriate HTTP status codes:
  - `200` for successful operations
  - `400` for client errors
  - `500` for server errors
- **Error Responses**: Include descriptive error messages and error codes

## Controller Design

### Controller Pattern

Controllers should follow a consistent pattern:

1. **Route Handler Function**: Receives `req` and `res` objects
   ```javascript
   async function getNewsFromDB(req, res) {
     try {
       // Extract and validate parameters
       // Call independent function with caching
       // Send response
     } catch (err) {
       // Handle errors
     }
   }
   ```

2. **Independent Function**: Contains the core business logic
   ```javascript
   async function getNewsFromDBIndependent(limit = 50, days = 3) {
     const startTime = process.hrtime.bigint();
     try {
       // Log start
       // Validate parameters
       // Execute query
       // Log success
       // Return result
     } catch (err) {
       // Log error
       // Rethrow
     } finally {
       // Log performance
     }
   }
   ```

### Separation of Concerns

- **Route Handlers**: Focus on HTTP request/response handling
- **Independent Functions**: Contain business logic, can be called programmatically
- **Query Builders**: Separate SQL query construction into dedicated modules
- **Service Modules**: Use for cross-cutting concerns (caching, logging, validation)

## Parameter Validation

### Validation Approach

- **Early Validation**: Validate parameters at the beginning of functions
- **Consistent Validation**: Use the validation service for all parameters
- **Default Values**: Provide sensible defaults for optional parameters
- **Constraints**: Define min/max values and other constraints

### Validation Functions

Use the validation service functions:

```javascript
// Numeric parameter validation
limit = validateNumericParam(limit, {
  defaultValue: QUERY_LIMITS.DEFAULT_NEWS,
  min: 1,
  max: 1000,
  paramName: 'limit'
});

// String parameter validation
symbol = validateStringParam(symbol, {
  required: true,
  minLength: 2,
  maxLength: 50,
  paramName: 'symbol'
});

// Date parameter validation
date = validateDateParam(date, {
  defaultValue: new Date(),
  paramName: 'date'
});

// Array parameter validation
items = validateArrayParam(items, {
  minLength: 1,
  maxLength: 100,
  paramName: 'items'
});
```

### Constants for Limits

- Define constants for default values, limits, and other parameters
- Store in a central location (`configs/constants.js`)
- Reference these constants in validation logic

## Caching Strategy

### Cache Implementation

- **Cache Service**: Use the `withCacheWrapper` function for caching
- **Cache Types**: Define different cache types with appropriate TTL values
- **Cache Keys**: Generate consistent cache keys based on function name and parameters

### Caching Pattern

```javascript
const result = await withCacheWrapper(
  'NEWS',                    // Cache type
  'getNewsFromDB',           // Function name
  () => getNewsFromDBIndependent(p_limit, p_days),  // Data function
  [p_limit, p_days]          // Parameters for cache key
);
```

### Cache Invalidation

- **Selective Invalidation**: Invalidate specific cache types when data changes
- **Prefix-Based Invalidation**: Use cache prefixes to group related cache entries
- **TTL-Based Expiration**: Set appropriate time-to-live values based on data volatility

## Logging Standards

### Log Levels

- **ERROR**: Critical errors requiring immediate attention
- **WARN**: Potential issues or unexpected conditions
- **INFO**: Important operational events
- **DEBUG**: Detailed information for troubleshooting

### Logging Pattern

```javascript
// Start of operation
log(LOG_LEVELS.INFO, 'functionName', 'Starting operation', {
  param1,
  param2
});

// Debug information
log(LOG_LEVELS.DEBUG, 'functionName', 'Executing query', {
  queryParams
});

// Success information
log(LOG_LEVELS.INFO, 'functionName', 'Operation successful', {
  resultCount: result.length
});

// Error logging
log(LOG_LEVELS.ERROR, 'functionName', 'Operation failed', {
  error: err.message,
  stack: err.stack,
  params
});
```

### Performance Logging

- **Measure Execution Time**: Use `process.hrtime.bigint()` for precise timing
- **Log Performance Metrics**: Use `logPerformance` function in `finally` blocks
- **Track Resource Usage**: Include memory usage and other relevant metrics

## Error Handling

### Error Types

- **ValidationError**: For invalid input parameters
- **DatabaseError**: For database-related errors
- **Custom Error Types**: For specific error scenarios

### Error Handling Pattern

```javascript
try {
  // Operation code
} catch (err) {
  // Log the error
  log(LOG_LEVELS.ERROR, 'functionName', 'Operation failed', {
    error: err.message,
    stack: err.stack
  });
  
  // For route handlers, use errorHandler
  const errorResponse = errorHandler(err, 'functionName');
  res.status(errorResponse.status).json(errorResponse);
  
  // For independent functions, rethrow
  throw err;
}
```

### Error Response Format

```json
{
  "status": 400,
  "error": "ValidationError",
  "message": "Parameter 'limit' must be at least 1",
  "function": "getNewsFromDB"
}
```

## Route Organization

### Route Grouping

- Group routes by functionality or resource type
- Use clear, descriptive comments to separate route groups
- Maintain consistent naming conventions

### Route Definition

```javascript
// Basic pattern
router.get('/resource', controller.getResource);

// With path parameters
router.get('/resource/:id', controller.getResourceById);

// With nested resources
router.get('/parent/:parentId/child', controller.getChildResources);
```

### Controller Organization

- Organize controllers by resource or functionality
- Split large controllers into smaller, focused modules
- Use consistent file naming conventions

## Performance Considerations

### Query Optimization

- Optimize database queries for performance
- Use appropriate indexes
- Limit result sets to necessary data

### Caching Strategy

- Cache frequently accessed, rarely changing data
- Use appropriate TTL values based on data volatility
- Implement selective cache invalidation

### Asynchronous Processing

- Use async/await for asynchronous operations
- Handle promises properly
- Avoid blocking the event loop

### Resource Management

- Monitor memory usage
- Release resources properly
- Use connection pooling for database connections

---

By following these best practices, you'll create consistent, maintainable, and efficient API endpoints for the Algotrader API.