<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" autoUpload="Always" serverName="ml-algotrader" remoteFilesAllowedToDisappearOnAutoupload="false">
    <serverData>
      <paths name="ml-algotrader">
        <serverdata>
          <mappings>
            <mapping deploy="/var/www/vhosts/ml-algotrader.com/api.ml-algotrader.com" local="$PROJECT_DIR$" web="/" />
          </mappings>
          <excludedPaths>
            <excludedPath local="true" path="$PROJECT_DIR$/.idea" />
            <excludedPath local="true" path="$PROJECT_DIR$/tests" />
          </excludedPaths>
        </serverdata>
      </paths>
    </serverData>
    <option name="myAutoUpload" value="ALWAYS" />
  </component>
</project>