const { Langfuse } = require("langfuse");
require('dotenv').config();

const langfuse = new Langfuse({
    secretKey: process.env.LANGFUSE_SECRET_KEY,
    publicKey: process.env.LANGFUSE_PUBLIC_KEY,
    baseUrl: "https://cloud.langfuse.com"
});

/**
 * Creates and manages a Langfuse trace for LLM interactions
 */
class LangfuseTraceManager {
    constructor(traceName, input, userId) {
        this.trace = langfuse.trace({
            name: traceName,
            input: input,
            userId: userId,
            metadata: { env: "production" }
        });
        this.generation = null;
    }

    startGeneration(name, model, temperature = 0.4, prompt = null) {
        this.generation = this.trace.generation({
            name: name,
            model: model,
            modelParameters: { temperature },
            prompt: prompt || name,
        });
        return this;
    }

    updateGeneration(input) {
        if (this.generation) {
            this.generation.update({ 
                completionStartTime: new Date(), 
                input: input 
            });
        }
        return this;
    }

    endGeneration(output, promptID = null, prompt = null) {
        if (this.generation) {
            this.generation.end({
                output: output,
                promptID: promptID,
                prompt: prompt,
                completionEndTime: new Date(),
            });
        }
        return this;
    }

    updateTrace(output) {
        this.trace.update({ 
            output: output, 
            metadata: {} 
        });
        return this;
    }

    /**
     * Fetches a prompt from Langfuse
     * @param {string} promptID - The ID of the prompt to fetch
     * @returns {Promise<Object>} The prompt object
     */
    static async getPrompt(promptID) {
        return await langfuse.getPrompt(promptID);
    }

    /**
     * Compiles a prompt with variables
     * @param {Object} prompt - The prompt object from Langfuse
     * @param {Object|string} promptVariables - Variables to compile into the prompt
     * @returns {string} The compiled prompt
     */
    static async compilePrompt(prompt, promptVariables) {
        if (!prompt || !prompt.compile) {
            throw new Error('Invalid prompt or prompt without compile function');
        }

        let parsedVariables;
        if (typeof promptVariables === 'string') {
            parsedVariables = JSON.parse(promptVariables);
        } else if (typeof promptVariables === 'object') {
            parsedVariables = promptVariables;
        } else {
            throw new Error('promptVariables hat einen unbekannten Typ');
        }

        const flattenedVariables = LangfuseTraceManager.flattenPromptVariables(parsedVariables);
        return prompt.compile(flattenedVariables);
    }

    static flattenPromptVariables(variables) {
        const result = {};
        for (const [key, value] of Object.entries(variables)) {
            result[key] = LangfuseTraceManager.flattenValue(value);
        }
        return result;
    }

    static flattenValue(value) {
        if (Array.isArray(value)) {
            return value.map(v => LangfuseTraceManager.flattenValue(v)).join(',');
        } else if (typeof value === 'object' && value !== null) {
            return Object.entries(value)
                .map(([k, v]) => `${k}:${LangfuseTraceManager.flattenValue(v)}`)
                .join(',');
        }
        return String(value);
    }
}

module.exports = {
    LangfuseTraceManager
};
