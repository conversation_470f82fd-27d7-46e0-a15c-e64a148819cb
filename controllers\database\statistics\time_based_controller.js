const { executeQuery } = require('../../../services/database_service');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../../services/cache_service');
const { LOG_LEVELS, log, logPerformance } = require('../../../services/logging_service');
const { DatabaseError, ValidationError, errorHandler } = require('../errors/database_errors');
const { validateNumericParam, validateDateParam, validateStringParam} = require('../../../services/validation_service');

/**
 * Controller für zeitbasierte Statistiken
 */
class TimeBasedController {
    /**
     * @swagger
     * tags:
     *   name: Statistics
     *   description: Trading performance analytics and metrics
     *
     * /api/v1/statistics/day:
     *   get:
     *     summary: Retrieve daily trading performance
     *     description: |
     *       Fetches detailed trading statistics aggregated by day.
     *       
     *       Technical Details:
     *       - Cached responses (5 min TTL)
     *       - Timezone-aware calculations
     *       - Profit rounding to whole numbers
     *       - Account-specific filtering
     *       
     *       Performance Metrics:
     *       - Daily profit/loss totals
     *       - Trade count analysis
     *       - Min/max trade performance
     *       - Success rate calculation
     *       
     *       Use Cases:
     *       - Daily performance tracking
     *       - Risk management analysis
     *       - Strategy effectiveness monitoring
     *       - Pattern identification
     *     tags: [Statistics]
     *     parameters:
     *       - in: query
     *         name: refID
     *         required: true
     *         schema:
     *           type: string
     *         description: Account reference ID
     *       - in: query
     *         name: startDate
     *         schema:
     *           type: string
     *           format: date
     *         description: Start date for statistics (defaults to 14 days ago)
     *     responses:
     *       200:
     *         description: Daily trading statistics
     *         content:
     *           application/json:
     *             schema:
     *               type: array
     *               items:
     *                 type: object
     *                 properties:
     *                   date:
     *                     type: string
     *                     format: date
     *                   min_profit:
     *                     type: number
     *                   max_profit:
     *                     type: number
     *                   cnt_trades:
     *                     type: integer
     *                   sum_profit:
     *                     type: number
     *       400:
     *         description: Invalid parameters
     *       500:
     *         description: Database error
     */
    async getDayStatistics(req, res) {
        const startTime = process.hrtime.bigint();
        try {
            const { refID, startDate } = req.query;
            
            if (!refID) {
                throw new ValidationError('refID is required');
            }

            const date14DaysAgo = new Date(new Date().getTime() - 14 * 24 * 60 * 60 * 1000);
            const validatedStartDate = validateDateParam(startDate, {
                required: false,
                defaultValue: date14DaysAgo,
                paramName: 'startDate'
            });

            log(LOG_LEVELS.INFO, 'getDayStatistics', 'Starting day statistics retrieval', {
                refID,
                startDate: validatedStartDate
            });

            return await withCacheWrapper(
                CACHE_PREFIX.TRADE,
                'getDayStatistics',
                async () => {
                    const query = {
                        sql: `SELECT date(t.exit_time) as date,
                                     round(min(profit), 0) AS min_profit,
                                     round(max(profit), 0) AS max_profit,
                                     COUNT(*) AS cnt_trades,
                                     round(SUM(profit), 0) AS sum_profit
                              FROM \`trades_history\` t,
                                   \`accounts\` a
                              WHERE t.account = a.account_id
                                AND a.refId = ?
                                AND exit_time >= ?
                              GROUP BY date(t.exit_time)
                              order by date(t.exit_time)`,
                        bigIntAsNumber: true
                    };

                    const result = await executeQuery(query, [refID, validatedStartDate]);
                    res.send(result);
                },
                [refID, validatedStartDate]
            );
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getDayStatistics', 'Failed to get day statistics', { error: err });
            const errorResponse = errorHandler(err, 'getDayStatistics');
            res.status(errorResponse.status).json(errorResponse);
        } finally {
            logPerformance('getDayStatistics', startTime);
        }
    }

    /**
     * @swagger
     * /api/v1/statistics/week:
     *   get:
     *     summary: Retrieve weekly trading performance
     *     description: |
     *       Fetches detailed trading statistics aggregated by week.
     *       
     *       Technical Details:
     *       - Cached responses (5 min TTL)
     *       - Week numbering (ISO-8601)
     *       - Rolling 3-month analysis
     *       - Performance benchmarking
     *       
     *       Performance Metrics:
     *       - Weekly profit/loss totals
     *       - Trade frequency analysis
     *       - Win/loss ratio tracking
     *       - Risk-adjusted returns
     *       
     *       Use Cases:
     *       - Weekly performance review
     *       - Strategy optimization
     *       - Risk exposure analysis
     *       - Trading consistency check
     *     tags: [Statistics]
     *     parameters:
     *       - in: query
     *         name: refID
     *         required: true
     *         schema:
     *           type: string
     *         description: Account reference ID
     *     responses:
     *       200:
     *         description: Weekly trading statistics
     *         content:
     *           application/json:
     *             schema:
     *               type: array
     *               items:
     *                 type: object
     *                 properties:
     *                   year:
     *                     type: integer
     *                   week:
     *                     type: integer
     *                   min_profit:
     *                     type: number
     *                   max_profit:
     *                     type: number
     *                   cnt_trades:
     *                     type: integer
     *                   sum_profit:
     *                     type: number
     *       400:
     *         description: Invalid parameters
     *       500:
     *         description: Database error
     */
    async getWeekStatistics(req, res) {
        const startTime = process.hrtime.bigint();
        try {
            const refID = req.query.refID;
            
            if (!refID) {
                throw new ValidationError('refID is required');
            }

            const validatedRefID = validateStringParam(refID, {
                required: true,
                minLength: 2,
                maxLength: 50,
                paramName: 'refID'
            });


            log(LOG_LEVELS.INFO, 'getWeekStatistics', 'Starting week statistics retrieval', {
                refID: validatedRefID
            });
            await withCacheWrapper(
                'TRADE',
                'getWeekStatistics',
                async () => {
                    const query = {
                        sql: 'SELECT YEAR(t.exit_time) as year, WEEKOFYEAR(t.exit_time) as week, ' +
                             'round(min(profit),0) AS min_profit, round(max(profit),0) AS max_profit, ' +
                             'COUNT(*) AS cnt_trades, round(SUM(profit),0) AS sum_profit ' +
                             'FROM `trades_history` t, `accounts` a ' +
                             'WHERE t.account=a.account_id ' +
                             'AND t.exit_time>=DATE_SUB(NOW(), INTERVAL 3 Month) ' +
                             'AND a.refId=? ' +
                             'GROUP BY WEEKOFYEAR(t.exit_time) ' +
                             'ORDER BY year,week',
                        bigIntAsNumber: true
                    };

                    const result = await executeQuery(query, [refID]);
                    res.send(result);
                },
                [refID]
            );
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getWeekStatistics', 'Failed to get week statistics', { error: err });
            const errorResponse = errorHandler(err, 'getWeekStatistics');
            return res.status(errorResponse.status).json(errorResponse);
        } finally {
            logPerformance('getWeekStatistics', startTime);
        }
    }

    /**
     * @swagger
     * /api/v1/statistics/month:
     *   get:
     *     summary: Retrieve monthly trading performance
     *     description: |
     *       Fetches detailed trading statistics aggregated by month.
     *       
     *       Technical Details:
     *       - Cached responses (5 min TTL)
     *       - Monthly rollover handling
     *       - Quarter-end calculations
     *       - Year-to-date tracking
     *       
     *       Performance Metrics:
     *       - Monthly profit/loss totals
     *       - Trade volume analysis
     *       - Performance trends
     *       - Capital efficiency
     *       
     *       Use Cases:
     *       - Monthly performance review
     *       - Strategy assessment
     *       - Capital allocation planning
     *       - Long-term trend analysis
     *     tags: [Statistics]
     *     parameters:
     *       - in: query
     *         name: refID
     *         required: true
     *         schema:
     *           type: string
     *         description: Account reference ID
     *     responses:
     *       200:
     *         description: Monthly trading statistics
     *         content:
     *           application/json:
     *             schema:
     *               type: array
     *               items:
     *                 type: object
     *                 properties:
     *                   year:
     *                     type: integer
     *                   month:
     *                     type: integer
     *                   min_profit:
     *                     type: number
     *                   max_profit:
     *                     type: number
     *                   cnt_trades:
     *                     type: integer
     *                   sum_profit:
     *                     type: number
     *       400:
     *         description: Invalid parameters
     *       500:
     *         description: Database error
     */
    async getMonthStatistics(req, res) {
        const startTime = process.hrtime.bigint();
        try {
            const refID = req.query.refID;
            
            if (!refID) {
                throw new ValidationError('refID is required');
            }

            const validatedRefID = validateStringParam(refID, {
                required: true,
                minLength: 2,
                maxLength: 50,
                paramName: 'refID'
            });


            log(LOG_LEVELS.INFO, 'getMonthStatistics', 'Starting month statistics retrieval', {
                refID: validatedRefID
            });
            await withCacheWrapper(
                'TRADE',
                'getMonthStatistics',
                async () => {
                    const query = {
                        sql: `SELECT YEAR(t.exit_time) as year,
                                     MONTH(t.exit_time) as month,
                                     round(min(profit), 0) AS min_profit,
                                     round(max(profit), 0) AS max_profit,
                                     COUNT(*) AS cnt_trades,
                                     round(SUM(profit), 0) AS sum_profit
                              FROM \`trades_history\` t,
                                   \`accounts\` a
                              WHERE t.account = a.account_id
                                AND t.exit_time >= DATE_SUB(NOW(), INTERVAL 3 Month)
                                AND a.refId = ?
                              GROUP BY MONTH(t.exit_time)
                              ORDER BY year, month`,
                        bigIntAsNumber: true
                    };

                    const result = await executeQuery(query, [refID]);
                    res.send(result);
                },
                [refID]
            );
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getMonthStatistics', 'Failed to get month statistics', { error: err });
            const errorResponse = errorHandler(err, 'getMonthStatistics');
            return res.status(errorResponse.status).json(errorResponse);
        } finally {
            logPerformance('getMonthStatistics', startTime);
        }
    }
}

module.exports = new TimeBasedController();
