/**
 * SQL Queries für zeitbasierte Statistiken
 */
const TimeBasedQueries = {
    dayStatistics: {
        sql: 'SELECT date(t.exit_time) as date, round(min(profit),0) AS min_profit, round(max(profit),0) AS max_profit, COUNT(*) AS cnt_trades, round(SUM(profit),0) AS sum_profit \
             FROM `trades_history` t, `accounts` a \
             WHERE t.account=a.account_id \
              AND a.refId=? \
              AND exit_time >= ? \
            GROUP BY date(t.exit_time) order by date(t.exit_time)',
        bigIntAsNumber: true
    },

    weekStatistics: {
        sql: 'SELECT YEAR(t.exit_time) as year, WEEKOFYEAR(t.exit_time) as week, round(min(profit),0) AS min_profit, round(max(profit),0) AS max_profit, COUNT(*) AS cnt_trades, round(SUM(profit),0) AS sum_profit \
             FROM `trades_history` t, `accounts` a \
             WHERE t.account=a.account_id \
              AND t.exit_time>=DATE_SUB(NOW(), INTERVAL 3 Month) \
              AND a.refId=? \
            GROUP BY WEEKOFYEAR(t.exit_time) \
             ORDER BY year,week',
        bigIntAsNumber: true
    },

    monthStatistics: {
        sql: 'SELECT YEAR(t.exit_time) as year, MONTH(t.exit_time) as month, round(min(profit),0) AS min_profit, round(max(profit),0) AS max_profit, COUNT(*) AS cnt_trades, round(SUM(profit),0) AS sum_profit \
             FROM `trades_history` t, `accounts` a \
             WHERE t.account=a.account_id \
              AND t.exit_time>=DATE_SUB(NOW(), INTERVAL 3 Month) \
              AND a.refId=? \
            GROUP BY MONTH(t.exit_time) \
             ORDER BY year,month',
        bigIntAsNumber: true
    }
};

module.exports = TimeBasedQueries;
