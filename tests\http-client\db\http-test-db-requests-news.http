###

// @name latest_factor_map
GET {{API_BASE_URL}}/api/v1/db/latest_factor_map
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: pivotpoints");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name latest_factor_map mit Limit 2
GET {{API_BASE_URL}}/api/v1/db/latest_factor_map?limit=2
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name newsFromDB
// limit 5 is used in production-setup and should also be used in test-setup
GET {{API_BASE_URL}}/api/v1/db/news?limit=10&days=1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: News from DB - Basic request");
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(Array.isArray(response.body), "Response should be an array");
    });
%}

###

// @name newsFromDB-onlyUnread
GET {{API_BASE_URL}}/api/v1/db/news?limit=1&days=1&onlyUnread=false
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: News from DB - Only unread news");
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(Array.isArray(response.body), "Response should be an array");
        
        // Check that all returned news items have is_read = 0 (if any results)
        if (response.body.length > 0) {
            const firstItem = response.body[0];
            client.log("First item is_read value: " + firstItem.is_read);
            // Note: We can't assert is_read = 0 because it depends on data state
        }
    });
%}

###

// @name newsFromDB-onlyBookmarked
GET {{API_BASE_URL}}/api/v1/db/news?limit=10&days=2&onlyBookmarked=true
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: News from DB - Only bookmarked news");
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(Array.isArray(response.body), "Response should be an array");
        
        // Check that all returned news items have is_bookmarked = 1 (if any results)
        if (response.body.length > 0) {
            const firstItem = response.body[0];
            client.log("First item is_bookmarked value: " + firstItem.is_bookmarked);
            // Note: We can't assert is_bookmarked = 1 because it depends on data state
        }
    });
%}

###

// @name newsFromDB-unreadAndBookmarked
GET {{API_BASE_URL}}/api/v1/db/news?limit=10&days=7&onlyUnread=true&onlyBookmarked=true
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: News from DB - Unread AND bookmarked news");
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(Array.isArray(response.body), "Response should be an array");
        
        // Check that all returned news items have both conditions (if any results)
        if (response.body.length > 0) {
            const firstItem = response.body[0];
            client.log("First item is_read value: " + firstItem.is_read);
            client.log("First item is_bookmarked value: " + firstItem.is_bookmarked);
        }
    });
%}

###

// @name newsFromDB-keywordSearch
GET {{API_BASE_URL}}/api/v1/db/news?limit=10&days=7&searchKeywords=Drohne,KI,Schwarm
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: News from DB - Keyword search for USD and EUR");
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(Array.isArray(response.body), "Response should be an array");
        
        // Log some results for verification
        if (response.body.length > 0) {
            client.log("Found " + response.body.length + " news items matching keywords");
            const firstItem = response.body[0];
            client.log("First item title: " + firstItem.title);
        } else {
            client.log("No news items found matching the keywords");
        }
    });
%}

###

// @name newsFromDB-singleKeywordSearch
GET {{API_BASE_URL}}/api/v1/db/news?limit=5&days=3&searchKeywords=Bitcoin
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: News from DB - Single keyword search for Bitcoin");
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(Array.isArray(response.body), "Response should be an array");
        
        if (response.body.length > 0) {
            client.log("Found " + response.body.length + " news items about Bitcoin");
        }
    });
%}

###

// @name newsFromDB-keywordSearchWithFilters
GET {{API_BASE_URL}}/api/v1/db/news?limit=5&days=7&searchKeywords=Market,Trading&onlyUnread=false&onlyBookmarked=false
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: News from DB - Keyword search combined with filters");
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(Array.isArray(response.body), "Response should be an array");
        
        if (response.body.length > 0) {
            client.log("Found " + response.body.length + " news items matching keywords and filters");
        }
    });
%}

###

// @name newsFromDB-emptyKeywordSearch
GET {{API_BASE_URL}}/api/v1/db/news?limit=5&days=1&searchKeywords=
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: News from DB - Empty keyword search (should return all news)");
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(Array.isArray(response.body), "Response should be an array");
    });
%}

###

// @name newsFromDB-withBooleanStrings
GET {{API_BASE_URL}}/api/v1/db/news?limit=5&days=3&onlyUnread=1&onlyBookmarked=0
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: News from DB - Boolean parameters as strings");
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(Array.isArray(response.body), "Response should be an array");
    });
%}