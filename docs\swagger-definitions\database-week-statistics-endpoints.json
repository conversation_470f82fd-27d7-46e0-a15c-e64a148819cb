{"paths": {"/api/v1/db/statistics/week": {"get": {"summary": "Wochenstatistiken abrufen", "description": "Ruft Handelsstatistiken auf Wochenbasis für ein bestimmtes Konto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Gruppiert nach Jahr und Kalenderwoche\n- Enthält Min/Max-Gewinn, Anzahl der Trades und Gesamtgewinn pro Woche\n- Standardmäßig werden die letzten 3 Monate abgerufen\n\nAnwendungsfälle:\n- An<PERSON><PERSON> von wöchentlichen Performance-Metriken im Dashboard\n- Ana<PERSON><PERSON> von Handelsmustern über verschiedene Wochen\n- Identifizierung von wöchentlichen Trends\n- Berechnung von wöchentlichen Durchschnittswerten für Performance-Berichte", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Wochenstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeekStatistics"}}, "example": [{"year": 2023, "week": 15, "min_profit": -180, "max_profit": 420, "cnt_trades": 35, "sum_profit": 850}, {"year": 2023, "week": 14, "min_profit": -150, "max_profit": 380, "cnt_trades": 32, "sum_profit": 720}, {"year": 2023, "week": 13, "min_profit": -200, "max_profit": 450, "cnt_trades": 38, "sum_profit": 780}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/statistics/current_week": {"get": {"summary": "Statistiken der aktuellen Woche abrufen", "description": "Ruft Handelsstatistiken für die aktuelle Woche eines bestimmten Kontos ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält nur Daten der aktuellen Woche\n- Enthält Min/Max-Gewinn, Anzahl der Trades und Gesamtgewinn\n- Filtert automatisch nach dem aktuellen Jahr und der aktuellen Kalenderwoche\n\nAnwendungsfälle:\n- Echtzeit-Überwachung der aktuellen Wochenperformance\n- Dashboard-Anzeige für die laufende Woche\n- Vergleich mit Zielvorgaben für die aktuelle Woche\n- Schnelle Überprüfung des Wochenergebnisses", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der aktuellen Wochenstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrentWeekStatistics"}}, "example": [{"year": 2023, "week": 15, "min_profit": -180, "max_profit": 420, "cnt_trades": 35, "sum_profit": 850}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}