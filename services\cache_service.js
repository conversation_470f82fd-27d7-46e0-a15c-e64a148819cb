const NodeCache = require('node-cache');
const { DatabaseError } = require('../controllers/database/errors/database_errors');
const { log, LOG_LEVELS } = require('./logging_service');

function sanitizeForLogging(obj) {
    if (!obj || typeof obj !== 'object') return obj;
    
    if (obj.constructor?.name === 'Request' || obj.constructor?.name === 'Response') {
        return '[Express ' + obj.constructor.name + ']';
    }

    const clean = Array.isArray(obj) ? [] : {};
    
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            try {
                clean[key] = sanitizeForLogging(obj[key]);
            } catch (err) {
                clean[key] = '[Nicht serialisierbar]';
            }
        }
    }
    
    return clean;
}

const cache = new NodeCache({
    stdTTL: 90,          // Time to live in seconds
    checkperiod: 120,     // Check for expired keys every 20 seconds (optimized for trading data)
    useClones: true     // For better performance
});

const DEFAULT_TTL = {
    TRADE: 1,
    CALENDAR: 10,
    ACCOUNT: 1,
    CHART: 10,
    NEWS: 10,
    GENERAL: 10
};

const CACHE_PREFIX = {
    TRADE: 'trade:',
    CALENDAR: 'calendar:',
    ACCOUNT: 'account:',
    CHART: 'chart:',
    NEWS: 'news:',
    GENERAL: 'general:'
};

function generateCacheKey(prefix, functionName, params = []) {
    const validParams = params.filter(param => param !== undefined && param !== null);
    return `${prefix}${functionName}:${validParams.join(':')}`;
}

async function withCacheWrapper(type, functionName, dataFn, params = [], customTTL = null) {
    const startTime = process.hrtime.bigint();
    try {
        const prefix = CACHE_PREFIX[type] || CACHE_PREFIX.GENERAL;
        const ttl = customTTL || DEFAULT_TTL[type] || DEFAULT_TTL.GENERAL;
        const cacheKey = generateCacheKey(prefix, functionName, params);
        const cachedData = cache.get(cacheKey);
        if (cachedData) {
            log(LOG_LEVELS.INFO, 'withCacheWrapper', 'Cache hit', {
                cacheKey,
                type,
                functionName
            });
            return cachedData;
        }

        const result = await dataFn();
        
        cache.set(cacheKey, result, ttl);
        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'withCacheWrapper', 'Cache operation failed', sanitizeForLogging({
            error: err.message,
            stack: err.stack,
            type,
            functionName
        }));
        throw err;
    } finally {
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1_000_000;
        log(LOG_LEVELS.DEBUG, 'withCacheWrapper', 'Cache operation completed', {
            duration_ms: duration,
            type,
            functionName
        });
    }
}

function invalidateCache(type) {
    const prefix = CACHE_PREFIX[type] || CACHE_PREFIX.GENERAL;
    const keys = cache.keys();
    const invalidatedKeys = keys.filter(key => key.startsWith(prefix));
    
    log(LOG_LEVELS.INFO, 'invalidateCache', 'Invalidating cache entries', {
        type,
        prefix,
        keysCount: invalidatedKeys.length
    });

    invalidatedKeys.forEach(key => cache.del(key));
    
    return invalidatedKeys.length;
}

module.exports = {
    withCacheWrapper,
    invalidateCache,
    CACHE_PREFIX
};
