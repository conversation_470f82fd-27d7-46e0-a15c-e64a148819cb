# Microsoft ToDo Integration - Troubleshooting Guide

## Übersicht

Dieser Guide hilft bei der Diagnose und Lösung häufiger Probleme mit der Microsoft ToDo Integration.

## Diagnose-Tools

### 1. Performance Metrics

Rufen Sie aktuelle Metriken ab:

```bash
curl -X GET "http://localhost:3000/api/v1/microsoft/todo/metrics" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### 2. Log-Analyse

Aktivieren Sie Debug-Logging:

```env
LOG_LEVEL=debug
```

### 3. HTTP Client Tests

Verwenden Sie die bereitgestellten HTTP-Tests:

```
# Datei: tests/http-client/microsoft/microsoft-todo-requests.http
```

## Häufige Fehler und Lösungen

### Authentication Errors

#### 401 Unauthorized - Invalid Client

**Symptom**:
```json
{
  "error": "invalid_client",
  "error_description": "AADSTS70002: Error validating credentials..."
}
```

**Ursachen und Lösungen**:

1. **Falsche Client ID oder Secret**
   ```bash
   # Überprüfen Sie Environment Variables
   echo $MICROSOFT_CLIENT_ID
   echo $MICROSOFT_CLIENT_SECRET
   ```

2. **Client Secret abgelaufen**
   - Erstellen Sie ein neues Secret in Azure Portal
   - Aktualisieren Sie `MICROSOFT_CLIENT_SECRET`

3. **Falsche Tenant ID**
   ```bash
   # Überprüfen Sie Tenant ID
   echo $MICROSOFT_TENANT_ID
   ```

#### 401 Unauthorized - Insufficient Privileges

**Symptom**:
```json
{
  "error": {
    "code": "Forbidden",
    "message": "Insufficient privileges to complete the operation."
  }
}
```

**Lösungen**:

1. **API Permissions überprüfen**:
   - Azure Portal > App Registration > API permissions
   - Stellen Sie sicher, dass `Tasks.ReadWrite` vorhanden ist

2. **Admin Consent erteilen**:
   - Klicken Sie auf "Grant admin consent"
   - Warten Sie 5-10 Minuten für Propagation

### Rate Limiting Errors

#### 429 Too Many Requests

**Symptom**:
```json
{
  "error": {
    "code": "TooManyRequests",
    "message": "Rate limit exceeded"
  }
}
```

**Automatische Behandlung**:
- Service implementiert automatisches Retry mit Exponential Backoff
- Überprüfen Sie `Retry-After` Header

**Manuelle Lösungen**:

1. **Rate Limit Status überprüfen**:
   ```bash
   curl -X GET "http://localhost:3000/api/v1/microsoft/todo/metrics"
   ```

2. **Anfrage-Frequenz reduzieren**:
   ```javascript
   // Implementieren Sie Client-seitige Rate Limiting
   const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
   await delay(1000); // 1 Sekunde warten
   ```

3. **Batch-Requests verwenden** (zukünftige Implementierung)

### Validation Errors

#### 400 Bad Request - Invalid List ID

**Symptom**:
```json
{
  "error": "ValidationError",
  "message": "listId is required and must be a non-empty string"
}
```

**Lösungen**:

1. **List ID Format überprüfen**:
   ```javascript
   // Korrekte List ID Format
   const listId = "AQMkADAwATM0MDAAMS1iNTcwLWI2NTEtMDACLTAwCgAuAAADiQ8RE6fqaEOyolDoO-lHwwEA";
   ```

2. **Verfügbare Listen abrufen** (manuell über Graph Explorer):
   ```
   GET https://graph.microsoft.com/v1.0/me/todo/lists
   ```

#### 400 Bad Request - Invalid Date Format

**Symptom**:
```json
{
  "error": "ValidationError",
  "message": "dueDateTime must be a valid ISO 8601 date string if provided"
}
```

**Lösung**:
```javascript
// Korrekte ISO 8601 Formate
const dueDateTime = "2024-12-31T23:59:59.000Z";
const dueDateTime2 = new Date().toISOString();
```

### Network and Connectivity Issues

#### ECONNREFUSED / Network Timeout

**Symptom**:
```json
{
  "error": "Network Error",
  "message": "connect ECONNREFUSED"
}
```

**Diagnose**:

1. **Internet-Verbindung testen**:
   ```bash
   curl -I https://graph.microsoft.com
   ```

2. **DNS-Auflösung überprüfen**:
   ```bash
   nslookup graph.microsoft.com
   ```

3. **Firewall/Proxy-Einstellungen**:
   - Stellen Sie sicher, dass Port 443 (HTTPS) offen ist
   - Überprüfen Sie Corporate Proxy-Einstellungen

**Lösungen**:

1. **Proxy-Konfiguration** (falls erforderlich):
   ```env
   HTTP_PROXY=http://proxy.company.com:8080
   HTTPS_PROXY=http://proxy.company.com:8080
   ```

2. **Timeout-Einstellungen anpassen**:
   ```javascript
   // In configs/constants.js
   MICROSOFT_TODO_CONFIG: {
     TIMEOUT: 30000 // 30 Sekunden
   }
   ```

### Performance Issues

#### Langsame Response-Zeiten

**Diagnose**:

1. **Performance Metrics überprüfen**:
   ```bash
   curl -X GET "http://localhost:3000/api/v1/microsoft/todo/metrics"
   ```

2. **Cache Hit Rate analysieren**:
   - Niedrige Cache Hit Rate deutet auf ineffiziente Caching hin
   - Überprüfen Sie Cache-Konfiguration

**Optimierungen**:

1. **Cache-TTL anpassen**:
   ```javascript
   // In configs/constants.js
   CACHE_TTL: {
     token: 3300,    // 55 Minuten
     lists: 300,     // 5 Minuten
     tasks: 60       // 1 Minute
   }
   ```

2. **Concurrency-Limits optimieren**:
   ```javascript
   // In MicrosoftTodoService
   this.maxConcurrentRequests = 10; // Erhöhen für mehr Parallelität
   ```

#### Memory Leaks

**Symptom**:
- Stetig steigender Memory-Verbrauch
- Performance-Degradation über Zeit

**Diagnose**:
```bash
# Node.js Memory Usage
node --inspect server.js
```

**Lösungen**:

1. **Cache-Größe begrenzen**:
   ```javascript
   // Cache-Konfiguration
   maxKeys: 100 // Begrenzt Cache-Einträge
   ```

2. **Request Queue überwachen**:
   ```javascript
   // Überprüfen Sie queuedRequests in Metrics
   if (metrics.queuedRequests > 50) {
     // Warnung ausgeben
   }
   ```

## Debugging-Strategien

### 1. Schritt-für-Schritt Debugging

```javascript
// 1. Environment Variables überprüfen
console.log('MICROSOFT_TENANT_ID:', process.env.MICROSOFT_TENANT_ID ? 'SET' : 'MISSING');
console.log('MICROSOFT_CLIENT_ID:', process.env.MICROSOFT_CLIENT_ID ? 'SET' : 'MISSING');
console.log('MICROSOFT_CLIENT_SECRET:', process.env.MICROSOFT_CLIENT_SECRET ? 'SET' : 'MISSING');

// 2. Token-Abruf testen
const service = new MicrosoftTodoService();
try {
  const token = await service.getAccessToken();
  console.log('Token erfolgreich abgerufen');
} catch (error) {
  console.error('Token-Fehler:', error.message);
}

// 3. API-Aufruf testen
try {
  const result = await service.createTask('test-list-id', { title: 'Test Task' });
  console.log('Task erfolgreich erstellt');
} catch (error) {
  console.error('API-Fehler:', error.message);
}
```

### 2. Log-Korrelation

Verwenden Sie Request IDs für Log-Korrelation:

```bash
# Suchen Sie nach spezifischer Request ID in Logs
grep "req_1705312200000_abc123" logs/app.log
```

### 3. Performance-Profiling

```javascript
// Performance-Messung
const startTime = Date.now();
await service.createTask(listId, taskData);
const duration = Date.now() - startTime;
console.log(`Request Duration: ${duration}ms`);
```

## Monitoring und Alerting

### 1. Health Checks

```bash
# Einfacher Health Check
curl -f http://localhost:3000/api/v1/microsoft/todo/metrics || echo "Service Down"
```

### 2. Automated Monitoring

```javascript
// Beispiel Monitoring Script
const checkHealth = async () => {
  try {
    const response = await fetch('/api/v1/microsoft/todo/metrics');
    const metrics = await response.json();
    
    if (metrics.data.successRate < 95) {
      console.warn('Low success rate:', metrics.data.successRate);
    }
    
    if (metrics.data.averageResponseTime > 5000) {
      console.warn('High response time:', metrics.data.averageResponseTime);
    }
  } catch (error) {
    console.error('Health check failed:', error.message);
  }
};

setInterval(checkHealth, 60000); // Jede Minute
```

### 3. Alert-Konfiguration

```yaml
# Beispiel für Prometheus/Grafana Alerts
alerts:
  - name: MicrosoftTodoHighErrorRate
    condition: error_rate > 0.05
    duration: 5m
    
  - name: MicrosoftTodoSlowResponse
    condition: avg_response_time > 3000
    duration: 2m
```

## Wartung und Updates

### 1. Client Secret Rotation

```bash
# 1. Neues Secret in Azure erstellen
# 2. Environment Variable aktualisieren
export MICROSOFT_CLIENT_SECRET="new-secret-value"

# 3. Service neu starten
pm2 restart algotrader-api
```

### 2. Cache-Wartung

```javascript
// Cache manuell leeren
service.cache.flushAll();
service.responseCache.flushAll();
```

### 3. Performance-Optimierung

```javascript
// Regelmäßige Metrics-Analyse
const metrics = service.getPerformanceMetrics();
console.log('Cache Hit Rate:', metrics.cacheHitRate);
console.log('Success Rate:', metrics.successRate);
console.log('Average Response Time:', metrics.averageResponseTime);
```

## Eskalation

### Wann eskalieren?

1. **Kritische Fehler**:
   - Success Rate < 90% über 15 Minuten
   - Kompletter Service-Ausfall
   - Security-Incidents

2. **Performance-Probleme**:
   - Response Time > 10 Sekunden
   - Memory Leaks
   - Hohe CPU-Auslastung

3. **Microsoft-seitige Probleme**:
   - Microsoft Graph API Status überprüfen: https://status.office365.com/
   - Azure Service Health Dashboard

### Eskalations-Prozess

1. **Sofortmaßnahmen**:
   - Service-Restart versuchen
   - Logs sammeln und analysieren
   - Performance Metrics dokumentieren

2. **Dokumentation**:
   - Fehler-Zeitstempel
   - Request IDs
   - Error Messages
   - Betroffene Funktionalität

3. **Kontakt**:
   - Entwicklungsteam mit vollständiger Dokumentation
   - Microsoft Support (bei Azure/Graph API Problemen)

## Präventive Maßnahmen

### 1. Monitoring

- Kontinuierliche Performance-Überwachung
- Automatische Alerts bei Anomalien
- Regelmäßige Health Checks

### 2. Wartung

- Monatliche Client Secret Rotation
- Quartalsweise Performance-Reviews
- Jährliche Security-Audits

### 3. Testing

- Regelmäßige Integration Tests
- Load Testing bei Traffic-Spitzen
- Disaster Recovery Tests
