# Swagger Documentation Best Practices

This document outlines the best practices for creating and maintaining Swagger/OpenAPI documentation for the Algotrader API, based on the current implementation.

## Table of Contents

1. [Documentation Structure](#documentation-structure)
2. [File Organization](#file-organization)
3. [Path Definitions](#path-definitions)
4. [Schema Definitions](#schema-definitions)
5. [Parameter Definitions](#parameter-definitions)
6. [Response Definitions](#response-definitions)
7. [Documentation Quality](#documentation-quality)
8. [Versioning and Maintenance](#versioning-and-maintenance)

## Documentation Structure

### OpenAPI Specification

- **Version**: Use OpenAPI 3.0.0 for all API documentation
- **Info Section**: Include comprehensive API information:
  ```json
  "info": {
    "title": "ML Algotrader API",
    "version": "2.0.0",
    "description": "REST API for algorithmic trading with IG Markets and integrated AI/ML capabilities",
    "contact": {
      "name": "API Support",
      "url": "https://app.ml-algotrader.com"
    },
    "license": {
      "name": "Private License"
    }
  }
  ```
- **Servers**: Define both production and development servers:
  ```json
  "servers": [
    {
      "url": "https://api.ml-algotrader.com",
      "description": "Production server"
    },
    {
      "url": "http://localhost:8080",
      "description": "Development server"
    }
  ]
  ```
- **Tags**: Organize endpoints with descriptive tags:
  ```json
  "tags": [
    {
      "name": "IG Trading",
      "description": "Execute and manage trades on IG Markets platform"
    },
    {
      "name": "Statistics",
      "description": "Trading performance and analysis statistics"
    }
  ]
  ```

## File Organization

### Modular Structure

- **Main Swagger File**: `swagger-doc.json` serves as the entry point
- **Component Directories**: Organize definitions in `docs/swagger-definitions/`
- **Endpoint Files**: Group related endpoints in separate files (e.g., `fmp-endpoints.json`)
- **Component Files**: Group related schemas in separate files (e.g., `fmp-components.json`)

### File Naming Conventions

- Use descriptive, lowercase names with hyphens
- Follow the pattern: `[area]-[type].json`
- Examples:
  - `fmp-endpoints.json`
  - `strategy-components.json`
  - `risk-management-endpoints.json`

## Path Definitions

### Path Structure

- **RESTful Paths**: Follow RESTful conventions for path naming
- **Versioning**: Include API version in the path (e.g., `/api/v1/db/...`)
- **Resource-Based**: Organize around resources (e.g., `/symbol_setup`, `/trade_history`)

### Path Documentation

- **Summary**: Provide a concise one-line description
- **Description**: Include detailed multi-line description with:
  - Purpose of the endpoint
  - Use cases
  - Technical details
  - Performance considerations
  
Example:
```json
"/api/v1/chartdata": {
  "get": {
    "summary": "Retrieve historical OHLCV data",
    "description": "Fetches historical Open-High-Low-Close-Volume (OHLCV) candlestick data for technical analysis.\n\nTechnical Details:\n- Cached responses (5 min TTL)\n- Data adjusted for symbol precision\n- Volume in base currency units\n- Timestamps in UTC with German format option\n\nPerformance Notes:\n- Optimized database queries\n- Index-based filtering\n- Response compression for large datasets\n\nUse Cases:\n- Technical analysis charting\n- Backtesting trading strategies\n- Market pattern analysis\n- Volume profile studies\n",
    "tags": [
      "Chart Data"
    ],
    "parameters": [
      // Parameters here
    ],
    "responses": {
      // Responses here
    }
  }
}
```

## Schema Definitions

### Schema Organization

- **Common Schemas**: Define reusable schemas in the components section
- **Naming Convention**: Use PascalCase for schema names (e.g., `ValidationError`, `IGTrade`)
- **Grouping**: Group related schemas in the same file

### Schema Documentation

- **Properties**: Document all properties with:
  - Descriptive names
  - Appropriate types
  - Format specifications where applicable
  - Descriptions
  - Examples
  - Constraints (min/max values, patterns)

Example:
```json
"IGTrade": {
  "type": "object",
  "properties": {
    "dealId": {
      "type": "string",
      "description": "Unique identifier for the trade",
      "example": "DIAAABC123"
    },
    "symbol": {
      "type": "string",
      "description": "Trading instrument identifier",
      "example": "CS.D.EURUSD.TODAY.IP"
    },
    "direction": {
      "type": "string",
      "enum": [
        "BUY",
        "SELL"
      ],
      "description": "Trade direction"
    },
    "size": {
      "type": "number",
      "description": "Position size in units",
      "example": 1.5
    }
  }
}
```

### Required Properties

- Explicitly define required properties for request bodies:
```json
"required": [
  "dealId",
  "symbol",
  "direction"
]
```

## Parameter Definitions

### Parameter Types

- **Path Parameters**: For resource identifiers
  ```json
  {
    "in": "path",
    "name": "symbol",
    "required": true,
    "schema": {
      "type": "string"
    },
    "description": "Trading symbol identifier (e.g. EURUSD, GBPUSD, USDJPY)"
  }
  ```

- **Query Parameters**: For filtering, pagination, and optional parameters
  ```json
  {
    "in": "query",
    "name": "limit",
    "schema": {
      "type": "integer",
      "default": 50,
      "minimum": 1,
      "maximum": 1000
    },
    "description": "Maximum number of records to return (default: 50)"
  }
  ```

- **Request Body**: For complex data structures
  ```json
  "requestBody": {
    "required": true,
    "content": {
      "application/json": {
        "schema": {
          "$ref": "#/components/schemas/IGTradeRequest"
        }
      }
    }
  }
  ```

### Parameter Documentation

- **Descriptions**: Provide clear, concise descriptions
- **Constraints**: Document min/max values, patterns, and other constraints
- **Default Values**: Specify default values where applicable
- **Examples**: Include realistic examples

## Response Definitions

### Response Structure

- **Status Codes**: Document all possible response status codes
- **Content Types**: Specify response content types (typically `application/json`)
- **Schema References**: Reference appropriate schemas for responses

Example:
```json
"responses": {
  "200": {
    "description": "Successfully retrieved OHLCV data",
    "content": {
      "application/json": {
        "schema": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "open": {
                "type": "number",
                "format": "float",
                "description": "Opening price adjusted for symbol precision",
                "example": 1.08543
              },
              // Other properties
            }
          }
        }
      }
    }
  },
  "400": {
    "$ref": "#/components/responses/ValidationError"
  },
  "500": {
    "$ref": "#/components/responses/DatabaseError"
  }
}
```

### Common Responses

- Define reusable response objects for common scenarios:
```json
"components": {
  "responses": {
    "ValidationError": {
      "description": "Invalid input parameters",
      "content": {
        "application/json": {
          "schema": {
            "$ref": "#/components/schemas/ValidationError"
          }
        }
      }
    },
    "DatabaseError": {
      "description": "Error occurred while accessing the database",
      "content": {
        "application/json": {
          "schema": {
            "$ref": "#/components/schemas/DatabaseError"
          }
        }
      }
    }
  }
}
```

## Documentation Quality

### Descriptions

- **Endpoint Descriptions**: Include:
  - Purpose of the endpoint
  - Use cases
  - Technical details
  - Performance considerations
  - Caching behavior
  - Rate limits

- **Parameter Descriptions**: Include:
  - Purpose of the parameter
  - Valid values or ranges
  - Default values
  - Impact on the response

- **Response Descriptions**: Include:
  - Meaning of the response
  - Structure of the data
  - Potential error conditions

### Examples

- Provide realistic examples for:
  - Request parameters
  - Request bodies
  - Response bodies
  - Error responses

Example:
```json
"example": {
  "symbol": "EURUSD",
  "timeframe": 15,
  "cmd": 0,
  "volume": 1.5,
  "price": 1.08247
}
```

## Versioning and Maintenance

### API Versioning

- Include API version in the path (e.g., `/api/v1/...`)
- Document version changes in the API description
- Maintain backward compatibility when possible

### Documentation Generation

- Use the `scripts/generate-swagger-json.js` script to generate the combined Swagger documentation
- Run the script after making changes to component files
- Validate the generated documentation against the OpenAPI specification

### Documentation Testing

- Test the documentation with Swagger UI
- Ensure all examples are valid and work as expected
- Verify that all endpoints are properly documented

---

By following these best practices, you'll create comprehensive, consistent, and maintainable API documentation that helps developers understand and use the Algotrader API effectively.