### @name calendar-with-date-range
GET {{API_BASE_URL}}/api/v1/fmp/calendar?from=2023-08-01&to=2023-08-31
Content-Type: application/json

> {%
    client.test("Calendar Request with date range executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Response status is not success");
        client.assert(response.body.data && Array.isArray(response.body.data), "Data is not an array");
    });

    client.test("Calendar data contains expected fields", function() {
        if (response.body.data && response.body.data.length > 0) {
            const event = response.body.data[0];
            client.assert(event.hasOwnProperty('date'), "Missing date field");
            client.assert(event.hasOwnProperty('event'), "Missing event field");
        }
    });
%}

### @name calendar-without-dates
GET {{API_BASE_URL}}/api/v1/fmp/calendar
Content-Type: application/json

> {%
    client.test("Calendar Request without date range executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Response status is not success");
    });
%}

### @name calendar-with-only-from-date
GET {{API_BASE_URL}}/api/v1/fmp/calendar?from=2023-09-01
Content-Type: application/json

> {%
    client.test("Calendar Request with only from date executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

### @name calendar-with-only-to-date
GET {{API_BASE_URL}}/api/v1/fmp/calendar?to=2025-04-01
Content-Type: application/json

> {%
    client.test("Calendar Request with only to date executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}