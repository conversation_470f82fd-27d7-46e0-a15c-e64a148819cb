require('dotenv').config()

// Use the MariaDB Node.js Connector
var mariadb = require('mariadb');

// Create a connection pool
var pool =
    mariadb.createPool({
        host: process.env.DB_HOST,
        user: process.env.DB_USER,
        password:process.env.PASSWORD,
        database: process.env.DATABASE,
        connectionLimit: 100,         // Reduced from 200 to a more manageable number
        timezone: 'Europe/Berlin',
        compress: true,
        metaAsArray: false,
        dateStrings: true,
        connectionTimeout: 5000,     // 5 seconds timeout for connections
        acquireTimeout: 10000,       // 10 seconds timeout for acquiring connections
        idleTimeout: 60000,         // 60 seconds before idle connections are closed
        keepaliveDelay: 30000,      // Send keepalive packets every 30 seconds
        minimumIdle: 10,            // Minimum number of idle connections to maintain
        resetAfterUse: true,        // Reset connection state after it is released
        trace: process.env.NODE_ENV !== 'production', // Enable connection tracing in non-production
});

// Expose a method to establish connection with MariaDB SkySQL
module.exports = Object.freeze({
    pool: pool
});