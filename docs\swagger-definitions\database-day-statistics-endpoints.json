{"paths": {"/api/v1/db/statistics/day": {"get": {"summary": "Tagesstatistiken abrufen", "description": "Ruft Handelsstatistiken auf Tagesbasis für ein bestimmtes Konto ab, beginnend mit einem angegebenen Startdatum.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Gruppiert nach Handelstagen\n- Enthält Min/Max-<PERSON><PERSON>inn, Anzahl der Trades und Gesamtgewinn pro Tag\n- Standardmäßig werden die letzten 30 Tage abgerufen\n\nAnwendungsfälle:\n- Anzeige von täglichen Performance-Metriken im Dashboard\n- <PERSON><PERSON><PERSON> von Handelsmustern über verschiedene Tage\n- Identifizierung von besonders profitablen oder verlustbringenden Tagen\n- Berechnung von täglichen Durchschnittswerten für Performance-Berichte", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}, {"in": "query", "name": "startdate", "required": false, "schema": {"type": "string", "format": "date"}, "description": "Startdatum für die Statistiken im Format YYYY-MM-DD. Wenn nicht angegeben, werden die letzten 30 Tage verwendet.", "example": "2023-03-15"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Tagesstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DayStatistics"}}, "example": [{"date": "2023-04-15", "min_profit": -120, "max_profit": 350, "cnt_trades": 12, "sum_profit": 230}, {"date": "2023-04-16", "min_profit": -80, "max_profit": 420, "cnt_trades": 15, "sum_profit": 340}, {"date": "2023-04-17", "min_profit": -150, "max_profit": 280, "cnt_trades": 10, "sum_profit": 130}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/statistics/current_day": {"get": {"summary": "Statistiken des aktuellen Tages abrufen", "description": "Ruft Handelsstatistiken für den aktuellen Handelstag eines bestimmten Kontos ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 1 Minute)\n- Enthält nur Daten vom letzten Handelstag mit Aktivität\n- Enthält Min/Max-G<PERSON>inn, Anzahl der Trades und Gesamtgewinn\n- Enthält zusätzlich Jahr, Monat und Datum für einfache Filterung\n\nAnwendungsfälle:\n- Echtzeit-Überwachung der aktuellen Tagesperformance\n- Dashboard-Anzeige für den aktuellen Handelstag\n- Schnelle Überprüfung des Tagesergebnisses\n- Vergleich mit historischen Tageswerten", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der aktuellen Tagesstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrentDayStatistics"}}, "example": [{"YEAR": 2023, "MONTH": 4, "DATE": "2023-04-17", "min_profit": -150, "max_profit": 280, "cnt_trades": 10, "sum_profit": 130}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}