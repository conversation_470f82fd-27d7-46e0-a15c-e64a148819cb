const axios = require('axios');
const { LoggingService } = require('./logging_service');
const NodeCache = require('node-cache');
const config = require('../configs/constants').MICROSOFT_TODO_CONFIG;
const { getInstance: getCredentialManager } = require('../utils/microsoft_credential_manager');
const {
    MicrosoftGraphError,
    MicrosoftOAuthError,
    MicrosoftRateLimitError,
    MicrosoftResourceNotFoundError,
    ValidationError
} = require('../controllers/database/errors/database_errors');

/**
 * Microsoft ToDo Service for Graph API Integration
 * 
 * Provides OAuth 2.0 authentication and task management functionality
 * for Microsoft ToDo via Microsoft Graph API.
 * 
 * Features:
 * - OAuth 2.0 Client Credentials Flow
 * - Automatic token refresh and caching
 * - Retry logic with exponential backoff
 * - Comprehensive error handling and logging
 * - Rate limiting compliance
 */
class MicrosoftTodoService {
    constructor(logger) {
        this.logger = logger || LoggingService.getInstance();
        this.baseUrl = config.API_BASE_URL;
        this.authUrl = config.AUTH_URL;
        this.tenantId = config.TENANT_ID;
        this.clientId = config.CLIENT_ID;
        this.clientSecret = config.CLIENT_SECRET;
        this.userId = config.USER_ID;
        this.scopes = config.SCOPES;
        this.maxRetries = config.MAX_RETRIES;
        this.retryDelay = config.RETRY_DELAY;
        this.timeout = config.TIMEOUT;
        this.correlationId = `mstodo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Initialize secure cache for tokens and API responses
        this.cache = new NodeCache({
            stdTTL: config.CACHE_TTL.token,
            checkperiod: 60,
            useClones: false,
            deleteOnExpire: true,
            maxKeys: 100 // Limit cache size for security
        });

        // Initialize separate cache for API responses with different TTL
        this.responseCache = new NodeCache({
            stdTTL: config.CACHE_TTL.lists,
            checkperiod: 30,
            useClones: false,
            deleteOnExpire: true,
            maxKeys: 500 // Larger cache for API responses
        });

        // Security configurations
        this.maxTokenRefreshAttempts = 3;
        this.tokenRefreshAttempts = 0;
        this.lastTokenRefresh = null;
        this.minTimeBetweenRefresh = 30000; // 30 seconds minimum between token refresh attempts

        // Performance and rate limiting configurations
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.maxConcurrentRequests = 5;
        this.activeRequests = 0;
        this.rateLimitDelay = 100; // Base delay between requests in ms
        this.lastRequestTime = 0;
        this.performanceMetrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        // Initialize credential manager and validate configuration
        this.credentialManager = getCredentialManager();
        this._validateConfiguration();
    }

    /**
     * Validate required configuration parameters
     * @private
     */
    _validateConfiguration() {
        // Use credential manager for comprehensive validation
        const validation = this.credentialManager.validateCredentials();

        if (!validation.isValid) {
            const errorMessage = `Microsoft Graph API configuration validation failed: ${
                validation.missingCredentials.concat(
                    validation.invalidCredentials.map(c => c.credential)
                ).join(', ')
            }`;

            this.logger.error('Microsoft Graph API configuration validation failed', {
                missingCredentials: validation.missingCredentials,
                invalidCredentials: validation.invalidCredentials,
                correlationId: this.correlationId
            });

            throw new Error(errorMessage);
        }

        // Validate instance fields
        const requiredFields = ['tenantId', 'clientId', 'clientSecret', 'userId'];
        const missingFields = requiredFields.filter(field => !this[field]);

        if (missingFields.length > 0) {
            const error = new Error(`Missing required Microsoft ToDo configuration: ${missingFields.join(', ')}`);
            this.logger.error('Microsoft ToDo configuration validation failed', {
                missingFields,
                correlationId: this.correlationId
            });
            throw error;
        }

        // Log warnings if any
        if (validation.warnings.length > 0) {
            this.logger.warn('Microsoft Graph API configuration warnings', {
                warnings: validation.warnings,
                correlationId: this.correlationId
            });
        }

        // Audit credential access
        this.credentialManager.auditCredentialAccess('service_initialization', this.correlationId);

        this.logger.info('Microsoft Graph API configuration validated successfully', {
            correlationId: this.correlationId,
            warningCount: validation.warnings.length
        });
    }

    /**
     * Sleep for specified milliseconds
     * @private
     * @param {number} ms - Milliseconds to sleep
     */
    async _sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Enforce rate limiting compliance
     * @private
     */
    async _enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;

        if (timeSinceLastRequest < this.rateLimitDelay) {
            const waitTime = this.rateLimitDelay - timeSinceLastRequest;
            this.logger.debug('Rate limiting: waiting before next request', {
                waitTime,
                correlationId: this.correlationId
            });
            await this._sleep(waitTime);
        }

        this.lastRequestTime = Date.now();
    }

    /**
     * Check if request should be queued due to concurrency limits
     * @private
     */
    async _checkConcurrencyLimit() {
        if (this.activeRequests >= this.maxConcurrentRequests) {
            this.logger.debug('Concurrency limit reached, queueing request', {
                activeRequests: this.activeRequests,
                maxConcurrentRequests: this.maxConcurrentRequests,
                correlationId: this.correlationId
            });

            return new Promise((resolve) => {
                this.requestQueue.push(resolve);
                this._processQueue();
            });
        }

        this.activeRequests++;
    }

    /**
     * Process queued requests
     * @private
     */
    async _processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.requestQueue.length > 0 && this.activeRequests < this.maxConcurrentRequests) {
            const resolve = this.requestQueue.shift();
            this.activeRequests++;
            resolve();
        }

        this.isProcessingQueue = false;
    }

    /**
     * Release request slot and process queue
     * @private
     */
    _releaseRequestSlot() {
        this.activeRequests = Math.max(0, this.activeRequests - 1);
        this._processQueue();
    }

    /**
     * Update performance metrics
     * @private
     */
    _updatePerformanceMetrics(success, responseTime, cacheHit = false) {
        this.performanceMetrics.totalRequests++;

        if (success) {
            this.performanceMetrics.successfulRequests++;
        } else {
            this.performanceMetrics.failedRequests++;
        }

        if (cacheHit) {
            this.performanceMetrics.cacheHits++;
        } else {
            this.performanceMetrics.cacheMisses++;
        }

        // Update average response time using exponential moving average
        const alpha = 0.1; // Smoothing factor
        this.performanceMetrics.averageResponseTime =
            (alpha * responseTime) + ((1 - alpha) * this.performanceMetrics.averageResponseTime);
    }

    /**
     * Get performance metrics
     * @returns {Object} Performance metrics
     */
    getPerformanceMetrics() {
        const cacheHitRate = this.performanceMetrics.totalRequests > 0
            ? (this.performanceMetrics.cacheHits / this.performanceMetrics.totalRequests * 100).toFixed(2)
            : 0;

        const successRate = this.performanceMetrics.totalRequests > 0
            ? (this.performanceMetrics.successfulRequests / this.performanceMetrics.totalRequests * 100).toFixed(2)
            : 0;

        return {
            ...this.performanceMetrics,
            cacheHitRate: `${cacheHitRate}%`,
            successRate: `${successRate}%`,
            averageResponseTime: Math.round(this.performanceMetrics.averageResponseTime),
            activeRequests: this.activeRequests,
            queuedRequests: this.requestQueue.length
        };
    }

    /**
     * Get OAuth 2.0 access token using Client Credentials Flow
     * @returns {Promise<string>} Access token
     */
    async getAccessToken() {
        const cacheKey = 'microsoft_access_token';

        // Check cache first
        const cachedToken = this.cache.get(cacheKey);
        if (cachedToken) {
            this.logger.debug('Using cached Microsoft access token', {
                correlationId: this.correlationId,
                cacheHit: true
            });
            return cachedToken;
        }

        // Security: Prevent rapid token refresh attempts
        if (this.lastTokenRefresh &&
            (Date.now() - this.lastTokenRefresh) < this.minTimeBetweenRefresh) {
            const waitTime = this.minTimeBetweenRefresh - (Date.now() - this.lastTokenRefresh);
            this.logger.warn('Token refresh rate limited', {
                correlationId: this.correlationId,
                waitTime,
                attempts: this.tokenRefreshAttempts
            });
            await this._sleep(waitTime);
        }

        // Security: Check maximum refresh attempts
        if (this.tokenRefreshAttempts >= this.maxTokenRefreshAttempts) {
            const error = new MicrosoftOAuthError('Maximum token refresh attempts exceeded', {
                correlationId: this.correlationId,
                attempts: this.tokenRefreshAttempts,
                maxAttempts: this.maxTokenRefreshAttempts
            });
            this.logger.error('Token refresh attempts exceeded', {
                correlationId: this.correlationId,
                attempts: this.tokenRefreshAttempts
            });
            throw error;
        }

        const startTime = Date.now();
        this.lastTokenRefresh = startTime;
        this.tokenRefreshAttempts++;
        
        try {
            this.logger.info('Requesting new Microsoft access token', {
                tenantId: this.tenantId,
                correlationId: this.correlationId
            });

            const tokenUrl = `${this.authUrl}/${this.tenantId}${config.ENDPOINTS.token}`;

            // Audit credential access for token request
            this.credentialManager.auditCredentialAccess('token_request', this.correlationId);

            const requestData = new URLSearchParams({
                grant_type: 'client_credentials',
                client_id: this.clientId,
                client_secret: this.clientSecret,
                scope: this.scopes.join(' ')
            });

            const response = await axios.post(tokenUrl, requestData, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'AlgoTrader-API/1.0',
                    'X-Correlation-ID': this.correlationId,
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                timeout: this.timeout,
                maxRedirects: 0, // Security: Prevent redirect attacks
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: true, // Security: Enforce SSL certificate validation
                    secureProtocol: 'TLSv1_2_method' // Security: Enforce TLS 1.2+
                })
            });

            const { access_token, expires_in, token_type } = response.data;

            // Security: Validate token response
            if (!access_token || typeof access_token !== 'string') {
                throw new MicrosoftOAuthError('Invalid token response: missing or invalid access_token', {
                    correlationId: this.correlationId
                });
            }

            if (!expires_in || expires_in < 60) {
                throw new MicrosoftOAuthError('Invalid token response: invalid expires_in value', {
                    correlationId: this.correlationId,
                    expiresIn: expires_in
                });
            }

            if (token_type && token_type.toLowerCase() !== 'bearer') {
                this.logger.warn('Unexpected token type received', {
                    correlationId: this.correlationId,
                    tokenType: token_type
                });
            }

            // Cache token with 5 minute buffer before expiration
            const cacheTime = Math.max(expires_in - 300, 300);
            this.cache.set(cacheKey, access_token, cacheTime);

            // Reset refresh attempts on successful token acquisition
            this.tokenRefreshAttempts = 0;

            const duration = Date.now() - startTime;
            this.logger.info('Microsoft access token obtained successfully', {
                duration,
                expiresIn: expires_in,
                tokenType: token_type,
                cacheTime,
                correlationId: this.correlationId
            });

            return access_token;
            
        } catch (error) {
            const duration = Date.now() - startTime;

            // Use credential manager to mask sensitive data in error logging
            const maskedErrorData = this.credentialManager.maskSensitiveData({
                duration,
                error: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                correlationId: this.correlationId,
                url: error.config?.url,
                data: error.config?.data
            });

            this.logger.error('Failed to obtain Microsoft access token', maskedErrorData);
            throw this._handleAuthError(error);
        }
    }



    /**
     * Get the default task list (usually "Tasks" list that appears in "My Day")
     * @returns {Promise<Object>} Default list data with id and displayName
     */
    async getDefaultList() {
        const cacheKey = 'microsoft_default_list';
        const cached = this.responseCache.get(cacheKey);

        if (cached) {
            this.performanceMetrics.cacheHits++;
            this.logger.debug('Default list retrieved from cache', {
                correlationId: this.correlationId
            });
            return cached;
        }

        try {
            const accessToken = await this.getAccessToken();

            // Try to find the correct user ID
            let actualUserId = config.USER_ID;

            if (config.USER_ID) {
                // First test if the configured USER_ID works
                try {
                    const userEndpoint = `/users/${config.USER_ID}`;
                    const userResponse = await this._executeRequest('GET', userEndpoint, null, accessToken);
                    this.logger.info('✅ Configured User ID is valid', {
                        userId: userResponse.id,
                        userPrincipalName: userResponse.userPrincipalName,
                        displayName: userResponse.displayName,
                        correlationId: this.correlationId
                    });
                    actualUserId = userResponse.id;
                } catch (userError) {
                    this.logger.warn('❌ Configured USER_ID not found, trying to find user by email', {
                        error: userError.message,
                        statusCode: userError.statusCode,
                        configuredUserId: config.USER_ID,
                        correlationId: this.correlationId
                    });

                    // Try to find user by email (<EMAIL>)
                    try {
                        const emailEndpoint = `/users/<EMAIL>`;
                        const emailUserResponse = await this._executeRequest('GET', emailEndpoint, null, accessToken);
                        this.logger.info('✅ Found user by email', {
                            userId: emailUserResponse.id,
                            userPrincipalName: emailUserResponse.userPrincipalName,
                            displayName: emailUserResponse.displayName,
                            correlationId: this.correlationId
                        });
                        actualUserId = emailUserResponse.id;
                    } catch (emailError) {
                        this.logger.error('❌ Cannot find user by email either', {
                            error: emailError.message,
                            statusCode: emailError.statusCode,
                            correlationId: this.correlationId
                        });
                        throw new MicrosoftGraphError(`Cannot find user: tried ID ${config.USER_ID} <NAME_EMAIL>`, {
                            correlationId: this.correlationId,
                            statusCode: 404
                        });
                    }
                }
            } else {
                // No USER_ID configured, try to find by email
                try {
                    const emailEndpoint = `/users/<EMAIL>`;
                    const emailUserResponse = await this._executeRequest('GET', emailEndpoint, null, accessToken);
                    this.logger.info('✅ Found user by email (no USER_ID configured)', {
                        userId: emailUserResponse.id,
                        userPrincipalName: emailUserResponse.userPrincipalName,
                        displayName: emailUserResponse.displayName,
                        correlationId: this.correlationId
                    });
                    actualUserId = emailUserResponse.id;
                } catch (emailError) {
                    throw new MicrosoftGraphError('MICROSOFT_USER_ID not configured and cannot find user <NAME_EMAIL>', {
                        correlationId: this.correlationId,
                        statusCode: 404
                    });
                }
            }

            // Use application method with actual user ID
            const endpoint = config.ENDPOINTS.lists.replace('{userId}', actualUserId);

            this.logger.info('Retrieving Microsoft ToDo lists to find default', {
                method: 'application',
                userId: actualUserId,
                endpoint: endpoint,
                correlationId: this.correlationId
            });

            const response = await this._executeRequest('GET', endpoint, null, accessToken);

            // Debug log the response structure
            this.logger.debug('Microsoft Graph API lists response', {
                responseType: typeof response,
                hasValue: !!response?.value,
                valueType: typeof response?.value,
                isArray: Array.isArray(response?.value),
                valueLength: response?.value?.length,
                responseKeys: response ? Object.keys(response) : [],
                correlationId: this.correlationId
            });

            // Log detailed error information for debugging
            if (response?.error) {
                this.logger.error('Detailed Microsoft Graph API error', {
                    errorCode: response.error.code,
                    errorMessage: response.error.message,
                    errorDetails: response.error.details,
                    innerError: response.error.innerError,
                    correlationId: this.correlationId
                });
            }

            // Check if response contains an error
            if (response?.error) {
                this.logger.error('Microsoft Graph API returned error response', {
                    error: response.error,
                    correlationId: this.correlationId
                });
                throw new MicrosoftGraphError(`Microsoft Graph API error: ${response.error.message || 'Unknown error'}`, {
                    correlationId: this.correlationId,
                    statusCode: response.error.code || 500,
                    graphErrorCode: response.error.code || 'UnknownError'
                });
            }

            // Validate response structure
            if (!response || !response.value || !Array.isArray(response.value)) {
                this.logger.error('Invalid response structure from Microsoft Graph API', {
                    response: response,
                    correlationId: this.correlationId
                });
                throw new MicrosoftGraphError('Invalid response structure from Microsoft Graph API', {
                    correlationId: this.correlationId,
                    statusCode: 500,
                    graphErrorCode: 'InvalidResponse'
                });
            }

            // Debug log all available lists
            this.logger.debug('Available Microsoft ToDo lists', {
                lists: response.value.map(list => ({
                    id: list.id,
                    displayName: list.displayName,
                    wellKnownListName: list.wellKnownListName,
                    isOwner: list.isOwner
                })),
                correlationId: this.correlationId
            });

            // Find the default list (prioritize wellKnownListName "defaultList")
            let defaultList = response.value.find(list =>
                list.wellKnownListName === 'defaultList'
            );

            // If not found by wellKnownListName, try by display name
            if (!defaultList) {
                defaultList = response.value.find(list =>
                    list.displayName === 'Tasks' ||
                    list.displayName === 'Aufgaben'
                );
            }

            // If no default list found, use the first list
            if (!defaultList && response.value.length > 0) {
                defaultList = response.value[0];
                this.logger.warn('No default list found, using first available list', {
                    listId: defaultList.id,
                    displayName: defaultList.displayName,
                    correlationId: this.correlationId
                });
            }

            if (!defaultList) {
                throw new MicrosoftResourceNotFoundError('No task lists found in Microsoft ToDo', {
                    correlationId: this.correlationId
                });
            }

            // Cache the default list
            this.responseCache.set(cacheKey, defaultList, config.CACHE_TTL.lists);
            this.performanceMetrics.cacheMisses++;

            this.logger.info('Default Microsoft ToDo list found', {
                listId: defaultList.id,
                displayName: defaultList.displayName,
                wellKnownListName: defaultList.wellKnownListName,
                correlationId: this.correlationId
            });

            return defaultList;

        } catch (error) {
            this.logger.error('Failed to retrieve default Microsoft ToDo list', {
                error: error.message,
                correlationId: this.correlationId
            });
            throw this._handleError(error);
        }
    }

    /**
     * Create a new task in Microsoft ToDo
     * @param {string} listId - ToDo list ID
     * @param {Object} taskData - Task data
     * @param {string} taskData.title - Task title (required)
     * @param {string} [taskData.body] - Task body/description
     * @param {string} [taskData.dueDateTime] - Due date in ISO format
     * @param {string} [taskData.importance] - Task importance (low, normal, high)
     * @param {string[]} [taskData.categories] - Task categories
     * @returns {Promise<Object>} Created task data
     */
    async createTask(listId, taskData) {
        this._validateTaskData(taskData);
        
        let retries = 0;
        let lastError = null;

        while (retries <= this.maxRetries) {
            try {
                const accessToken = await this.getAccessToken();
                const endpoint = config.ENDPOINTS.tasks.replace('{userId}', config.USER_ID).replace('{listId}', listId);

                // Prepare task data for Microsoft Graph API
                const graphTaskData = {
                    title: taskData.title
                };

                // Add optional fields if provided
                if (taskData.body) {
                    // Ensure body.content is a string, not an object
                    const bodyContent = typeof taskData.body === 'string' ? taskData.body :
                                       (taskData.body.content || JSON.stringify(taskData.body));

                    graphTaskData.body = {
                        content: bodyContent,
                        contentType: 'text'
                    };
                }

                if (taskData.dueDateTime) {
                    graphTaskData.dueDateTime = {
                        dateTime: taskData.dueDateTime,
                        timeZone: 'UTC'
                    };
                }

                if (taskData.importance) {
                    graphTaskData.importance = taskData.importance;
                }

                if (taskData.categories && Array.isArray(taskData.categories)) {
                    graphTaskData.categories = taskData.categories;
                }
                
                this.logger.info('Creating Microsoft ToDo task', {
                    listId,
                    title: taskData.title,
                    taskDataJSON: JSON.stringify(graphTaskData, null, 2),
                    retryCount: retries,
                    correlationId: this.correlationId
                });

                const response = await this._executeRequest('POST', endpoint, graphTaskData, accessToken);

                // Check if the response contains an error
                if (response.error) {
                    this.logger.error('Microsoft Graph API returned error', {
                        error: response.error,
                        errorCode: response.error.code,
                        errorMessage: response.error.message,
                        correlationId: this.correlationId
                    });
                    throw new MicrosoftGraphError(
                        `Microsoft Graph API error: ${response.error.message}`,
                        {
                            correlationId: this.correlationId,
                            statusCode: 400,
                            graphErrorCode: response.error.code
                        }
                    );
                }

                this.logger.info('Microsoft ToDo task created successfully', {
                    taskId: response.id,
                    title: response.title,
                    correlationId: this.correlationId
                });

                // Debug log the full response to see what Microsoft Graph API returns
                this.logger.error('Full Microsoft Graph API task response (with error)', {
                    response: JSON.stringify(response, null, 2),
                    responseKeys: Object.keys(response),
                    correlationId: this.correlationId
                });

                // Normalize the response to ensure all expected fields are present
                const normalizedTask = {
                    id: response.id,
                    title: response.title,
                    body: response.body?.content || taskData.body || null,
                    status: response.status || 'notStarted',
                    importance: response.importance || 'normal',
                    createdDateTime: response.createdDateTime,
                    lastModifiedDateTime: response.lastModifiedDateTime,
                    dueDateTime: response.dueDateTime?.dateTime || null,
                    categories: response.categories || [],
                    webUrl: response.webUrl || null,
                    listId: listId
                };

                this.logger.debug('Normalized task response', {
                    normalizedTask: normalizedTask,
                    correlationId: this.correlationId
                });

                return normalizedTask;
                
            } catch (error) {
                lastError = error;
                
                // Handle rate limiting
                if (error.response?.status === 429) {
                    const retryAfter = error.response.headers['retry-after'] || this.retryDelay / 1000;
                    this.logger.warn('Microsoft Graph API rate limit exceeded', {
                        listId,
                        retryCount: retries,
                        retryAfter,
                        correlationId: this.correlationId
                    });
                    
                    if (retries < this.maxRetries) {
                        await this._sleep(retryAfter * 1000);
                        retries++;
                        continue;
                    }
                }
                
                // Handle server errors with retry
                if (error.response?.status >= 500 && retries < this.maxRetries) {
                    this.logger.warn('Microsoft Graph API server error, retrying', {
                        listId,
                        retryCount: retries,
                        status: error.response.status,
                        correlationId: this.correlationId
                    });
                    await this._sleep(this.retryDelay * Math.pow(2, retries));
                    retries++;
                    continue;
                }
                
                // Handle authentication errors
                if (error.response?.status === 401) {
                    this.logger.warn('Microsoft Graph API authentication failed, clearing token cache', {
                        correlationId: this.correlationId
                    });
                    this.cache.del('microsoft_access_token');
                    
                    if (retries < this.maxRetries) {
                        retries++;
                        continue;
                    }
                }
                
                throw this._handleError(error);
            }
        }

        throw lastError;
    }

    /**
     * Create a new task in the default Microsoft ToDo list (appears in "My Day")
     * @param {Object} taskData - Task data
     * @param {string} taskData.title - Task title (required)
     * @param {string} [taskData.body] - Task body/description
     * @param {string} [taskData.dueDateTime] - Due date in ISO format
     * @param {string} [taskData.importance] - Task importance (low, normal, high)
     * @param {string[]} [taskData.categories] - Task categories
     * @returns {Promise<Object>} Created task data with list information
     */
    async createTaskInDefaultList(taskData) {
        try {
            // Get the default list first
            const defaultList = await this.getDefaultList();

            this.logger.info('Creating task in default Microsoft ToDo list', {
                listId: defaultList.id,
                listName: defaultList.displayName,
                taskTitle: taskData.title,
                correlationId: this.correlationId
            });

            // Create the task in the default list
            const task = await this.createTask(defaultList.id, taskData);

            // Return task with list information
            return {
                ...task,
                listInfo: {
                    id: defaultList.id,
                    displayName: defaultList.displayName,
                    wellKnownListName: defaultList.wellKnownListName || 'defaultList',
                    isDefaultList: defaultList.wellKnownListName === 'defaultList'
                }
            };

        } catch (error) {
            this.logger.error('Failed to create task in default list', {
                error: error.message,
                taskTitle: taskData.title,
                correlationId: this.correlationId
            });
            throw error;
        }
    }

    /**
     * Generate cache key for API responses
     * @private
     */
    _generateCacheKey(method, endpoint, data) {
        const dataHash = data ? JSON.stringify(data) : '';
        return `${method}_${endpoint}_${Buffer.from(dataHash).toString('base64').substring(0, 16)}`;
    }

    /**
     * Check response cache for GET requests
     * @private
     */
    _checkResponseCache(method, endpoint, data) {
        if (method !== 'GET') return null;

        const cacheKey = this._generateCacheKey(method, endpoint, data);
        const cachedResponse = this.responseCache.get(cacheKey);

        if (cachedResponse) {
            this.logger.debug('Response cache hit', {
                endpoint,
                cacheKey,
                correlationId: this.correlationId
            });
            return cachedResponse;
        }

        return null;
    }

    /**
     * Store response in cache for GET requests
     * @private
     */
    _storeResponseCache(method, endpoint, data, response) {
        if (method !== 'GET') return;

        const cacheKey = this._generateCacheKey(method, endpoint, data);
        const ttl = endpoint.includes('/lists') ? config.CACHE_TTL.lists : config.CACHE_TTL.tasks;

        this.responseCache.set(cacheKey, response, ttl);

        this.logger.debug('Response cached', {
            endpoint,
            cacheKey,
            ttl,
            correlationId: this.correlationId
        });
    }

    /**
     * Execute HTTP request to Microsoft Graph API with performance optimization
     * @private
     */
    async _executeRequest(method, endpoint, data, accessToken) {
        const url = `${this.baseUrl}${endpoint}`;
        const startTime = Date.now();

        try {
            // Check response cache for GET requests
            const cachedResponse = this._checkResponseCache(method, endpoint, data);
            if (cachedResponse) {
                const duration = Date.now() - startTime;
                this._updatePerformanceMetrics(true, duration, true);
                return cachedResponse;
            }

            // Check concurrency limits and queue if necessary
            await this._checkConcurrencyLimit();

            // Enforce rate limiting
            await this._enforceRateLimit();
            const config = {
                method,
                url,
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'AlgoTrader-API/1.0',
                    'X-Correlation-ID': this.correlationId,
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                },
                timeout: this.timeout,
                maxRedirects: 0, // Security: Prevent redirect attacks
                validateStatus: (status) => status < 500, // Handle 4xx errors gracefully
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: true, // Security: Enforce SSL certificate validation
                    secureProtocol: 'TLSv1_2_method' // Security: Enforce TLS 1.2+
                })
            };

            if (['POST', 'PUT', 'PATCH'].includes(method) && data) {
                config.data = data;
            } else if (method === 'GET' && data) {
                config.params = data;
            }

            const response = await axios(config);

            const duration = Date.now() - startTime;

            // Store response in cache for GET requests
            this._storeResponseCache(method, endpoint, data, response.data);

            // Update performance metrics
            this._updatePerformanceMetrics(true, duration, false);

            // Release request slot
            this._releaseRequestSlot();

            this.logger.debug('Microsoft Graph API request successful', {
                method,
                endpoint,
                duration,
                statusCode: response.status,
                correlationId: this.correlationId,
                activeRequests: this.activeRequests
            });

            return response.data;
            
        } catch (error) {
            const duration = Date.now() - startTime;

            // Update performance metrics for failed request
            this._updatePerformanceMetrics(false, duration, false);

            // Release request slot
            this._releaseRequestSlot();

            this.logger.error('Microsoft Graph API request failed', {
                method,
                endpoint,
                duration,
                error: error.message,
                status: error.response?.status,
                correlationId: this.correlationId,
                activeRequests: this.activeRequests
            });

            throw this._handleError(error);
        }
    }

    /**
     * Validate task data before creation
     * @private
     */
    _validateTaskData(taskData) {
        if (!taskData || typeof taskData !== 'object') {
            throw new ValidationError('Task data must be an object', {
                correlationId: this.correlationId
            });
        }

        if (!taskData.title || typeof taskData.title !== 'string' || taskData.title.trim().length === 0) {
            throw new ValidationError('Task title is required and must be a non-empty string', {
                correlationId: this.correlationId
            });
        }

        if (taskData.importance && !Object.values(config.TASK_IMPORTANCE).includes(taskData.importance)) {
            throw new ValidationError(`Invalid task importance. Must be one of: ${Object.values(config.TASK_IMPORTANCE).join(', ')}`, {
                correlationId: this.correlationId,
                providedValue: taskData.importance,
                allowedValues: Object.values(config.TASK_IMPORTANCE)
            });
        }

        if (taskData.dueDateTime && !this._isValidISODate(taskData.dueDateTime)) {
            throw new ValidationError('Due date must be in valid ISO 8601 format', {
                correlationId: this.correlationId,
                providedValue: taskData.dueDateTime
            });
        }
    }

    /**
     * Validate ISO 8601 date format
     * @private
     */
    _isValidISODate(dateString) {
        if (!dateString || typeof dateString !== 'string') {
            return false;
        }

        // Check if it's a valid date
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return false;
        }

        // Check if it matches common ISO 8601 formats
        const iso8601Regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
        return iso8601Regex.test(dateString);
    }

    /**
     * Handle authentication errors
     * @private
     */
    _handleAuthError(error) {
        if (error.response) {
            const { status, data } = error.response;
            const oauthError = data.error || 'unknown_error';
            const errorDescription = data.error_description || data.error || 'Authentication failed';

            return new MicrosoftOAuthError(`Microsoft OAuth error: ${status} - ${errorDescription}`, {
                statusCode: status,
                oauthError: oauthError,
                errorDescription: errorDescription,
                correlationId: this.correlationId,
                responseData: data
            });
        }
        if (error.request) {
            return new MicrosoftOAuthError(`Microsoft OAuth request failed: ${error.message}`, {
                statusCode: 500,
                oauthError: 'network_error',
                correlationId: this.correlationId,
                originalError: error.message
            });
        }
        return new MicrosoftOAuthError(`Microsoft OAuth error: ${error.message}`, {
            statusCode: 500,
            oauthError: 'unknown_error',
            correlationId: this.correlationId,
            originalError: error.message
        });
    }

    /**
     * Handle API errors
     * @private
     */
    _handleError(error) {
        if (error.response) {
            const { status, data } = error.response;
            const graphErrorCode = data.error?.code || 'UnknownError';
            const errorMessage = data.error?.message || 'Microsoft Graph API error';

            // Handle specific error types
            if (status === 429) {
                const retryAfter = error.response.headers['retry-after'] || 60;
                return new MicrosoftRateLimitError(`Rate limit exceeded: ${errorMessage}`, {
                    statusCode: status,
                    retryAfter: parseInt(retryAfter),
                    correlationId: this.correlationId,
                    graphErrorCode: graphErrorCode
                });
            }

            if (status === 404) {
                let resourceType = 'unknown';
                if (errorMessage.toLowerCase().includes('list')) {
                    resourceType = 'todoList';
                } else if (errorMessage.toLowerCase().includes('task')) {
                    resourceType = 'task';
                } else if (errorMessage.toLowerCase().includes('user')) {
                    resourceType = 'user';
                }

                return new MicrosoftResourceNotFoundError(`Resource not found: ${errorMessage}`, {
                    statusCode: status,
                    resourceType: resourceType,
                    correlationId: this.correlationId,
                    graphErrorCode: graphErrorCode
                });
            }

            return new MicrosoftGraphError(`Microsoft Graph API error: ${status} - ${errorMessage}`, {
                statusCode: status,
                graphErrorCode: graphErrorCode,
                correlationId: this.correlationId,
                responseData: data
            });
        }

        if (error.request) {
            return new MicrosoftGraphError(`Microsoft Graph API request failed: ${error.message}`, {
                statusCode: 500,
                graphErrorCode: 'NetworkError',
                correlationId: this.correlationId,
                originalError: error.message
            });
        }

        return new MicrosoftGraphError(`Microsoft Graph API error: ${error.message}`, {
            statusCode: 500,
            graphErrorCode: 'UnknownError',
            correlationId: this.correlationId,
            originalError: error.message
        });
    }

    /**
     * Sleep helper for retry delays
     * @private
     */
    _sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = MicrosoftTodoService;
