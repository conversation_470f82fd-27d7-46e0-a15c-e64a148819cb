const axios = require('axios');
const { LoggingService } = require('./logging_service');
const NodeCache = require('node-cache');
const config = require('../configs/constants').MICROSOFT_TODO_CONFIG;

/**
 * Microsoft ToDo Service for Graph API Integration
 * 
 * Provides OAuth 2.0 authentication and task management functionality
 * for Microsoft ToDo via Microsoft Graph API.
 * 
 * Features:
 * - OAuth 2.0 Client Credentials Flow
 * - Automatic token refresh and caching
 * - Retry logic with exponential backoff
 * - Comprehensive error handling and logging
 * - Rate limiting compliance
 */
class MicrosoftTodoService {
    constructor(logger) {
        this.logger = logger || LoggingService.getInstance();
        this.baseUrl = config.API_BASE_URL;
        this.authUrl = config.AUTH_URL;
        this.tenantId = config.TENANT_ID;
        this.clientId = config.CLIENT_ID;
        this.clientSecret = config.CLIENT_SECRET;
        this.scopes = config.SCOPES;
        this.maxRetries = config.MAX_RETRIES;
        this.retryDelay = config.RETRY_DELAY;
        this.timeout = config.TIMEOUT;
        this.correlationId = `mstodo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Initialize cache for tokens and API responses
        this.cache = new NodeCache({
            stdTTL: config.CACHE_TTL.token,
            checkperiod: 60,
            useClones: false
        });
        
        // Validate configuration
        this._validateConfiguration();
    }

    /**
     * Validate required configuration parameters
     * @private
     */
    _validateConfiguration() {
        const requiredFields = ['tenantId', 'clientId', 'clientSecret'];
        const missingFields = requiredFields.filter(field => !this[field]);
        
        if (missingFields.length > 0) {
            const error = new Error(`Missing required Microsoft ToDo configuration: ${missingFields.join(', ')}`);
            this.logger.error('Microsoft ToDo configuration validation failed', {
                missingFields,
                correlationId: this.correlationId
            });
            throw error;
        }
    }

    /**
     * Get OAuth 2.0 access token using Client Credentials Flow
     * @returns {Promise<string>} Access token
     */
    async getAccessToken() {
        const cacheKey = 'microsoft_access_token';
        
        // Check cache first
        const cachedToken = this.cache.get(cacheKey);
        if (cachedToken) {
            this.logger.debug('Using cached Microsoft access token', {
                correlationId: this.correlationId
            });
            return cachedToken;
        }

        const startTime = Date.now();
        
        try {
            this.logger.info('Requesting new Microsoft access token', {
                tenantId: this.tenantId,
                correlationId: this.correlationId
            });

            const tokenUrl = `${this.authUrl}/${this.tenantId}${config.ENDPOINTS.token}`;
            
            const response = await axios.post(tokenUrl, new URLSearchParams({
                grant_type: 'client_credentials',
                client_id: this.clientId,
                client_secret: this.clientSecret,
                scope: this.scopes.join(' ')
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'AlgoTrader-API/1.0'
                },
                timeout: this.timeout
            });

            const { access_token, expires_in } = response.data;
            
            // Cache token with 5 minute buffer before expiration
            const cacheTime = Math.max(expires_in - 300, 300);
            this.cache.set(cacheKey, access_token, cacheTime);

            const duration = Date.now() - startTime;
            this.logger.info('Microsoft access token obtained successfully', {
                duration,
                expiresIn: expires_in,
                correlationId: this.correlationId
            });

            return access_token;
            
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Failed to obtain Microsoft access token', {
                duration,
                error: error.message,
                status: error.response?.status,
                correlationId: this.correlationId
            });
            throw this._handleAuthError(error);
        }
    }

    /**
     * Create a new task in Microsoft ToDo
     * @param {string} listId - ToDo list ID
     * @param {Object} taskData - Task data
     * @param {string} taskData.title - Task title (required)
     * @param {string} [taskData.body] - Task body/description
     * @param {string} [taskData.dueDateTime] - Due date in ISO format
     * @param {string} [taskData.importance] - Task importance (low, normal, high)
     * @param {string[]} [taskData.categories] - Task categories
     * @returns {Promise<Object>} Created task data
     */
    async createTask(listId, taskData) {
        this._validateTaskData(taskData);
        
        let retries = 0;
        let lastError = null;

        while (retries <= this.maxRetries) {
            try {
                const accessToken = await this.getAccessToken();
                const endpoint = config.ENDPOINTS.tasks.replace('{listId}', listId);
                
                this.logger.info('Creating Microsoft ToDo task', {
                    listId,
                    title: taskData.title,
                    retryCount: retries,
                    correlationId: this.correlationId
                });

                const response = await this._executeRequest('POST', endpoint, taskData, accessToken);
                
                this.logger.info('Microsoft ToDo task created successfully', {
                    taskId: response.id,
                    title: response.title,
                    correlationId: this.correlationId
                });

                return response;
                
            } catch (error) {
                lastError = error;
                
                // Handle rate limiting
                if (error.response?.status === 429) {
                    const retryAfter = error.response.headers['retry-after'] || this.retryDelay / 1000;
                    this.logger.warn('Microsoft Graph API rate limit exceeded', {
                        listId,
                        retryCount: retries,
                        retryAfter,
                        correlationId: this.correlationId
                    });
                    
                    if (retries < this.maxRetries) {
                        await this._sleep(retryAfter * 1000);
                        retries++;
                        continue;
                    }
                }
                
                // Handle server errors with retry
                if (error.response?.status >= 500 && retries < this.maxRetries) {
                    this.logger.warn('Microsoft Graph API server error, retrying', {
                        listId,
                        retryCount: retries,
                        status: error.response.status,
                        correlationId: this.correlationId
                    });
                    await this._sleep(this.retryDelay * Math.pow(2, retries));
                    retries++;
                    continue;
                }
                
                // Handle authentication errors
                if (error.response?.status === 401) {
                    this.logger.warn('Microsoft Graph API authentication failed, clearing token cache', {
                        correlationId: this.correlationId
                    });
                    this.cache.del('microsoft_access_token');
                    
                    if (retries < this.maxRetries) {
                        retries++;
                        continue;
                    }
                }
                
                throw this._handleError(error);
            }
        }

        throw lastError;
    }

    /**
     * Execute HTTP request to Microsoft Graph API
     * @private
     */
    async _executeRequest(method, endpoint, data, accessToken) {
        const url = `${this.baseUrl}${endpoint}`;
        const startTime = Date.now();

        try {
            const config = {
                method,
                url,
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'AlgoTrader-API/1.0'
                },
                timeout: this.timeout
            };

            if (['POST', 'PUT', 'PATCH'].includes(method) && data) {
                config.data = data;
            } else if (method === 'GET' && data) {
                config.params = data;
            }

            const response = await axios(config);

            const duration = Date.now() - startTime;
            this.logger.debug('Microsoft Graph API request successful', {
                method,
                endpoint,
                duration,
                statusCode: response.status,
                correlationId: this.correlationId
            });

            return response.data;
            
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Microsoft Graph API request failed', {
                method,
                endpoint,
                duration,
                error: error.message,
                status: error.response?.status,
                correlationId: this.correlationId
            });
            throw error;
        }
    }

    /**
     * Validate task data before creation
     * @private
     */
    _validateTaskData(taskData) {
        if (!taskData || typeof taskData !== 'object') {
            throw new Error('Task data must be an object');
        }
        
        if (!taskData.title || typeof taskData.title !== 'string' || taskData.title.trim().length === 0) {
            throw new Error('Task title is required and must be a non-empty string');
        }
        
        if (taskData.importance && !Object.values(config.TASK_IMPORTANCE).includes(taskData.importance)) {
            throw new Error(`Invalid task importance. Must be one of: ${Object.values(config.TASK_IMPORTANCE).join(', ')}`);
        }
        
        if (taskData.dueDateTime && !this._isValidISODate(taskData.dueDateTime)) {
            throw new Error('Due date must be in valid ISO 8601 format');
        }
    }

    /**
     * Validate ISO 8601 date format
     * @private
     */
    _isValidISODate(dateString) {
        const date = new Date(dateString);
        return date instanceof Date && !isNaN(date) && dateString === date.toISOString();
    }

    /**
     * Handle authentication errors
     * @private
     */
    _handleAuthError(error) {
        if (error.response) {
            const { status, data } = error.response;
            return new Error(`Microsoft OAuth error: ${status} - ${data.error_description || data.error || JSON.stringify(data)}`);
        }
        if (error.request) {
            return new Error(`Microsoft OAuth request failed: ${error.message}`);
        }
        return new Error(`Microsoft OAuth error: ${error.message}`);
    }

    /**
     * Handle API errors
     * @private
     */
    _handleError(error) {
        if (error.response) {
            const { status, data } = error.response;
            return new Error(`Microsoft Graph API error: ${status} - ${data.error?.message || JSON.stringify(data)}`);
        }
        if (error.request) {
            return new Error(`Microsoft Graph API request failed: ${error.message}`);
        }
        return new Error(`Microsoft Graph API error: ${error.message}`);
    }

    /**
     * Sleep helper for retry delays
     * @private
     */
    _sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = MicrosoftTodoService;
