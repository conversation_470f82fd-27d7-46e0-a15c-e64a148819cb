const { LoggingService } = require('../../services/logging_service');
const FMPNewsService = require('../../services/fmp_news_service');

class FMPNewsController {
    constructor() {
        this.logger = LoggingService.getInstance();
        this.newsService = new FMPNewsService(this.logger);
    }


    async fetchNews(req, res) {
        try {
            const size = parseInt(req.query.size) || 50;
            const page = parseInt(req.query.page) || 0;
            
            this.logger.info('News fetch request received', {
                size,
                page,
                requestId: req.id
            });

            const newsItems = await this.newsService.getNews(size, page);
            
            return res.json({
                status: 'success',
                data: newsItems,
                count: newsItems.length,
                page,
                size
            });
        } catch (error) {
            this.logger.error('News fetch failed', {
                error: error.message,
                requestId: req.id
            });

            return res.status(500).json({
                status: 'error',
                message: 'Failed to fetch news data',
                error: error.message
            });
        }
    }

  
}

module.exports = new FMPNewsController();