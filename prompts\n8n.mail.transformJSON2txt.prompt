You are tasked with transforming a JSON-formatted text into a readable Markdown format. The JSON content will be provided to you, and you need to convert it into a well-structured Markdown document with headings, lists, and text.

Here is the JSON content you need to transform:

<json_content>
{{INHALT}}
</json_content>

Follow these steps to transform the JSON content into Markdown:

1. Parse the JSON content and identify the main sections or keys.
2. Create appropriate headings for each main section using Markdown syntax (e.g., # for h1, ## for h2, etc.).
3. For nested objects, create subheadings or lists as appropriate.
4. Convert arrays into bullet point lists using the Markdown syntax (- or *).
5. Present key-value pairs as regular text, with the key in bold (using **key**) followed by the value.
6. Ensure proper spacing and line breaks between sections for readability.

Your output should be formatted in Markdown and structured as follows:

1. Main headings (h1) for top-level keys
2. Subheadings (h2, h3, etc.) for nested objects
3. Bullet point lists for arrays
4. Bold text for keys in key-value pairs
5. Regular text for values

Here's a simple example of how your output might look:

```markdown
# Main Section

## Subsection 1

- List item 1
- List item 2
- List item 3

## Subsection 2

**Key 1**: Value 1
**Key 2**: Value 2

### Nested Subsection

- Nested list item 1
- Nested list item 2
```

Please provide ONLY your Markdown-formatted output.