const FMPBaseService = require('./fmp_base_service');
const { LoggingService } = require('./logging_service');

class FMPChartService extends FMPBaseService {
    constructor(logger) {
        super(logger || LoggingService.getInstance());
    }


    async getIntradayChart(timeframe, symbol, fromDate, toDate, extended = false) {
        if (!timeframe || !symbol) {
            throw new Error('Timeframe and symbol are required parameters');
        }

        // Validate timeframe
        const validTimeframes = ['1min', '5min', '15min', '30min', '1hour', '4hour','1day'];
        if (!validTimeframes.includes(timeframe)) {
            throw new Error(`Invalid timeframe: ${timeframe}. Must be one of: ${validTimeframes.join(', ')}`);
        }

        this.logger.info('Fetching FMP intraday chart data', {
            timeframe,
            symbol,
            fromDate,
            toDate,
            extended,
            correlationId: this.correlationId
        });

        try {
            // Construct the endpoint with the dynamic timeframe and symbol
            const endpoint = `v3/historical-chart/${timeframe}/${symbol}`;
            
            // Prepare query parameters
            const params = {};
            if (fromDate) params.from = fromDate;
            if (toDate) params.to = toDate;
            if (extended) params.extended = extended.toString();

            const data = await this.makeRequest(endpoint, params);
            
            if (!Array.isArray(data) || data.length === 0) {
                this.logger.warn('No chart data received', {
                    timeframe,
                    symbol,
                    correlationId: this.correlationId
                });
                return [];
            }

            this.logger.info('Chart data fetched successfully', {
                timeframe,
                symbol,
                dataPoints: data.length,
                correlationId: this.correlationId
            });

            return data;
        } catch (error) {
            this.logger.error('Failed to fetch chart data', {
                timeframe,
                symbol,
                error: error.message,
                correlationId: this.correlationId
            });
            throw error;
        }
    }
}

module.exports = FMPChartService;