const db = require('../../../configs/config_db');
var moment = require('moment-timezone');

function extractSymbol(text) {
    if (text!=undefined && text.includes(':')) {
        const parts = text.split(':');
        if (parts.length < 2) {
            throw new Error('Ungültiges Format');
        }
        return parts[1].trim().split(' ')[0];
    } else return "undefined";
}

async function transferXTBTrades2LogsProfitAsEntryExitPoints()  {
    const queryXTBEntries =
        `INSERT IGNORE INTO mirror_trading_logs_profit (targetRefID, log_timestamp, log_order2, log_order, profit, status)
            SELECT a.refId AS targetRefID, xtb.entry_time AS log_timestamp, xtb.xtb_orderid AS log_order2, xtb.orderRef AS log_order, 0 AS profit, 'entry' AS status
            FROM \`trades_history\` xtb, accounts a
            WHERE xtb.xtb_orderid IS NOT NULL
              AND xtb.xtb_orderid>0
              AND xtb.account=a.account_id
              AND xtb.exit_time>current_date()-2`;

    const queryXTBExits =
        `INSERT IGNORE INTO mirror_trading_logs_profit (targetRefID, log_timestamp, log_order2, log_order, profit, status, sl)
            SELECT a.refId AS targetRefID, xtb.exit_time AS log_timestamp, xtb.xtb_orderid AS log_order2, xtb.orderRef AS log_order, xtb.profit AS profit, 'exit' AS status, xtb.stopp AS sl
            FROM \`trades_history\` xtb, accounts a
            WHERE xtb.xtb_orderid IS NOT NULL
              AND xtb.account=a.account_id
              AND xtb.xtb_orderid>0
              AND xtb.exit_time>current_date()-2`;

   const queryTransferFlagUpdate =
      `  UPDATE \`trades_history\` xtb
            SET xtb.transfer_timestamp = NOW()
          WHERE xtb.transfer_timestamp IS NULL
            AND xtb.exit_time>current_date()-5
            AND xtb.xtb_orderid in (select distinct log_order2 from mirror_trading_logs_profit mtlp  where mtlp.status in ('entry','exit'))`;

   const queryUpdateClosedProfit =
     ` UPDATE mirror_trading_logs_details lt
         JOIN \`trades_history\` xtb ON xtb.xtb_orderid = lt.order2 and closed_profit IS NULL OR lt.open_time = '0000-00-00'
       SET lt.closed_profit = xtb.profit,
           lt.open_time     = xtb.entry_time
       WHERE closed_profit IS NULL
          OR lt.open_time = '0000-00-00'`;

    try {
        await db.pool.query(queryXTBEntries).catch(err => console.log(err));
        await db.pool.query(queryXTBExits).catch(err => console.log(err));
        await db.pool.query(queryTransferFlagUpdate).catch(err => console.log(err));
        await db.pool.query(queryUpdateClosedProfit).catch(err => console.log(err));
        return 'Data processed successfully';
    } catch (err) {
        console.log(err);
        return 'transferXTBTrades2LogsProfitAsEntryExitPoints: Data processed: '+ JSON.stringify(err);
    }
}

async function transferMirrorTradingLogs2LogsProfit() {
    const query = 'SELECT * FROM mirror_trading_logs WHERE transfer_timestamp IS NULL and timestamp>=current_date()-5 ORDER BY TIMESTAMP desc';
    try {
        const rows = await db.pool.query(query);
        console.log('[db_mirrorTradingLogs][transferMirrorTradingLogs2LogsProfit] Data selected from mirror_trading_logs');

        let dataChangedCnt = 0;
        const existingDetailOrders = new Set();

        for (const [index, row] of rows.entries()) {
            const log = JSON.parse(row.log);
            const root = Object.values(log).find(v => v.iterateOverAllSourceTrades || v.modifyStoppLevel);
            if (!root) continue;

            const details = root.iterateOverAllSourceTrades || { modifyStoppLevel: root.modifyStoppLevel };
            const date = moment(row.timestamp).tz('Europe/Berlin');
            const dateString = date.format('YYYY-MM-DD HH:mm:ss');

            if (details.modifyStoppLevel) {
                dataChangedCnt += await processModifyStoppLevel(details.modifyStoppLevel, row, dateString, existingDetailOrders);
            } else {
                dataChangedCnt += await processTradeDetails(details, row, dateString, existingDetailOrders);
            }

            await db.pool.query(`UPDATE mirror_trading_logs SET transfer_timestamp = NOW() WHERE id = ${row.ID}`).catch(console.log);

            if (index % 10 === 0) {
                console.log(`[db_mirrorTradingLogs][transferMirrorTradingLogs2LogsProfit] Update-State: ${Math.round((index + 1) * 100 / rows.length)}%`);
            }
        }

        console.log(`[db_mirrorTradingLogs][transferMirrorTradingLogs2LogsProfit] Data processed: ${dataChangedCnt} rows changed`);
        return `Data processed: ${dataChangedCnt} rows changed`;
    } catch (err) {
        console.log(err);
        return `transferMirrorTradingLogs2LogsProfit: Data processed: ${JSON.stringify(err)}`;
    }
}

/**
 * Verarbeitet Änderungen an Stop-Levels für Mirror-Trading
 * 
 * Diese Funktion verarbeitet verschiedene Arten von Stop-Level-Modifikationen:
 * - Break-Even Anpassungen
 * - Stop-Level Änderungen
 * - Profit-Level ohne interessante Levels
 * - Start-Parameter
 * - Log-Modus Einträge
 * - Fehlende Stop-Offset Anpassungen
 * 
 * @param {Object} modifyStoppLevel - Objekt mit Stop-Level Änderungen
 * @param {Object} row - Datenbank-Zeile mit Trade-Informationen
 * @param {string} dateString - Zeitstempel im Format 'YYYY-MM-DD HH:mm:ss'
 * @param {Set<number>} existingDetailOrders - Set von bereits verarbeiteten Order-IDs
 * @returns {Promise<number>} Anzahl der verarbeiteten Datensätze
 * @throws {Error} Bei Fehlern während der Datenbankoperationen
 * 
 * @example
 * const modifyData = {
 *   'break-even-reached': [{order: '123', sl: 1.1234}],
 *   'modify-stopp-level': {'456': [{new-stopp-level: 1.2345}]}
 * };
 * const processed = await processModifyStoppLevel(
 *   modifyData,
 *   rowData,
 *   '2024-11-23 10:00:00',
 *   new Set()
 * );
 */
async function processModifyStoppLevel(modifyStoppLevel, row, dateString, existingDetailOrders) {
    let dataChangedCnt = 0;

    const processArray = async (array, status) => {
        for (const tradeInfo of array) {
            dataChangedCnt += await insertTradeData(tradeInfo, row, dateString, status, existingDetailOrders);
        }
    };

    await processArray(modifyStoppLevel['break-even-reached'] || [], 'modify-break-even');
    
    if (modifyStoppLevel['modify-stopp-level']) {
        for (const [order, modifyInfo] of Object.entries(modifyStoppLevel['modify-stopp-level'])) {
            if (Array.isArray(modifyInfo) && modifyInfo.length > 0) {
                const tradeInfo = { ...modifyInfo[0], order };
                if (tradeInfo['compare-orderID']) {
                    tradeInfo.order2 = tradeInfo['compare-orderID'];
                }
                if (tradeInfo['new-stopp-level']) {
                    tradeInfo.sl = tradeInfo['new-stopp-level'];
                }
                dataChangedCnt += await insertTradeData(tradeInfo, row, dateString, 'modify-stopp', existingDetailOrders);
            }
        }
    }

    await processArray(modifyStoppLevel['profit-no-interesting-level'] || [], 'no-interesting-level');

    if (modifyStoppLevel['start-parameter'] && Array.isArray(modifyStoppLevel['start-parameter'])) {
        for (const params of modifyStoppLevel['start-parameter']) {
            dataChangedCnt += await insertTradeData(params, row, dateString, 'start-parameter', existingDetailOrders);
        }
    }

    // Neue Verarbeitung für 'log-mode'
    if (modifyStoppLevel['log-mode'] && Array.isArray(modifyStoppLevel['log-mode'])) {
        for (const logEntry of modifyStoppLevel['log-mode']) {
            dataChangedCnt += await insertTradeData({ comment: logEntry }, row, dateString, 'log-mode', existingDetailOrders);
        }
    }

    // Neue Verarbeitung für 'adjust-missing-stoppoffset'
    if (modifyStoppLevel['adjust-missing-stoppoffset'] && Array.isArray(modifyStoppLevel['adjust-missing-stoppoffset'])) {
        for (const adjustInfo of modifyStoppLevel['adjust-missing-stoppoffset']) {
            dataChangedCnt += await insertTradeData(adjustInfo, row, dateString, 'adjust-missing-stoppoffset', existingDetailOrders);
        }
    }

    return dataChangedCnt;
}

async function processTradeDetails(details, row, dateString, existingDetailOrders) {
    let dataChangedCnt = 0;

    for (const detail of Object.values(details)) {
        if (typeof detail !== 'object' || detail === null) continue;

        const [tradeInfo, status] = Array.isArray(detail) ? 
            [detail[0], 'open'] : 
            [detail[Object.keys(detail)[0]][0], getStatus(Object.keys(detail)[0])];

        dataChangedCnt += await insertTradeData(tradeInfo, row, dateString, status, existingDetailOrders);
    }

    return dataChangedCnt;
}

function getStatus(key) {
    const statusMap = {
        'trade-already-exist': 'exist',
        'position-was-already-opened-before-and-closed-manually': 'manual-close',
        'params_defaultMinProfit lower than trade-position': 'low-profit'
    };
    return statusMap[key] || 'unknown';
}

/**
 * Fügt Trade-Daten in die Mirror-Trading-Logs Tabellen ein
 * 
 * Diese Funktion speichert Trade-Informationen in zwei Tabellen:
 * - mirror_trading_logs_profit: Enthält Profit/Loss Informationen
 * - mirror_trading_logs_details: Enthält detaillierte Trade-Daten
 * 
 * Die Funktion verwendet IGNORE bei INSERT um Duplikate zu vermeiden.
 * 
 * @param {Object} tradeInfo - Trade-Details wie Order, Profit, Stop-Loss etc.
 * @param {Object} row - Referenz-Informationen aus der Datenbank
 * @param {string} dateString - Zeitstempel im Format 'YYYY-MM-DD HH:mm:ss'
 * @param {string} status - Status des Trades (z.B. 'open', 'exist', 'manual-close')
 * @param {Set<number>} existingDetailOrders - Set von bereits verarbeiteten Order-IDs
 * @returns {Promise<number>} 1 wenn erfolgreich, 0 bei Duplikat
 * @throws {Error} Bei SQL-Fehlern außer Duplikat-Einträgen
 * 
 * @example
 * const tradeInfo = {
 *   order2: 123,
 *   order: 456,
 *   profit: 10.5,
 *   sl: 1.1234,
 *   symbol: 'EURUSD'
 * };
 * const inserted = await insertTradeData(
 *   tradeInfo,
 *   rowData,
 *   '2024-11-23 10:00:00',
 *   'open',
 *   new Set()
 * );
 */
async function insertTradeData(tradeInfo, row, dateString, status, existingDetailOrders) {
    const order2 = tradeInfo.order2 || tradeInfo['compare-orderID'];
    const order = parseInt(tradeInfo.order) || 0;
    const profit = parseFloat(tradeInfo.profit) || 0;

    const insertQueryProfitData = `
        INSERT IGNORE INTO mirror_trading_logs_profit 
        (targetRefID, log_timestamp, log_order2, log_order, profit, status, sl, tp, \`offset\`, additional_info)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const insertQueryOrderData = `
        INSERT IGNORE INTO mirror_trading_logs_details 
        (order2, symbol, \`comment\`, open_price, volume, open_time, cmd, additional_info)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    let additionalInfo = JSON.stringify(tradeInfo);

    if (order2>0)
        await db.pool.query(insertQueryProfitData, [
            row.targetRefID,
            dateString,
            order2,
            order,
            profit,
            status,
            tradeInfo.sl || tradeInfo['new-stopp-level'] || 0,
            tradeInfo.tp || 0,
            tradeInfo.offset || tradeInfo['trailStoppOffset'] || 0,
            additionalInfo
        ]).catch(err => {
            if (err.code !== "ER_DUP_ENTRY") console.log(err);
        });

    if (!existingDetailOrders.has(order2) && status !== 'start-parameter' && status !== 'log-mode') {
        await db.pool.query(insertQueryOrderData, [
            order2,
            tradeInfo.symbol || extractSymbol(tradeInfo.comment) || '',
            tradeInfo.comment || status,
            parseFloat(tradeInfo.open_price) || 0,
            parseFloat(tradeInfo.volume) || 0,
            new Date(tradeInfo.open_timeString || dateString),
            parseInt(tradeInfo.cmd) || 0,
            additionalInfo
        ]).catch(err => {
            if (err.code !== "ER_DUP_ENTRY") console.log(err);
        });
        existingDetailOrders.add(order2);
    }

    return 1;
}


/**
 * @swagger
 * /api/v1/mirrorTradingLogs/losts:
 *   get:
 *     summary: Get lost mirror trades
 *     description: |
 *       Retrieves detailed information about losing trades from the mirror trading system.
 *       Includes trade entry/exit points, profit/loss metrics, and trade parameters.
 *       Data can be optionally refreshed from the source trading system.
 *       
 *       Use this endpoint to:
 *       - Analyze trading performance
 *       - Identify problematic trade patterns
 *       - Monitor risk management effectiveness
 *     tags: [Mirror Trading]
 *     parameters:
 *       - in: query
 *         name: refreshData
 *         schema:
 *           type: boolean
 *           default: false
 *         description: |
 *           When true, synchronizes data with the trading system before returning results.
 *           This may add a few seconds to the response time but ensures latest data.
 *       - in: query
 *         name: targetRefID
 *         schema:
 *           type: string
 *           example: "D2"
 *           pattern: "^[A-Z][0-9]$"
 *         description: |
 *           Trading account identifier.
 *           Format: Letter followed by number (e.g. D1, P2)
 *           - D* = Demo account
 *           - P* = Production account
 *     responses:
 *       200:
 *         description: Successfully retrieved lost trades data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               required: [results]
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     required: [log_row_number, targetRefID, log_order, log_order2]
 *                     properties:
 *                       log_row_number:
 *                         type: integer
 *                         description: Sequential number of the log entry
 *                         example: 1
 *                       total_rows:
 *                         type: integer
 *                         description: Total number of trades in the dataset
 *                         example: 100
 *                       targetRefID:
 *                         type: string
 *                         description: Account identifier
 *                         example: "D2"
 *                       log_order:
 *                         type: integer
 *                         description: Internal order reference
 *                         example: 12345
 *                       log_order2:
 *                         type: integer
 *                         description: External broker order ID
 *                         example: 67890
 *                       min_time:
 *                         type: string
 *                         format: date-time
 *                         description: Trade entry time
 *                         example: "2025-01-12T10:30:00Z"
 *                       max_time:
 *                         type: string
 *                         format: date-time
 *                         description: Trade exit time
 *                         example: "2025-01-12T14:45:00Z"
 *                       min_profit:
 *                         type: number
 *                         format: double
 *                         description: Minimum profit reached during trade
 *                         example: -150.50
 *                       max_profit:
 *                         type: number
 *                         format: double
 *                         description: Maximum profit reached during trade
 *                         example: -50.25
 *                       avg_profit:
 *                         type: number
 *                         format: double
 *                         description: Average profit during trade duration
 *                         example: -85.75
 *                       cnt:
 *                         type: integer
 *                         description: Number of price updates for this trade
 *                         example: 24
 *                 refreshMirrorTradingLogs2LogsProfit:
 *                   type: string
 *                   description: Status of data refresh operation
 *                   example: "Data processed successfully"
 *                 refreshEntryExitPoint2LogsProfit:
 *                   type: string
 *                   description: Status of entry/exit points refresh
 *                   example: "Data processed: 15 rows changed"
 *       400:
 *         description: Invalid parameters provided
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationError'
 *       500:
 *         description: |
 *           Server error occurred:
 *           - Database connection failed
 *           - Query execution error
 *           - Data synchronization failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DatabaseError'
 */
exports.getMirrorTradingLogLosts = async (req, res) => {
    await getMirrorTradingLogData(req, res, 'detailsSet.closed_profit<0', 20);
}

/**
 * @swagger
 * /api/v1/mirrorTradingLogs/profits:
 *   get:
 *     summary: Get profitable mirror trades
 *     description: Retrieves mirror trading logs for trades that resulted in a profit
 *     tags: [Mirror Trading]
 *     parameters:
 *       - in: query
 *         name: refreshData
 *         schema:
 *           type: boolean
 *         description: Whether to refresh the data before returning
 *       - in: query
 *         name: targetRefID
 *         schema:
 *           type: string
 *         description: Target account reference ID
 *     responses:
 *       200:
 *         description: Profitable trades data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       log_row_number:
 *                         type: integer
 *                       total_rows:
 *                         type: integer
 *                       targetRefID:
 *                         type: string
 *                       log_order:
 *                         type: integer
 *                       log_order2:
 *                         type: integer
 *                       min_time:
 *                         type: string
 *                         format: date-time
 *                       max_time:
 *                         type: string
 *                         format: date-time
 *                       min_profit:
 *                         type: number
 *                       max_profit:
 *                         type: number
 *                       avg_profit:
 *                         type: number
 *                       cnt:
 *                         type: integer
 *       500:
 *         description: Database error
 */
exports.getMirrorTradingLogProfits = async (req, res) => {
    await getMirrorTradingLogData(req, res, 'SubSet.max_profit-15>0', -15);
}

/**
 * @swagger
 * /api/v1/mirrorTradingLogs/losts/stats:
 *   get:
 *     summary: Get lost trades statistics
 *     description: Retrieves statistical analysis of losing mirror trades
 *     tags: [Mirror Trading]
 *     responses:
 *       200:
 *         description: Lost trades statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       symbol:
 *                         type: string
 *                       total_losses:
 *                         type: integer
 *                       avg_loss:
 *                         type: number
 *                       max_loss:
 *                         type: number
 *                       total_loss_amount:
 *                         type: number
 *       500:
 *         description: Database error
 */
exports.getMirrorTradingLogLostsStats = async (req, res) => {
    let l_return = { "results": []  };
    try {
        const p_query = {
            sql: `SELECT * FROM mirror_trading_logs_lost_stats`,
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        l_return.results = await db.pool.query(p_query);
    } catch (err) {
        console.error("error in getMirrorTradingLogLostsStats", err); // eslint-disable-line no-
        l_return.error = err;
    }
    res.send(l_return);
}

/**
 * @swagger
 * /api/v1/mirrorTradingLogs/bes/stats:
 *   get:
 *     summary: Get break-even stop statistics
 *     description: Retrieves statistics about break-even stop usage in mirror trading
 *     tags: [Mirror Trading]
 *     parameters:
 *       - in: query
 *         name: refID
 *         required: true
 *         schema:
 *           type: string
 *         description: Account reference ID
 *     responses:
 *       200:
 *         description: Break-even stop statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       refID:
 *                         type: string
 *                       symbol:
 *                         type: string
 *                       total_bes_trades:
 *                         type: integer
 *                       successful_bes:
 *                         type: integer
 *                       total_sum:
 *                         type: number
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Database error
 */
exports.getMirrorTradingLogBESStats = async (req, res) => {
    let p_refID = req.query.refID;
    let l_return = { "results": []  };
    try {
        const p_query = {
            sql: `SELECT * FROM mirror_trading_logs_bes_stats where refID=? and total_sum<>0`,
            bigIntAsNumber: true,
            values: [p_refID],
            timezone: 'de_de'
        }
        l_return.results = await db.pool.query(p_query);
    } catch (err) {
        console.error("error in getMirrorTradingLogBESStats", err); // eslint-disable-line no-
        l_return.error = err;
    }
    res.send(l_return);
}

/**
 * @swagger
 * /api/v1/mirrorTradingLogs/optimizations:
 *   get:
 *     summary: Get optimization data
 *     description: Retrieves optimization settings and results for mirror trading
 *     tags: [Mirror Trading]
 *     parameters:
 *       - in: query
 *         name: targetRefID
 *         schema:
 *           type: string
 *           default: D1
 *         description: Target account reference ID
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 3
 *           minimum: 1
 *           maximum: 365
 *         description: Number of days to analyze
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: integer
 *           default: 1
 *           minimum: 1
 *           maximum: 1440
 *         description: Timeframe in minutes
 *     responses:
 *       200:
 *         description: Optimization data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       targetRefID:
 *                         type: string
 *                       date:
 *                         type: string
 *                         format: date
 *                       optimization_type:
 *                         type: string
 *                       parameters:
 *                         type: object
 *                       result:
 *                         type: object
 *       500:
 *         description: Database error
 */
exports.getMirrorTradingOptimizations = async (req, res) => {
    let targetRefID = req.query.targetRefID || 'IG-D1';
    let days = parseInt(req.query.days) || 3;
    let timeframe = parseInt(req.query.timeframe) || 1;

    let l_return = { "results": [] };
    try {
        const p_query = {
            sql: `SELECT *
                  FROM mirror_trading_optimizations p
                  WHERE p.targetRefID = ?
                    AND p.date between DATE_SUB(CURDATE(), INTERVAL ? DAY) AND DATE_SUB(CURDATE(), INTERVAL ? DAY)
                  ORDER BY p.date`,
            values: [targetRefID, days, days-timeframe, days, days-timeframe],
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        l_return.results = await db.pool.query(p_query);
        if (process.env.NODE_ENV !== 'production')
            console.log(`[db_mirrorTradingLogs][getMirrorTradingOptimizaions] ${targetRefID}:`, l_return.results.length, "rows returned");

    } catch (err) {
        console.error("Error in getMirrorTradingLogProfitCurves", err);
        l_return.error = err;
    }
    res.send(l_return);
}

/**
 * @swagger
 * /api/v1/mirrorTradingLogs/profitcurves:
 *   get:
 *     summary: Get profit curves
 *     description: Retrieves profit curve data for mirror trading analysis
 *     tags: [Mirror Trading]
 *     parameters:
 *       - in: query
 *         name: targetRefID
 *         schema:
 *           type: string
 *           default: D1
 *         description: Target account reference ID
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 3
 *           minimum: 1
 *           maximum: 365
 *         description: Number of days to analyze
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: integer
 *           default: 1
 *           minimum: 1
 *           maximum: 1440
 *         description: Timeframe in minutes
 *     responses:
 *       200:
 *         description: Profit curve data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       targetRefID:
 *                         type: string
 *                       log_timestamp:
 *                         type: string
 *                         format: date-time
 *                       log_order2:
 *                         type: integer
 *                       log_order:
 *                         type: integer
 *                       profit:
 *                         type: number
 *                       status:
 *                         type: string
 *                       sl:
 *                         type: number
 *                       tp:
 *                         type: number
 *       500:
 *         description: Database error
 */
exports.getMirrorTradingLogProfitCurves = async (req, res) => {
    let targetRefID = req.query.targetRefID || 'IG-D1';
    let days = parseInt(req.query.days);

    let timeframe = parseInt(req.query.timeframe);

    let l_return = { "results": [] };
    try {
        const p_query = {
            sql: `SELECT p.targetRefID,
                         DATE_ADD(p.log_timestamp, INTERVAL 0 HOUR) AS log_timestamp,
                         p.log_order2,
                         p.log_order,
                         h.profit as closed_profit,
                         p.profit,
                         p.\`status\`,
                         p.sl,
                         p.tp,
                         h.symbol,
                         h.cmd,
                         h.comment,
                         h.entry_time,
                         h.exit_time,
                         h.open_price,
                         h.close_price,
                         h.strategy,
                         h.manual_stop,
                         p.source
                  FROM mirror_trading_logs_profit p,
                       trades_history h
                  WHERE p.targetRefID = ?
                    and p.log_order=h.source_xtb_orderID 
                    AND p.log_timestamp >= DATE_SUB(CURDATE(), INTERVAL ? DAY) AND DATE_ADD(p.log_timestamp, INTERVAL ? DAY)
                  ORDER BY p.log_order2, p.log_timestamp desc`,
            values: [targetRefID, days, days-timeframe, days, days-timeframe],
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        l_return.results = await db.pool.query(p_query);
        if (process.env.NODE_ENV !== 'production') {
            console.log(`[db_mirrorTradingLogs][getMirrorTradingLogProfitCurves] ${targetRefID}:`, l_return.results.length, "rows returned", "sql: ", p_query.sql);
            console.log(`[db_mirrorTradingLogs][getMirrorTradingLogProfitCurves] sql-params: `, targetRefID, days, timeframe);   
        }    

    } catch (err) {
        console.error("Error in getMirrorTradingLogProfitCurves", err);
        l_return.error = err;
    }
    res.send(l_return);
}


/**
 * Lädt und analysiert Mirror-Trading-Log-Daten
 * 
 * Diese Funktion führt mehrere Operationen aus:
 * 1. Optional: Aktualisiert die Log-Daten aus mirror_trading_logs
 * 2. Optional: Überträgt Entry/Exit-Punkte
 * 3. Lädt gefilterte Log-Daten mit Profit/Loss Informationen
 * 
 * Die Daten werden nach relativem Profit sortiert und mit
 * zusätzlichen Statistiken angereichert.
 * 
 * @param {Object} req - Express Request-Objekt
 * @param {Object} res - Express Response-Objekt
 * @param {string} p_subSetMaxProfitCondition - SQL-Bedingung für Profit-Filter
 * @param {number} p_maxProfit_Correction - Korrekturwert für Profit-Berechnung
 * @returns {Promise<void>} Sendet JSON-Response mit Log-Daten
 * @throws {Error} Bei Datenbankfehlern
 * 
 * @example
 * // API-Aufruf:
 * GET /api/v1/mirrorTradingLogs/profits?targetRefID=ACC123&refreshData=true
 * 
 * // Funktionsaufruf:
 * await getMirrorTradingLogData(
 *   req,
 *   res,
 *   'SubSet.max_profit-15>0',
 *   -15
 * );
 */
async function getMirrorTradingLogData(req, res, p_subSetMaxProfitCondition, p_maxProfit_Correction) {

    let p_refreshData = req.query.refreshData?req.query.refreshData=='true':false;

    // transfer data from mirror_trading_logs to mirror_trading_logs_profit
    let l_refreshResult = p_refreshData ? await transferMirrorTradingLogs2LogsProfit() : "refreshMirrorTradingLogs2LogsProfit skipped";
    let l_return = { "results": [], "refreshMirrorTradingLogs2LogsProfit": l_refreshResult };

    // transfer entry-exit-points
    l_return.refreshEntryExitPoint2LogsProfit = p_refreshData?await transferXTBTrades2LogsProfitAsEntryExitPoints():'refreshEntryExitPoint2LogsProfit skipped';

    // get data from mirror_trading_logs_profit
    let p_refID = req.query.targetRefID;
    if (p_refID == undefined) p_refID = 'IG-D1';

    try {
        const p_query = {
            sql: `SELECT SubSet.*,
                         ROUND(SubSet.log_row_number * 100 / SubSet.total_rows,1) AS relative_row_number,
                         ROUND((SubSet.max_profit+${p_maxProfit_Correction}) * SubSet.log_row_number) AS relative_profit_forecast,
                         detailsSet.comment,
                         detailsSet.open_price,
                         detailsSet.volume,
                         detailsSet.cmd,
                         detailsSet.symbol,
                         detailsSet.closed_profit
                  FROM (SELECT ROW_NUMBER() over ( ORDER BY MAX(mtl.profit) DESC) AS log_row_number,
                               subquery.total_rows                                AS total_rows,
                               mtl.targetRefID                                    AS targetRefID,
                               mtl.log_order                                      AS log_order,
                               mtl.log_order2                                     AS log_order2,
                               MIN(mtl.log_timestamp)                             AS min_time,
                               MAX(mtl.log_timestamp)                             AS max_time,
                               round(MIN(mtl.profit))                             AS min_profit,
                               round(MAX(mtl.profit))                             AS max_profit,
                               round(AVG(mtl.profit))                             AS avg_profit,
                               COUNT(0)                                           AS cnt
                        FROM (mirror_trading_logs_profit mtl
                            JOIN (SELECT COUNT(DISTINCT m.log_order) AS total_rows
                                  FROM mirror_trading_logs_profit m
                                  WHERE m.targetRefID = ?) subquery)
                        WHERE mtl.targetRefID = ?
                          AND mtl.log_timestamp>=date_sub(current_date(), interval 120 day)
                        GROUP BY mtl.targetRefID, mtl.log_order, mtl.log_order2
                        ORDER BY max_profit DESC) as SubSet,
                       mirror_trading_logs_details as detailsSet
                  where ${p_subSetMaxProfitCondition}
                    and SubSet.log_order2=detailsSet.order2
                   order by relative_profit_forecast desc`,
            values: [p_refID,p_refID],
            types: ['string', 'string'],
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        if (process.env.NODE_ENV !== 'production')
          console.log("[db_mirrorTradingLogs][getMirrorTradingLogData] - sql: ",p_query.sql);
        l_return.results = await db.pool.query(p_query);
        if (process.env.NODE_ENV !== 'production')
          console.log(`[db_mirrorTradingLogs][getMirrorTradingLogData] ${p_refID}:`, l_return.results.length, "rows returned")
    } catch (err) {
        console.error("error in get_mirrorTradingLogs", err); // eslint-disable-line no-
        l_return.error = err;
    }
    res.send(l_return);
}
