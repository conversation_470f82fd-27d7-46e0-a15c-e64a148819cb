{"components": {"schemas": {"NewsItem": {"type": "object", "properties": {"news_id": {"type": "integer", "description": "Eindeutige ID des Nachrichteneintrags", "example": 12345}, "title": {"type": "string", "description": "Titel der Nachricht", "example": "US Federal Reserve Raises Interest Rates by 25 Basis Points"}, "content": {"type": "string", "description": "Inhalt der Nachricht", "example": "The Federal Reserve raised its benchmark interest rate by 25 basis points on Wednesday, bringing it to a range of 5.00% to 5.25%, the highest level since 2007."}, "publish_date": {"type": "string", "format": "date-time", "description": "Veröffentlichungsdatum der Nachricht", "example": "2023-04-15T14:30:00Z"}, "source": {"type": "string", "description": "Quelle der Nachricht", "example": "Bloomberg"}, "url": {"type": "string", "format": "uri", "description": "URL zur Originalnachricht", "example": "https://www.bloomberg.com/news/articles/2023-04-15/fed-raises-rates"}, "category": {"type": "string", "description": "Kategorie der Nachricht", "example": "Economy"}, "sentiment": {"type": "number", "format": "float", "description": "Sentimentwert der Nachricht (-1 bis 1, wobei -1 negativ und 1 positiv ist)", "minimum": -1, "maximum": 1, "example": -0.25}, "relevance": {"type": "number", "format": "float", "description": "Relevanzwert der Nachricht (0 bis 1)", "minimum": 0, "maximum": 1, "example": 0.85}, "symbols": {"type": "string", "description": "Kommagetrennte Liste von Symbolen, die von der Nachricht betroffen sind", "example": "EURUSD,GBPUSD,USDJPY"}, "is_read": {"type": "boolean", "description": "<PERSON><PERSON>t an, ob die Nachricht bereits gelesen wurde", "example": false}, "is_bookmarked": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> an, ob die Nachricht als Lesezeichen markiert wurde", "example": false}}}, "FactorMap": {"type": "object", "properties": {"factor_map_id": {"type": "integer", "description": "Eindeutige ID der Faktorkarte", "example": 789}, "timestamp": {"type": "string", "format": "date-time", "description": "Zeitpunkt der Erstellung der Faktorkarte", "example": "2023-04-15T14:30:00Z"}, "factors": {"type": "object", "description": "Faktoren und ihre Werte", "example": {"interest_rates": 0.85, "inflation": 0.72, "gdp_growth": -0.25, "unemployment": 0.15, "consumer_confidence": -0.35}}, "market_sentiment": {"type": "string", "description": "Allgemeines Marktsentiment", "enum": ["Bullish", "Bearish", "Neutral"], "example": "Bearish"}, "risk_level": {"type": "string", "description": "Aktuelles Risikoniveau", "enum": ["Low", "Medium", "High"], "example": "High"}, "summary": {"type": "string", "description": "Zusammenfassung der aktuellen Marktlage", "example": "Markets are cautious due to rising interest rates and inflation concerns, with a bearish outlook for the near term."}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Invalid limit parameter"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getNewsFromDB"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Invalid limit parameter", "function": "getNewsFromDB"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch news", "function": "getNewsFromDB"}}}}}}}