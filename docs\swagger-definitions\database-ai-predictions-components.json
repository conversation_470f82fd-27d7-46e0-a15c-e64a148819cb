{"components": {"schemas": {"AIPrediction": {"type": "object", "properties": {"symbol": {"type": "string", "description": "Trading-Symbol, für das die Vorhersage gilt", "example": "EURUSD"}, "prediction_date": {"type": "string", "format": "date-time", "description": "Datum und Uhrzeit der Vorhersage", "example": "2023-04-15T10:30:00Z"}, "prediction_value": {"type": "number", "format": "float", "description": "Vorhergesagter Wert (z.B<PERSON>, Wahrscheinlichkeit)", "example": 1.0865}, "prediction_direction": {"type": "string", "description": "Vorhergesag<PERSON> Rich<PERSON>g (Long, Short, Neutral)", "enum": ["<PERSON>", "Short", "Neutral"], "example": "<PERSON>"}, "confidence": {"type": "number", "format": "float", "description": "Konfidenzwert der Vorhersage (0-1)", "minimum": 0, "maximum": 1, "example": 0.85}, "model_name": {"type": "string", "description": "Name des verwendeten AI-Modells", "example": "LSTM-v2.3"}, "timeframe": {"type": "string", "description": "Zeitrahmen der Vorhersage", "example": "1D"}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Invalid symbol parameter"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getSymbolAIPredictions"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Invalid symbol parameter", "function": "getSymbolAIPredictions"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch AI predictions", "function": "getSymbolAIPredictions"}}}}}}}