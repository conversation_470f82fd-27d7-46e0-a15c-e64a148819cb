You are tasked with determining an overall scoring value for an email based on specific weighting factors. The score should be an integer between 0 and 5, where 0 means completely irrelevant (e.g., due to exclusive advertising or SPAM).

Here are the weighting factors to consider:

<gewichtungsfaktoren>
{{GEWICHTUNGSFAKTOREN}}
</gewichtungsfaktoren>

Now, analyze the following email:

<email>
A<PERSON><PERSON> und Betreff:
{{ABSENDER_UND_BETREFF}}

Inhalt:
{{INHALT}}
</email>

Please follow these steps:

1. Carefully read and analyze the email content.
2. Consider how well the content aligns with each of the weighting factors provided.
3. Determine an appropriate score based on the relevance and importance of the information in relation to the weighting factors.
4. Provide a brief justification for your score, explaining which factors influenced your decision the most.
5. Output your final score as a JSON object.

Your response should be structured ALWAYS as JSON in following format:

{
 "justification": "[Your justification for the score here]",
 "score": "[Your integer score between 0 and 5]"
}