# Parallel GraphQL und REST API Operation - Technische Analyse

## Zusammenfassung

Diese Analyse untersucht die technische Machbarkeit und den Overhead eines parallelen Betriebs von GraphQL und REST API für die Algotrader API, inklusive Architektur-Patterns, Implementierungsstrategien und langfristige Wartungsaspekte.

## 1. Architektur-Überblick für Dual-API Setup

### 1.1 **Shared Business Logic Pattern**

```
┌─────────────────────────────────────────────────────────────┐
│                    Express.js Server                        │
├─────────────────────────────────────────────────────────────┤
│  REST Routes          │           GraphQL Endpoint          │
│  /api/v1/db/*        │           /graphql                   │
│  /api/v1/ig/*        │           /subscriptions             │
│  /api/v1/ai/*        │                                      │
│  /api/v1/fmp/*       │                                      │
├─────────────────────────────────────────────────────────────┤
│                 Shared Middleware Layer                     │
│  • Authentication    • Rate Limiting    • Logging          │
│  • CORS              • Validation       • Error Handling   │
├─────────────────────────────────────────────────────────────┤
│                Shared Business Logic Layer                  │
│  • Controllers/Services  • Database Access  • Caching      │
│  • AI Integrations      • Trading Logic    • Validation    │
├─────────────────────────────────────────────────────────────┤
│                    Data Access Layer                        │
│  • MariaDB Pool      • Cache Service     • External APIs   │
│  • Query Builders    • Connection Mgmt   • Rate Limiting   │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 **Server Setup mit Dual-Endpoints**

```javascript
// server.js - Erweiterte Konfiguration
const express = require('express');
const { ApolloServer } = require('apollo-server-express');
const { createServer } = require('http');
const { SubscriptionServer } = require('subscriptions-transport-ws');
const { execute, subscribe } = require('graphql');

const app = express();
const httpServer = createServer(app);

// Bestehende REST API Middleware
app.use('/api/v1/db', databaseRoutes);
app.use('/api/v1/ig', igRoutes);
app.use('/api/v1/ai', aiRoutes);
app.use('/api/v1/fmp', fmpRoutes);

// GraphQL Server Setup
const apolloServer = new ApolloServer({
  typeDefs,
  resolvers,
  context: ({ req, connection }) => {
    // Shared context für REST und GraphQL
    if (connection) {
      // WebSocket context für Subscriptions
      return {
        ...connection.context,
        dataSources: getDataSources(),
      };
    }
    // HTTP context
    return {
      user: req.user,
      apiKey: req.headers['x-api-key'],
      requestId: req.id,
      dataSources: getDataSources(),
    };
  },
  plugins: [
    // Shared monitoring und logging
    require('./plugins/apollo-logging-plugin'),
    require('./plugins/apollo-caching-plugin'),
  ],
});

await apolloServer.start();
apolloServer.applyMiddleware({ 
  app, 
  path: '/graphql',
  cors: corsOptions // Gleiche CORS Settings wie REST
});

// WebSocket Subscriptions
SubscriptionServer.create({
  schema: apolloServer.schema,
  execute,
  subscribe,
  onConnect: async (connectionParams, webSocket, context) => {
    // Gleiche Auth-Logic wie REST
    const apiKey = connectionParams['x-api-key'];
    if (!await validateApiKey(apiKey)) {
      throw new Error('Unauthorized');
    }
    return { apiKey, authenticated: true };
  },
}, {
  server: httpServer,
  path: '/subscriptions',
});
```

## 2. Code-Sharing Strategien

### 2.1 **Service Layer Abstraktion**

```javascript
// services/trading/tradeHistoryService.js
class TradeHistoryService {
  constructor(dbPool, cacheService, logger) {
    this.db = dbPool;
    this.cache = cacheService;
    this.logger = logger;
  }

  async getTradeHistory(params) {
    const { symbol, days, limit, refID } = params;
    
    // Shared validation logic
    const validatedParams = this.validateParams(params);
    
    // Shared caching logic
    const cacheKey = this.generateCacheKey('trade_history', validatedParams);
    const cached = await this.cache.get(cacheKey);
    if (cached) return cached;

    // Shared business logic
    const result = await this.fetchTradeHistoryFromDB(validatedParams);
    
    // Shared caching
    await this.cache.set(cacheKey, result, 300); // 5 min TTL
    
    return result;
  }

  // Validation logic shared between REST and GraphQL
  validateParams(params) {
    // ... shared validation
  }

  async fetchTradeHistoryFromDB(params) {
    // ... shared database logic
  }
}

module.exports = TradeHistoryService;
```

### 2.2 **REST Controller Adaption**

```javascript
// controllers/database/trade_history_controller.js
const TradeHistoryService = require('../../services/trading/tradeHistoryService');

class TradeHistoryController {
  constructor() {
    this.service = new TradeHistoryService(dbPool, cacheService, logger);
  }

  // REST Endpoint Handler
  async getTradeHistory(req, res) {
    try {
      const params = {
        symbol: req.query.symbol,
        days: parseInt(req.query.days) || 7,
        limit: parseInt(req.query.limit) || 50,
        refID: req.query.refID
      };

      const result = await this.service.getTradeHistory(params);
      
      res.json({
        status: 'success',
        results: result,
        count: result.length
      });
    } catch (error) {
      const errorResponse = errorHandler(error, 'getTradeHistory');
      res.status(errorResponse.status).json(errorResponse);
    }
  }
}
```

### 2.3 **GraphQL Resolver Integration**

```javascript
// resolvers/tradeHistoryResolvers.js
const TradeHistoryService = require('../services/trading/tradeHistoryService');

const tradeHistoryResolvers = {
  Query: {
    tradeHistory: async (parent, args, context) => {
      const service = new TradeHistoryService(
        context.dataSources.db,
        context.dataSources.cache,
        context.logger
      );

      return await service.getTradeHistory({
        symbol: args.symbol,
        days: args.days || 7,
        limit: args.limit || 50,
        refID: args.refID
      });
    },
  },

  Symbol: {
    // Field resolver für nested queries
    tradeHistory: async (parent, args, context) => {
      const service = new TradeHistoryService(
        context.dataSources.db,
        context.dataSources.cache,
        context.logger
      );

      return await service.getTradeHistory({
        symbol: parent.name,
        days: args.days || 7,
        limit: args.limit || 50
      });
    },
  },
};

module.exports = tradeHistoryResolvers;
```

## 3. Middleware und Security Integration

### 3.1 **Shared Authentication Middleware**

```javascript
// middleware/sharedAuth.js
class SharedAuthService {
  static async validateApiKey(apiKey, req = null) {
    // Shared validation logic für REST und GraphQL
    if (!apiKey) {
      throw new Error('API Key required');
    }

    // Validation logic...
    const isValid = await this.checkApiKeyInDatabase(apiKey);
    
    if (!isValid) {
      // Shared logging
      logger.warn('Invalid API key attempt', {
        apiKey: apiKey.substring(0, 8) + '...',
        ip: req?.ip || 'GraphQL Subscription',
        userAgent: req?.headers['user-agent'] || 'WebSocket'
      });
      throw new Error('Invalid API Key');
    }

    return true;
  }

  static async checkApiKeyInDatabase(apiKey) {
    // Shared database query
    const result = await dbPool.query(
      'SELECT id FROM api_keys WHERE key_hash = ? AND is_active = 1',
      [crypto.createHash('sha256').update(apiKey).digest('hex')]
    );
    return result.length > 0;
  }
}

// REST Middleware
const restAuthMiddleware = async (req, res, next) => {
  try {
    const apiKey = req.headers['x-api-key'];
    await SharedAuthService.validateApiKey(apiKey, req);
    next();
  } catch (error) {
    res.status(401).json({ error: error.message });
  }
};

// GraphQL Context Auth
const graphqlAuthContext = async (req) => {
  const apiKey = req.headers['x-api-key'];
  await SharedAuthService.validateApiKey(apiKey, req);
  return { authenticated: true, apiKey };
};
```

### 3.2 **Unified Rate Limiting**

```javascript
// middleware/sharedRateLimit.js
const rateLimit = require('express-rate-limit');
const { shield, rule, or } = require('graphql-shield');

class UnifiedRateLimiter {
  constructor() {
    this.store = new Map(); // Oder Redis für Production
  }

  // REST Rate Limiting
  getRestLimiter(endpoint) {
    return rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: this.getLimitForEndpoint(endpoint),
      message: 'Too many requests',
      standardHeaders: true,
      legacyHeaders: false,
      keyGenerator: (req) => {
        return req.headers['x-api-key'] || req.ip;
      }
    });
  }

  // GraphQL Rate Limiting
  getGraphQLShield() {
    const rateLimitRule = rule({ cache: 'contextual' })(
      async (parent, args, context, info) => {
        const key = context.apiKey || context.ip;
        const complexity = this.calculateQueryComplexity(info);
        
        return this.checkRateLimit(key, complexity);
      }
    );

    return shield({
      Query: {
        tradeHistory: rateLimitRule,
        statistics: rateLimitRule,
        // ... weitere protected queries
      },
      Mutation: {
        '*': rateLimitRule // Alle Mutations rate-limited
      }
    });
  }

  getLimitForEndpoint(endpoint) {
    const limits = {
      '/api/v1/db/trade_history': 100,
      '/api/v1/db/statistics': 50,
      'default': 200
    };
    return limits[endpoint] || limits['default'];
  }
}
```

## 4. Caching Strategy für Dual-API

### 4.1 **Unified Cache Service**

```javascript
// services/cache/unifiedCacheService.js
class UnifiedCacheService {
  constructor(nodeCache, redisClient = null) {
    this.localCache = nodeCache;
    this.distributedCache = redisClient;
  }

  // Cache Key Generation für beide APIs
  generateKey(type, operation, params) {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {});
    
    return `${type}:${operation}:${JSON.stringify(sortedParams)}`;
  }

  async get(key) {
    // Local Cache first
    let value = this.localCache.get(key);
    if (value) {
      return value;
    }

    // Distributed Cache fallback
    if (this.distributedCache) {
      const distributed = await this.distributedCache.get(key);
      if (distributed) {
        value = JSON.parse(distributed);
        // Populate local cache
        this.localCache.set(key, value, 300);
        return value;
      }
    }

    return null;
  }

  async set(key, value, ttl = 300) {
    // Set in both caches
    this.localCache.set(key, value, ttl);
    
    if (this.distributedCache) {
      await this.distributedCache.setex(key, ttl, JSON.stringify(value));
    }
  }

  async invalidate(pattern) {
    // Local cache invalidation
    const keys = this.localCache.keys();
    keys.forEach(key => {
      if (key.includes(pattern)) {
        this.localCache.del(key);
      }
    });

    // Distributed cache invalidation
    if (this.distributedCache) {
      const distributedKeys = await this.distributedCache.keys(`*${pattern}*`);
      if (distributedKeys.length > 0) {
        await this.distributedCache.del(distributedKeys);
      }
    }
  }
}

// Usage in both REST and GraphQL
const cacheService = new UnifiedCacheService(nodeCache, redisClient);

// REST Usage
app.get('/api/v1/db/trade_history', async (req, res) => {
  const cacheKey = cacheService.generateKey('TRADE_HISTORY', 'get', req.query);
  let result = await cacheService.get(cacheKey);
  
  if (!result) {
    result = await getTradeHistoryFromDB(req.query);
    await cacheService.set(cacheKey, result);
  }
  
  res.json(result);
});

// GraphQL Usage
const resolvers = {
  Query: {
    tradeHistory: async (parent, args, context) => {
      const cacheKey = context.cacheService.generateKey('TRADE_HISTORY', 'get', args);
      let result = await context.cacheService.get(cacheKey);
      
      if (!result) {
        result = await getTradeHistoryFromDB(args);
        await context.cacheService.set(cacheKey, result);
      }
      
      return result;
    }
  }
};
```

### 4.2 **Cache Invalidation Strategy**

```javascript
// services/cache/invalidationService.js
class CacheInvalidationService {
  constructor(cacheService, eventEmitter) {
    this.cache = cacheService;
    this.events = eventEmitter;
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    // Trade execution events
    this.events.on('trade.executed', (data) => {
      this.invalidateTradeRelatedCache(data.symbol, data.refID);
    });

    // Symbol setup changes
    this.events.on('symbol.setup.updated', (data) => {
      this.invalidateSymbolCache(data.symbol);
    });

    // Statistics updates
    this.events.on('statistics.updated', (data) => {
      this.invalidateStatisticsCache(data.refID, data.period);
    });
  }

  async invalidateTradeRelatedCache(symbol, refID) {
    const patterns = [
      `TRADE_HISTORY:${symbol}`,
      `STATISTICS:${refID}`,
      `SYMBOL_DATA:${symbol}`,
      'DASHBOARD_DATA' // Invalidate dashboard cache
    ];

    for (const pattern of patterns) {
      await this.cache.invalidate(pattern);
    }

    // Notify connected GraphQL clients
    this.events.emit('cache.invalidated', { patterns, symbol, refID });
  }
}
```

## 5. Monitoring und Logging Integration

### 5.1 **Unified Logging Service**

```javascript
// services/logging/unifiedLogger.js
const winston = require('winston');

class UnifiedLogger {
  constructor() {
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      transports: [
        new winston.transports.File({ filename: 'logs/api.log' }),
        new winston.transports.Console({
          format: winston.format.simple()
        })
      ]
    });
  }

  logRequest(type, operation, params, duration, success = true) {
    this.logger.info('API Request', {
      apiType: type, // 'REST' or 'GraphQL'
      operation,
      params: this.sanitizeParams(params),
      duration,
      success,
      timestamp: new Date().toISOString()
    });
  }

  logError(type, operation, error, params = {}) {
    this.logger.error('API Error', {
      apiType: type,
      operation,
      error: error.message,
      stack: error.stack,
      params: this.sanitizeParams(params),
      timestamp: new Date().toISOString()
    });
  }

  sanitizeParams(params) {
    // Remove sensitive data from logs
    const sanitized = { ...params };
    if (sanitized.apiKey) {
      sanitized.apiKey = sanitized.apiKey.substring(0, 8) + '...';
    }
    return sanitized;
  }
}

// REST Middleware
const restLoggingMiddleware = (req, res, next) => {
  const startTime = process.hrtime.bigint();
  
  res.on('finish', () => {
    const duration = Number(process.hrtime.bigint() - startTime) / 1000000; // ms
    unifiedLogger.logRequest('REST', req.route?.path || req.path, req.query, duration, res.statusCode < 400);
  });
  
  next();
};

// GraphQL Plugin
const apolloLoggingPlugin = {
  requestDidStart() {
    return {
      didResolveOperation(requestContext) {
        requestContext.startTime = process.hrtime.bigint();
      },
      willSendResponse(requestContext) {
        const duration = Number(process.hrtime.bigint() - requestContext.startTime) / 1000000;
        const operation = requestContext.request.operationName || 'Unknown';
        unifiedLogger.logRequest('GraphQL', operation, requestContext.request.variables, duration, !requestContext.errors);
      }
    };
  }
};
```

### 5.2 **Performance Monitoring**

```javascript
// services/monitoring/performanceMonitor.js
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      rest: {
        requests: 0,
        totalDuration: 0,
        errors: 0
      },
      graphql: {
        requests: 0,
        totalDuration: 0,
        errors: 0,
        complexity: 0
      }
    };
  }

  recordRestRequest(duration, success) {
    this.metrics.rest.requests++;
    this.metrics.rest.totalDuration += duration;
    if (!success) this.metrics.rest.errors++;
  }

  recordGraphQLRequest(duration, success, complexity = 1) {
    this.metrics.graphql.requests++;
    this.metrics.graphql.totalDuration += duration;
    this.metrics.graphql.complexity += complexity;
    if (!success) this.metrics.graphql.errors++;
  }

  getMetrics() {
    return {
      rest: {
        ...this.metrics.rest,
        avgDuration: this.metrics.rest.totalDuration / this.metrics.rest.requests || 0,
        errorRate: this.metrics.rest.errors / this.metrics.rest.requests || 0
      },
      graphql: {
        ...this.metrics.graphql,
        avgDuration: this.metrics.graphql.totalDuration / this.metrics.graphql.requests || 0,
        avgComplexity: this.metrics.graphql.complexity / this.metrics.graphql.requests || 0,
        errorRate: this.metrics.graphql.errors / this.metrics.graphql.requests || 0
      }
    };
  }

  // Endpoint für Monitoring Dashboard
  setupMetricsEndpoint(app) {
    app.get('/metrics', (req, res) => {
      res.json(this.getMetrics());
    });
  }
}
```

## 6. Deployment und Infrastructure

### 6.1 **Docker Setup für Dual-API**

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

# Dependencies
COPY package*.json ./
RUN npm ci --only=production

# Application code
COPY . .

# Expose ports für REST und GraphQL
EXPOSE 8080 8081

# Health check für beide APIs
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node health-check.js

CMD ["node", "server.js"]
```

```javascript
// health-check.js
const http = require('http');

async function checkHealth() {
  try {
    // Check REST API
    const restCheck = await makeRequest('http://localhost:8080/health');
    
    // Check GraphQL API
    const graphqlCheck = await makeRequest('http://localhost:8080/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: '{ __typename }'
      })
    });

    if (restCheck.status === 200 && graphqlCheck.status === 200) {
      process.exit(0);
    } else {
      process.exit(1);
    }
  } catch (error) {
    console.error('Health check failed:', error);
    process.exit(1);
  }
}

checkHealth();
```

### 6.2 **Load Balancer Konfiguration**

```nginx
# nginx.conf
upstream api_backend {
    server api1:8080;
    server api2:8080;
    server api3:8080;
}

server {
    listen 80;
    server_name api.ml-algotrader.com;

    # REST API Routes
    location /api/ {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # REST-spezifische Timeouts
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # GraphQL Endpoint
    location /graphql {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # GraphQL-spezifische Timeouts (länger für komplexe Queries)
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket Subscriptions
    location /subscriptions {
        proxy_pass http://api_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # WebSocket-spezifische Settings
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
    }
}
```

## 7. Testing Strategy

### 7.1 **Shared Test Utilities**

```javascript
// tests/shared/testUtils.js
class DualAPITestSuite {
  constructor(restClient, graphqlClient) {
    this.rest = restClient;
    this.graphql = graphqlClient;
  }

  async testDataConsistency(endpoint, query, params) {
    const restResult = await this.rest.get(endpoint, { params });
    const graphqlResult = await this.graphql.query({
      query,
      variables: params
    });

    // Normalize data formats
    const normalizedRest = this.normalizeRestResponse(restResult.data);
    const normalizedGraphQL = this.normalizeGraphQLResponse(graphqlResult.data);

    expect(normalizedRest).toEqual(normalizedGraphQL);
  }

  async testPerformanceComparison(endpoint, query, params, iterations = 10) {
    const restTimes = [];
    const graphqlTimes = [];

    for (let i = 0; i < iterations; i++) {
      // REST Performance
      const restStart = process.hrtime.bigint();
      await this.rest.get(endpoint, { params });
      const restEnd = process.hrtime.bigint();
      restTimes.push(Number(restEnd - restStart) / 1000000);

      // GraphQL Performance
      const graphqlStart = process.hrtime.bigint();
      await this.graphql.query({ query, variables: params });
      const graphqlEnd = process.hrtime.bigint();
      graphqlTimes.push(Number(graphqlEnd - graphqlStart) / 1000000);
    }

    return {
      rest: {
        avg: restTimes.reduce((a, b) => a + b) / restTimes.length,
        min: Math.min(...restTimes),
        max: Math.max(...restTimes)
      },
      graphql: {
        avg: graphqlTimes.reduce((a, b) => a + b) / graphqlTimes.length,
        min: Math.min(...graphqlTimes),
        max: Math.max(...graphqlTimes)
      }
    };
  }
}
```

### 7.2 **Integration Tests**

```javascript
// tests/integration/dualAPI.test.js
describe('Dual API Integration Tests', () => {
  let testSuite;

  beforeAll(() => {
    testSuite = new DualAPITestSuite(restClient, graphqlClient);
  });

  test('Trade History Data Consistency', async () => {
    await testSuite.testDataConsistency(
      '/api/v1/db/trade_history',
      `
        query TradeHistory($symbol: String!, $days: Int!) {
          tradeHistory(symbol: $symbol, days: $days) {
            dealId
            symbol
            direction
            profit
            openTime
          }
        }
      `,
      { symbol: 'EURUSD', days: 7 }
    );
  });

  test('Performance Comparison', async () => {
    const results = await testSuite.testPerformanceComparison(
      '/api/v1/db/trade_history',
      `
        query TradeHistory($symbol: String!) {
          tradeHistory(symbol: $symbol) {
            profit
            direction
          }
        }
      `,
      { symbol: 'EURUSD' },
      5
    );

    console.log('Performance Results:', results);
    
    // GraphQL sollte nicht mehr als 50% langsamer sein
    expect(results.graphql.avg).toBeLessThan(results.rest.avg * 1.5);
  });
});
```

## 8. Overhead-Analyse

### 8.1 **Development Overhead**

| Aspekt | REST Only | Dual APIs | Overhead |
|--------|-----------|-----------|----------|
| **Initial Setup** | 2 Wochen | 6 Wochen | +200% |
| **Feature Development** | 1x | 1.3x | +30% |
| **Testing** | 1x | 1.8x | +80% |
| **Bug Fixes** | 1x | 1.4x | +40% |
| **Documentation** | 1x | 1.6x | +60% |

### 8.2 **Infrastructure Overhead**

```javascript
// Speicher-Verbrauch Schätzung
const memoryOverhead = {
  graphqlSchema: '~5MB',
  apolloServer: '~10MB', 
  additionalResolvers: '~3MB',
  subscriptionConnections: '~2MB per 100 connections',
  total: '~18MB + subscription overhead'
};

// CPU Overhead
const cpuOverhead = {
  queryParsing: '+5-10% bei komplexen Queries',
  schemaValidation: '+2-3% pro Request',
  subscriptionManagement: '+10-15% bei aktiven Subscriptions'
};

// Network Overhead
const networkOverhead = {
  websocketConnections: 'Konstante Verbindungen für Subscriptions',
  querySize: 'Größere Payloads bei komplexen Queries',
  headerOverhead: '+200-500 bytes pro GraphQL Request'
};
```

### 8.3 **Maintenance Overhead**

```javascript
// Quantifizierte Wartungskosten
const maintenanceOverhead = {
  deployment: {
    restOnly: '30 min',
    dualAPI: '45 min',
    overhead: '+50%'
  },
  monitoring: {
    restOnly: '2 dashboards',
    dualAPI: '4 dashboards',
    overhead: '+100%'
  },
  debugging: {
    restOnly: '1 request trace',
    dualAPI: '2 request traces + resolver debugging',
    overhead: '+80%'
  },
  teamTraining: {
    restOnly: '0 hours',
    dualAPI: '40 hours GraphQL Training',
    overhead: '+∞'
  }
};
```

## 9. Cost-Benefit Analysis

### 9.1 **Kosten des parallelen Betriebs**

**Entwicklungskosten (6 Monate):**
- Zusätzliche Entwicklungszeit: +30-40%
- GraphQL Expertise: 2-3 Wochen Training
- Testing Overhead: +80% Testaufwand
- Documentation: +60% Dokumentationsaufwand

**Operationale Kosten (jährlich):**
- Server Resources: +15-20% Memory/CPU
- Monitoring Tools: +2 zusätzliche Dashboards
- Support Complexity: +40% Debugging-Zeit

**Wartungskosten (laufend):**
- Feature Development: +30% Entwicklungszeit
- Bug Fixes: +40% Aufwand durch Dual-Testing
- Documentation Updates: +60% Aufwand

### 9.2 **Nutzen des parallelen Betriebs**

**Kurzfristige Vorteile:**
- Keine Breaking Changes für bestehende Clients
- Graduelle Migration möglich
- A/B Testing zwischen REST und GraphQL
- Risikoverteilung bei Problemen

**Langfristige Vorteile:**
- Flexibilität bei Client-Anforderungen
- Future-Proof Architecture
- Better Developer Experience für neue Features
- Optimierte Mobile Performance

### 9.3 **ROI Calculation**

```javascript
const roiCalculation = {
  implementationCost: {
    development: 120000, // 6 Monate * 20k/Monat
    training: 15000,
    infrastructure: 5000,
    total: 140000
  },
  
  operationalCostIncrease: {
    annual: 25000 // +20% Infrastruktur + Support
  },

  benefits: {
    fasterFeatureDevelopment: 40000, // 30% Zeitersparnis nach Jahr 1
    improvedMobilePerformance: 15000, // Bessere User Experience
    reducedApiMaintenance: 20000, // Weniger Breaking Changes
    developerProductivity: 25000, // Bessere DX
    annualTotal: 100000
  },

  breakEvenPoint: '18 Monate',
  roi3Years: {
    investment: 215000, // Initial + 3 Jahre operational
    returns: 300000, // 3 Jahre Vorteile
    netRoi: '40%'
  }
};
```

## 10. Empfehlungen

### 10.1 **Parallelbetrieb: JA, aber mit Strategie**

**Empfohlener Ansatz:**
1. **Phase 1 (3 Monate):** GraphQL Proof-of-Concept parallel zu REST
2. **Phase 2 (6 Monate):** Core Features in beiden APIs
3. **Phase 3 (12 Monate):** Graduelle Client-Migration
4. **Phase 4 (18 Monate):** REST Deprecation für unkritische Endpoints

### 10.2 **Technische Umsetzungsempfehlungen**

```javascript
// Prioritäten für Dual-API Implementation
const implementationPriorities = [
  {
    priority: 1,
    component: 'Shared Business Logic Layer',
    effort: '4 Wochen',
    impact: 'Hoch'
  },
  {
    priority: 2,
    component: 'Unified Caching Service',
    effort: '3 Wochen',
    impact: 'Hoch'
  },
  {
    priority: 3,
    component: 'GraphQL Core Schema',
    effort: '4 Wochen',
    impact: 'Mittel'
  },
  {
    priority: 4,
    component: 'Authentication Integration',
    effort: '2 Wochen',
    impact: 'Hoch'
  },
  {
    priority: 5,
    component: 'Monitoring & Logging',
    effort: '3 Wochen',
    impact: 'Mittel'
  },
  {
    priority: 6,
    component: 'Real-time Subscriptions',
    effort: '4 Wochen',
    impact: 'Niedrig'
  }
];
```

### 10.3 **Success Metrics**

```javascript
const successMetrics = {
  technical: {
    codeReuse: '>80% Business Logic geteilt',
    performance: 'GraphQL nicht >30% langsamer als REST',
    uptime: '>99.9% für beide APIs',
    errorRate: '<0.1% für beide APIs'
  },
  
  business: {
    migrationRate: '>50% Clients nach 12 Monaten',
    developmentSpeed: '+25% neue Features nach 6 Monaten',
    maintenanceEffort: '<150% current REST maintenance',
    teamSatisfaction: '>8/10 Developer Experience Score'
  }
};
```

## 11. Fazit

**Parallelbetrieb ist technisch machbar** und bietet strategische Vorteile, erfordert aber:

✅ **Pros:**
- Risikoarme Migration
- Graduelle Client-Transition
- Flexibilität für verschiedene Use Cases
- Future-Proof Architecture

❌ **Cons:**
- +30-40% Entwicklungsoverhead
- Komplexere Testing und Debugging
- Höhere Infrastruktur-Kosten
- Längere Time-to-Market für neue Features

**Empfehlung:** Beginnen Sie mit einem **3-monatigen PoC** für kritische Endpoints, um konkrete Metriken zu sammeln, bevor Sie sich für den vollen parallelen Betrieb entscheiden.

Der parallele Betrieb rechtfertigt sich vor allem, wenn:
- Multiple Client-Anwendungen mit verschiedenen Anforderungen
- Längerfristige API-Evolution geplant
- Team bereit für GraphQL-Expertise Investment
- Business-Case für Real-time Features vorhanden
- Vue.js 3 Frontend profitiert von besserer Developer Experience

## 12. Vue.js 3 Client Implementation

### 12.1 **Setup für Dual-API Client**

```javascript
// src/plugins/api.js
import axios from 'axios';
import { ApolloClient, InMemoryCache, createHttpLink, split } from '@apollo/client/core';
import { getMainDefinition } from '@apollo/client/utilities';
import { GraphQLWsLink } from '@apollo/client/link/subscriptions';
import { createClient } from 'graphql-ws';

// REST API Client
const restClient = axios.create({
  baseURL: 'https://api.ml-algotrader.com/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': import.meta.env.VITE_API_KEY
  }
});

// GraphQL HTTP Link
const httpLink = createHttpLink({
  uri: 'https://api.ml-algotrader.com/graphql',
  headers: {
    'X-API-Key': import.meta.env.VITE_API_KEY
  }
});

// GraphQL WebSocket Link für Subscriptions
const wsLink = new GraphQLWsLink(
  createClient({
    url: 'wss://api.ml-algotrader.com/subscriptions',
    connectionParams: {
      'x-api-key': import.meta.env.VITE_API_KEY
    },
    retryAttempts: 5,
    shouldRetry: () => true
  })
);

// Split Link - WebSocket für Subscriptions, HTTP für Queries/Mutations
const splitLink = split(
  ({ query }) => {
    const definition = getMainDefinition(query);
    return (
      definition.kind === 'OperationDefinition' &&
      definition.operation === 'subscription'
    );
  },
  wsLink,
  httpLink
);

// Apollo Client
const apolloClient = new ApolloClient({
  link: splitLink,
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          tradeHistory: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming];
            }
          }
        }
      }
    }
  }),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all'
    },
    query: {
      errorPolicy: 'all'
    }
  }
});

export { restClient, apolloClient };
```

### 12.2 **Vue 3 Composables für Dual-API**

```javascript
// src/composables/useTradeHistory.js
import { ref, computed, watchEffect } from 'vue';
import { useQuery } from '@vue/apollo-composable';
import { restClient } from '@/plugins/api';
import { TRADE_HISTORY_QUERY } from '@/graphql/queries';

export function useTradeHistory(symbol, days = 7, apiType = 'auto') {
  const loading = ref(false);
  const error = ref(null);
  const data = ref([]);

  // REST Implementation
  const fetchViaREST = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await restClient.get('/db/trade_history', {
        params: { symbol: symbol.value, days: days.value }
      });
      
      data.value = response.data.results || [];
    } catch (err) {
      error.value = err.response?.data?.message || err.message;
    } finally {
      loading.value = false;
    }
  };

  // GraphQL Implementation
  const {
    result: graphqlResult,
    loading: graphqlLoading,
    error: graphqlError,
    refetch: graphqlRefetch
  } = useQuery(
    TRADE_HISTORY_QUERY,
    () => ({ symbol: symbol.value, days: days.value }),
    {
      enabled: computed(() => !!symbol.value && apiType.value !== 'rest'),
      errorPolicy: 'all'
    }
  );

  // Auto-select API based on performance or preference
  const shouldUseGraphQL = computed(() => {
    if (apiType.value === 'rest') return false;
    if (apiType.value === 'graphql') return true;
    // Auto-selection logic
    return days.value <= 30; // GraphQL für kleinere Datasets
  });

  // Reactive data source
  const trades = computed(() => {
    if (shouldUseGraphQL.value) {
      return graphqlResult.value?.tradeHistory || [];
    }
    return data.value;
  });

  const isLoading = computed(() => {
    return shouldUseGraphQL.value ? graphqlLoading.value : loading.value;
  });

  const currentError = computed(() => {
    return shouldUseGraphQL.value ? graphqlError.value : error.value;
  });

  // Watch for changes and refetch
  watchEffect(() => {
    if (symbol.value && !shouldUseGraphQL.value) {
      fetchViaREST();
    }
  });

  const refetch = () => {
    if (shouldUseGraphQL.value) {
      graphqlRefetch();
    } else {
      fetchViaREST();
    }
  };

  return {
    trades,
    loading: isLoading,
    error: currentError,
    refetch,
    apiType: shouldUseGraphQL
  };
}
```

### 12.3 **Trading Dashboard Component**

```vue
<!-- src/components/TradingDashboard.vue -->
<template>
  <div class="trading-dashboard">
    <!-- API Toggle -->
    <div class="api-selector">
      <label>
        <input
          type="radio"
          :value="'auto'"
          v-model="selectedApi"
        /> Auto
      </label>
      <label>
        <input
          type="radio"
          :value="'rest'"
          v-model="selectedApi"
        /> REST
      </label>
      <label>
        <input
          type="radio"
          :value="'graphql'"
          v-model="selectedApi"
        /> GraphQL
      </label>
    </div>

    <!-- Performance Indicator -->
    <div class="performance-indicator">
      <span>API: {{ currentApiType ? 'GraphQL' : 'REST' }}</span>
      <span>Latency: {{ latency }}ms</span>
      <span>Cache: {{ cacheStatus }}</span>
    </div>

    <!-- Symbol Selection -->
    <div class="symbol-selector">
      <select v-model="selectedSymbol">
        <option value="EURUSD">EUR/USD</option>
        <option value="GBPUSD">GBP/USD</option>
        <option value="USDJPY">USD/JPY</option>
      </select>
      <input
        type="number"
        v-model="days"
        min="1"
        max="365"
        placeholder="Days"
      />
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading">
      Loading trade history...
    </div>

    <!-- Error State -->
    <div v-if="error" class="error">
      Error: {{ error }}
      <button @click="refetch">Retry</button>
    </div>

    <!-- Trade History -->
    <div v-if="!loading && trades.length" class="trade-list">
      <h3>Recent Trades ({{ trades.length }})</h3>
      <div class="trades">
        <div
          v-for="trade in trades"
          :key="trade.dealId"
          class="trade-item"
          :class="{ 'profit': trade.profit > 0, 'loss': trade.profit < 0 }"
        >
          <span class="symbol">{{ trade.symbol }}</span>
          <span class="direction">{{ trade.direction }}</span>
          <span class="profit">${{ trade.profit?.toFixed(2) }}</span>
          <span class="time">{{ formatTime(trade.openTime) }}</span>
        </div>
      </div>
    </div>

    <!-- Real-time Updates (GraphQL Only) -->
    <div v-if="currentApiType && realtimeUpdates.length" class="realtime-updates">
      <h4>Live Updates</h4>
      <div
        v-for="update in realtimeUpdates"
        :key="update.id"
        class="update-item"
      >
        {{ update.message }}
      </div>
    </div>

    <!-- Statistics Comparison -->
    <div class="statistics-grid">
      <TradingStatistics
        :symbol="selectedSymbol"
        :api-type="selectedApi"
      />
      <SymbolInfo
        :symbol="selectedSymbol"
        :api-type="selectedApi"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useTradeHistory } from '@/composables/useTradeHistory';
import { useRealtimeUpdates } from '@/composables/useRealtimeUpdates';
import TradingStatistics from './TradingStatistics.vue';
import SymbolInfo from './SymbolInfo.vue';

const selectedSymbol = ref('EURUSD');
const days = ref(7);
const selectedApi = ref('auto');
const latency = ref(0);
const cacheStatus = ref('MISS');

// Use trading data
const {
  trades,
  loading,
  error,
  refetch,
  apiType: currentApiType
} = useTradeHistory(selectedSymbol, days, selectedApi);

// Real-time updates (GraphQL Subscriptions)
const { updates: realtimeUpdates } = useRealtimeUpdates(selectedSymbol);

// Performance tracking
const startTime = ref(0);

watch([loading], ([newLoading]) => {
  if (newLoading) {
    startTime.value = performance.now();
  } else if (startTime.value) {
    latency.value = Math.round(performance.now() - startTime.value);
    cacheStatus.value = latency.value < 100 ? 'HIT' : 'MISS';
  }
});

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString();
};

onMounted(() => {
  refetch();
});
</script>

<style scoped>
.trading-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.api-selector {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 5px;
}

.performance-indicator {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #666;
}

.symbol-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.trade-list {
  margin-bottom: 30px;
}

.trades {
  display: grid;
  gap: 10px;
}

.trade-item {
  display: grid;
  grid-template-columns: 1fr 80px 100px 120px;
  gap: 15px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.trade-item.profit {
  border-left: 4px solid #4caf50;
}

.trade-item.loss {
  border-left: 4px solid #f44336;
}

.statistics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.loading, .error {
  padding: 20px;
  text-align: center;
}

.error {
  color: #f44336;
  background: #ffebee;
  border-radius: 5px;
}

.realtime-updates {
  margin-bottom: 20px;
  padding: 15px;
  background: #e3f2fd;
  border-radius: 5px;
}

.update-item {
  padding: 5px 0;
  border-bottom: 1px solid #bbdefb;
}
</style>
```

### 12.4 **Real-time Subscriptions Composable**

```javascript
// src/composables/useRealtimeUpdates.js
import { ref, onUnmounted, watchEffect } from 'vue';
import { useSubscription } from '@vue/apollo-composable';
import { PRICE_UPDATES_SUBSCRIPTION, TRADE_EXECUTIONS_SUBSCRIPTION } from '@/graphql/subscriptions';

export function useRealtimeUpdates(symbol) {
  const updates = ref([]);
  const connectionStatus = ref('disconnected');
  const lastUpdate = ref(null);

  // Price Updates Subscription
  const {
    result: priceResult,
    error: priceError,
    start: startPriceSubscription,
    stop: stopPriceSubscription
  } = useSubscription(
    PRICE_UPDATES_SUBSCRIPTION,
    () => ({ symbols: [symbol.value] }),
    {
      enabled: false // Manual control
    }
  );

  // Trade Executions Subscription
  const {
    result: tradeResult,
    error: tradeError,
    start: startTradeSubscription,
    stop: stopTradeSubscription
  } = useSubscription(
    TRADE_EXECUTIONS_SUBSCRIPTION,
    () => ({ refID: 'IG-P1' }),
    {
      enabled: false
    }
  );

  // Handle price updates
  watchEffect(() => {
    if (priceResult.value?.priceUpdates) {
      const update = priceResult.value.priceUpdates;
      updates.value.unshift({
        id: Date.now(),
        type: 'price',
        message: `${update.symbol}: ${update.bid}/${update.ask}`,
        timestamp: update.timestamp
      });
      
      // Keep only last 50 updates
      if (updates.value.length > 50) {
        updates.value = updates.value.slice(0, 50);
      }
      
      lastUpdate.value = new Date();
      connectionStatus.value = 'connected';
    }
  });

  // Handle trade executions
  watchEffect(() => {
    if (tradeResult.value?.tradeExecutions) {
      const execution = tradeResult.value.tradeExecutions;
      updates.value.unshift({
        id: Date.now(),
        type: 'trade',
        message: `Trade ${execution.dealId}: ${execution.status} at ${execution.executionPrice}`,
        timestamp: new Date().toISOString()
      });
      
      lastUpdate.value = new Date();
    }
  });

  // Error handling
  watchEffect(() => {
    if (priceError.value || tradeError.value) {
      connectionStatus.value = 'error';
      console.error('Subscription error:', priceError.value || tradeError.value);
    }
  });

  // Start subscriptions
  const startSubscriptions = () => {
    startPriceSubscription();
    startTradeSubscription();
    connectionStatus.value = 'connecting';
  };

  // Stop subscriptions
  const stopSubscriptions = () => {
    stopPriceSubscription();
    stopTradeSubscription();
    connectionStatus.value = 'disconnected';
  };

  // Auto-start/stop based on symbol
  watchEffect(() => {
    if (symbol.value) {
      startSubscriptions();
    }
  });

  onUnmounted(() => {
    stopSubscriptions();
  });

  return {
    updates,
    connectionStatus,
    lastUpdate,
    startSubscriptions,
    stopSubscriptions
  };
}
```

### 12.5 **GraphQL Queries und Mutations**

```javascript
// src/graphql/queries.js
import { gql } from '@apollo/client/core';

export const TRADE_HISTORY_QUERY = gql`
  query TradeHistory($symbol: String!, $days: Int!, $limit: Int) {
    tradeHistory(symbol: $symbol, days: $days, limit: $limit) {
      dealId
      symbol
      direction
      size
      openPrice
      closePrice
      profit
      commission
      openTime
      closeTime
      strategy {
        name
        id
      }
    }
  }
`;

export const TRADING_DASHBOARD_QUERY = gql`
  query TradingDashboard($symbol: String!, $refID: String!) {
    symbol(name: $symbol) {
      name
      currentPrice
      bid
      ask
      spread
      pivotPoints {
        s1
        s2
        r1
        r2
        pivot
      }
      tradeHistory(days: 7) {
        dealId
        direction
        profit
        openTime
      }
      aiPredictions {
        direction
        confidence
        timestamp
      }
      symbolSetup {
        deadZoneLow
        deadZoneHigh
        isActive
      }
    }
    
    account(refID: $refID) {
      currentEquity
      dailyProfit
      settings {
        riskPerTrade
        maxDrawdown
      }
      statistics {
        daily {
          profit
          trades
          winRate
        }
      }
    }
  }
`;

export const SYMBOL_STATISTICS_QUERY = gql`
  query SymbolStatistics($symbol: String!, $period: String!) {
    statistics(symbol: $symbol, period: $period) {
      totalTrades
      winRate
      avgProfit
      maxDrawdown
      sharpeRatio
      profitFactor
    }
  }
`;
```

```javascript
// src/graphql/mutations.js
import { gql } from '@apollo/client/core';

export const UPDATE_SYMBOL_SETUP_MUTATION = gql`
  mutation UpdateSymbolSetup($symbol: String!, $input: SymbolSetupInput!) {
    updateSymbolSetup(symbol: $symbol, input: $input) {
      success
      message
      symbolSetup {
        deadZoneLow
        deadZoneHigh
        isActive
      }
    }
  }
`;

export const EXECUTE_TRADE_MUTATION = gql`
  mutation ExecuteTrade($input: TradeInput!) {
    executeTrade(input: $input) {
      success
      dealId
      executionPrice
      message
      errors {
        field
        message
      }
    }
  }
`;
```

```javascript
// src/graphql/subscriptions.js
import { gql } from '@apollo/client/core';

export const PRICE_UPDATES_SUBSCRIPTION = gql`
  subscription PriceUpdates($symbols: [String!]!) {
    priceUpdates(symbols: $symbols) {
      symbol
      bid
      ask
      timestamp
    }
  }
`;

export const TRADE_EXECUTIONS_SUBSCRIPTION = gql`
  subscription TradeExecutions($refID: String!) {
    tradeExecutions(refID: $refID) {
      dealId
      status
      executionPrice
      timestamp
    }
  }
`;

export const ACCOUNT_UPDATES_SUBSCRIPTION = gql`
  subscription AccountUpdates($refID: String!) {
    accountUpdates(refID: $refID) {
      equity
      margin
      freeMargin
      timestamp
    }
  }
`;
```

### 12.6 **State Management mit Pinia**

```javascript
// src/stores/trading.js
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { restClient, apolloClient } from '@/plugins/api';
import { TRADING_DASHBOARD_QUERY } from '@/graphql/queries';

export const useTradingStore = defineStore('trading', () => {
  // State
  const selectedSymbol = ref('EURUSD');
  const selectedRefID = ref('IG-P1');
  const apiPreference = ref('auto'); // 'auto', 'rest', 'graphql'
  const dashboardData = ref(null);
  const loading = ref(false);
  const error = ref(null);
  const lastUpdated = ref(null);

  // Performance tracking
  const apiPerformance = ref({
    rest: { avgLatency: 0, requests: 0, errors: 0 },
    graphql: { avgLatency: 0, requests: 0, errors: 0 }
  });

  // Computed
  const shouldUseGraphQL = computed(() => {
    if (apiPreference.value === 'rest') return false;
    if (apiPreference.value === 'graphql') return true;
    
    // Auto-selection based on performance
    const restAvg = apiPerformance.value.rest.avgLatency;
    const graphqlAvg = apiPerformance.value.graphql.avgLatency;
    
    if (restAvg > 0 && graphqlAvg > 0) {
      return graphqlAvg < restAvg * 1.2; // GraphQL wenn nicht mehr als 20% langsamer
    }
    
    return true; // Default zu GraphQL
  });

  const currentAccount = computed(() => {
    return dashboardData.value?.account || null;
  });

  const currentSymbolData = computed(() => {
    return dashboardData.value?.symbol || null;
  });

  // Actions
  const fetchDashboardData = async () => {
    loading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
      if (shouldUseGraphQL.value) {
        await fetchViaGraphQL();
      } else {
        await fetchViaREST();
      }
      
      lastUpdated.value = new Date();
    } catch (err) {
      error.value = err.message;
      console.error('Dashboard fetch error:', err);
    } finally {
      loading.value = false;
      
      // Track performance
      const duration = performance.now() - startTime;
      const apiType = shouldUseGraphQL.value ? 'graphql' : 'rest';
      updatePerformanceMetrics(apiType, duration, !error.value);
    }
  };

  const fetchViaGraphQL = async () => {
    const result = await apolloClient.query({
      query: TRADING_DASHBOARD_QUERY,
      variables: {
        symbol: selectedSymbol.value,
        refID: selectedRefID.value
      },
      fetchPolicy: 'cache-first'
    });

    dashboardData.value = result.data;
  };

  const fetchViaREST = async () => {
    // Multiple REST calls to gather same data
    const [symbolData, accountData, tradeHistory, statistics] = await Promise.all([
      restClient.get(`/db/symbol_setup/${selectedSymbol.value}`),
      restClient.get(`/db/account/${selectedRefID.value}`),
      restClient.get('/db/trade_history', {
        params: { symbol: selectedSymbol.value, days: 7 }
      }),
      restClient.get('/db/statistics/current_day', {
        params: { refID: selectedRefID.value }
      })
    ]);

    // Combine REST responses to match GraphQL structure
    dashboardData.value = {
      symbol: {
        name: selectedSymbol.value,
        ...symbolData.data,
        tradeHistory: tradeHistory.data.results || []
      },
      account: {
        ...accountData.data,
        statistics: {
          daily: statistics.data
        }
      }
    };
  };

  const updatePerformanceMetrics = (apiType, duration, success) => {
    const metrics = apiPerformance.value[apiType];
    const newCount = metrics.requests + 1;
    
    metrics.avgLatency = (metrics.avgLatency * metrics.requests + duration) / newCount;
    metrics.requests = newCount;
    
    if (!success) {
      metrics.errors++;
    }
  };

  const setSymbol = (symbol) => {
    selectedSymbol.value = symbol;
    fetchDashboardData();
  };

  const setApiPreference = (preference) => {
    apiPreference.value = preference;
    fetchDashboardData();
  };

  const refreshData = () => {
    fetchDashboardData();
  };

  return {
    // State
    selectedSymbol,
    selectedRefID,
    apiPreference,
    dashboardData,
    loading,
    error,
    lastUpdated,
    apiPerformance,
    
    // Computed
    shouldUseGraphQL,
    currentAccount,
    currentSymbolData,
    
    // Actions
    fetchDashboardData,
    setSymbol,
    setApiPreference,
    refreshData
  };
});
```

### 12.7 **Performance Comparison Component**

```vue
<!-- src/components/ApiPerformanceComparison.vue -->
<template>
  <div class="api-performance">
    <h3>API Performance Comparison</h3>
    
    <div class="metrics-grid">
      <div class="metric-card rest">
        <h4>REST API</h4>
        <div class="metrics">
          <div class="metric">
            <span class="label">Avg Latency:</span>
            <span class="value">{{ restMetrics.avgLatency.toFixed(1) }}ms</span>
          </div>
          <div class="metric">
            <span class="label">Requests:</span>
            <span class="value">{{ restMetrics.requests }}</span>
          </div>
          <div class="metric">
            <span class="label">Success Rate:</span>
            <span class="value">{{ restSuccessRate.toFixed(1) }}%</span>
          </div>
        </div>
      </div>

      <div class="metric-card graphql">
        <h4>GraphQL API</h4>
        <div class="metrics">
          <div class="metric">
            <span class="label">Avg Latency:</span>
            <span class="value">{{ graphqlMetrics.avgLatency.toFixed(1) }}ms</span>
          </div>
          <div class="metric">
            <span class="label">Requests:</span>
            <span class="value">{{ graphqlMetrics.requests }}</span>
          </div>
          <div class="metric">
            <span class="label">Success Rate:</span>
            <span class="value">{{ graphqlSuccessRate.toFixed(1) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Chart -->
    <div class="performance-chart">
      <canvas ref="chartCanvas" width="400" height="200"></canvas>
    </div>

    <!-- API Recommendation -->
    <div class="recommendation" :class="recommendedApi">
      <strong>Recommended:</strong> {{ recommendedApi.toUpperCase() }}
      <span class="reason">{{ recommendationReason }}</span>
    </div>

    <!-- Test Buttons -->
    <div class="test-controls">
      <button @click="runPerformanceTest('rest')" :disabled="testing">
        Test REST Performance
      </button>
      <button @click="runPerformanceTest('graphql')" :disabled="testing">
        Test GraphQL Performance
      </button>
      <button @click="runPerformanceTest('both')" :disabled="testing">
        Compare Both APIs
      </button>
    </div>

    <div v-if="testing" class="testing-indicator">
      Running performance test... {{ testProgress }}%
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { useTradingStore } from '@/stores/trading';
import { restClient, apolloClient } from '@/plugins/api';
import { TRADE_HISTORY_QUERY } from '@/graphql/queries';

const store = useTradingStore();
const chartCanvas = ref(null);
const testing = ref(false);
const testProgress = ref(0);

const restMetrics = computed(() => store.apiPerformance.rest);
const graphqlMetrics = computed(() => store.apiPerformance.graphql);

const restSuccessRate = computed(() => {
  const total = restMetrics.value.requests;
  return total > 0 ? ((total - restMetrics.value.errors) / total) * 100 : 100;
});

const graphqlSuccessRate = computed(() => {
  const total = graphqlMetrics.value.requests;
  return total > 0 ? ((total - graphqlMetrics.value.errors) / total) * 100 : 100;
});

const recommendedApi = computed(() => {
  const restLatency = restMetrics.value.avgLatency;
  const graphqlLatency = graphqlMetrics.value.avgLatency;
  
  if (restLatency === 0 && graphqlLatency === 0) return 'graphql';
  if (restLatency === 0) return 'graphql';
  if (graphqlLatency === 0) return 'rest';
  
  // Consider success rate as well
  const restScore = restLatency * (2 - restSuccessRate.value / 100);
  const graphqlScore = graphqlLatency * (2 - graphqlSuccessRate.value / 100);
  
  return restScore < graphqlScore ? 'rest' : 'graphql';
});

const recommendationReason = computed(() => {
  const api = recommendedApi.value;
  if (api === 'rest') {
    return `${restMetrics.value.avgLatency.toFixed(1)}ms avg latency`;
  } else {
    return `${graphqlMetrics.value.avgLatency.toFixed(1)}ms avg latency + better caching`;
  }
});

const runPerformanceTest = async (apiType) => {
  testing.value = true;
  testProgress.value = 0;
  
  const iterations = 10;
  const testSymbols = ['EURUSD', 'GBPUSD', 'USDJPY'];
  
  try {
    if (apiType === 'rest' || apiType === 'both') {
      await testRestPerformance(iterations, testSymbols);
    }
    
    if (apiType === 'graphql' || apiType === 'both') {
      await testGraphQLPerformance(iterations, testSymbols);
    }
    
    updateChart();
  } finally {
    testing.value = false;
    testProgress.value = 0;
  }
};

const testRestPerformance = async (iterations, symbols) => {
  const step = 50 / (iterations * symbols.length);
  
  for (let i = 0; i < iterations; i++) {
    for (const symbol of symbols) {
      const start = performance.now();
      
      try {
        await restClient.get('/db/trade_history', {
          params: { symbol, days: 7 }
        });
        
        const duration = performance.now() - start;
        updateMetrics('rest', duration, true);
      } catch (error) {
        const duration = performance.now() - start;
        updateMetrics('rest', duration, false);
      }
      
      testProgress.value += step;
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
};

const testGraphQLPerformance = async (iterations, symbols) => {
  const step = 50 / (iterations * symbols.length);
  
  for (let i = 0; i < iterations; i++) {
    for (const symbol of symbols) {
      const start = performance.now();
      
      try {
        await apolloClient.query({
          query: TRADE_HISTORY_QUERY,
          variables: { symbol, days: 7 },
          fetchPolicy: 'no-cache'
        });
        
        const duration = performance.now() - start;
        updateMetrics('graphql', duration, true);
      } catch (error) {
        const duration = performance.now() - start;
        updateMetrics('graphql', duration, false);
      }
      
      testProgress.value += step;
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
};

const updateMetrics = (apiType, duration, success) => {
  const metrics = store.apiPerformance[apiType];
  const newCount = metrics.requests + 1;
  
  metrics.avgLatency = (metrics.avgLatency * metrics.requests + duration) / newCount;
  metrics.requests = newCount;
  
  if (!success) {
    metrics.errors++;
  }
};

const updateChart = async () => {
  await nextTick();
  
  const ctx = chartCanvas.value?.getContext('2d');
  if (!ctx) return;
  
  // Simple chart drawing
  ctx.clearRect(0, 0, 400, 200);
  
  const restLatency = restMetrics.value.avgLatency;
  const graphqlLatency = graphqlMetrics.value.avgLatency;
  const maxLatency = Math.max(restLatency, graphqlLatency, 100);
  
  // Draw bars
  ctx.fillStyle = '#2196F3';
  ctx.fillRect(50, 200 - (restLatency / maxLatency) * 150, 100, (restLatency / maxLatency) * 150);
  
  ctx.fillStyle = '#4CAF50';
  ctx.fillRect(200, 200 - (graphqlLatency / maxLatency) * 150, 100, (graphqlLatency / maxLatency) * 150);
  
  // Labels
  ctx.fillStyle = '#333';
  ctx.font = '14px Arial';
  ctx.fillText('REST', 75, 190);
  ctx.fillText('GraphQL', 215, 190);
  ctx.fillText(`${restLatency.toFixed(1)}ms`, 60, 180);
  ctx.fillText(`${graphqlLatency.toFixed(1)}ms`, 210, 180);
};

onMounted(() => {
  updateChart();
});
</script>

<style scoped>
.api-performance {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.metric-card {
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #eee;
}

.metric-card.rest {
  border-left: 4px solid #2196F3;
}

.metric-card.graphql {
  border-left: 4px solid #4CAF50;
}

.metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.label {
  font-weight: 500;
}

.value {
  font-family: monospace;
  font-weight: bold;
}

.performance-chart {
  margin: 20px 0;
  text-align: center;
}

.recommendation {
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.recommendation.rest {
  background: #e3f2fd;
  border-left: 4px solid #2196F3;
}

.recommendation.graphql {
  background: #e8f5e8;
  border-left: 4px solid #4CAF50;
}

.reason {
  margin-left: 10px;
  font-style: italic;
  color: #666;
}

.test-controls {
  display: flex;
  gap: 10px;
}

.test-controls button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.test-controls button:hover {
  background: #f5f5f5;
}

.test-controls button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.testing-indicator {
  margin-top: 10px;
  padding: 10px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  text-align: center;
}
</style>
```

### 12.8 **Migration Guide für bestehende Vue.js Apps**

```javascript
// Step 1: Install dependencies
// npm install @apollo/client @vue/apollo-composable graphql graphql-ws

// Step 2: Update main.js
// src/main.js
import { createApp } from 'vue';
import { DefaultApolloClient } from '@vue/apollo-composable';
import { createPinia } from 'pinia';
import App from './App.vue';
import { apolloClient } from './plugins/api';

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.provide(DefaultApolloClient, apolloClient);
app.mount('#app');

// Step 3: Migration Wrapper für bestehende Components
// src/utils/apiMigrationHelper.js
export class ApiMigrationHelper {
  static wrapExistingRestCall(restFunction, graphqlQuery, variables) {
    return async function(...args) {
      const useGraphQL = shouldUseGraphQLHeuristic(args);
      
      if (useGraphQL) {
        try {
          const result = await apolloClient.query({
            query: graphqlQuery,
            variables: variables(...args)
          });
          return normalizeGraphQLToRestFormat(result.data);
        } catch (error) {
          console.warn('GraphQL fallback to REST:', error);
          return await restFunction(...args);
        }
      } else {
        return await restFunction(...args);
      }
    };
  }

  static shouldUseGraphQLHeuristic(args) {
    // Heuristics to decide when to use GraphQL
    // e.g., complex queries, real-time needs, etc.
    return Math.random() > 0.5; // Example: 50% chance
  }

  static normalizeGraphQLToRestFormat(graphqlData) {
    // Transform GraphQL response to match existing REST format
    return {
      status: 'success',
      results: graphqlData,
      count: Array.isArray(graphqlData) ? graphqlData.length : 1
    };
  }
}

// Step 4: Gradual migration example
// Before (existing REST component)
export default {
  async mounted() {
    const response = await restClient.get('/db/trade_history', {
      params: { symbol: 'EURUSD', days: 7 }
    });
    this.trades = response.data.results;
  }
};

// After (dual API support)
export default {
  async mounted() {
    const wrappedFunction = ApiMigrationHelper.wrapExistingRestCall(
      (symbol, days) => restClient.get('/db/trade_history', {
        params: { symbol, days }
      }),
      TRADE_HISTORY_QUERY,
      (symbol, days) => ({ symbol, days })
    );
    
    const response = await wrappedFunction('EURUSD', 7);
    this.trades = response.results || response;
  }
};
```

### 12.9 **Vue.js 3 Vorteile für Dual-API Setup**

**Entwicklungsvorteile:**
- **Composition API**: Bessere Code-Reuse zwischen REST und GraphQL Logik
- **Reactivity System**: Automatische UI-Updates bei API-Wechsel
- **TypeScript Support**: Bessere Type Safety für beide APIs
- **Composables**: Wiederverwendbare API-Logik

**Performance Vorteile:**
- **Lazy Loading**: Nur benötigte API-Clients laden
- **Caching**: Intelligentes Caching zwischen REST und GraphQL
- **Bundle Splitting**: Separate Chunks für GraphQL-spezifischen Code
- **Tree Shaking**: Ungenutzte API-Code automatisch entfernen

**Developer Experience:**
- **Hot Reload**: Funktioniert mit beiden API-Typen
- **DevTools**: Vue DevTools zeigen beide API-States
- **Error Handling**: Einheitliche Error-Behandlung
- **Testing**: Gleiche Test-Patterns für beide APIs