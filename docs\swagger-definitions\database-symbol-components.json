{"components": {"schemas": {"SymbolPipValue": {"type": "object", "properties": {"symbol": {"type": "string", "description": "Trading-Symbol", "example": "EURUSD"}, "pipValuePerLotInProfitEUR": {"type": "number", "format": "float", "description": "Pip-Wert pro Lot in EUR", "example": 10.0}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Failed to fetch pip values"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getAllUsedSymbolsWithPipValues"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Invalid parameter", "function": "getAllUsedSymbolsWithPipValues"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch pip values", "function": "getAllUsedSymbolsWithPipValues"}}}}}}}