const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam, validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');

async function getDayStatisticsIndependent(refID, startdate) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getDayStatisticsIndependent', 'Starting day statistics retrieval', {
            refID,
            startdate
        });

        // Validate refID
        refID = validateStringParam(refID, {
            required: true,
            paramName: 'refID'
        });

        // Handle default startdate
        if (!startdate) {
            const currentDate = new Date();
            const p_date = new Date();
            p_date.setDate(currentDate.getDate() - 30);
            startdate = p_date.toLocaleString('sv-SE', { 
                year: 'numeric', 
                month: '2-digit', 
                day: '2-digit' 
            });
        }

        const query = {
            sql: 'SELECT date(t.exit_time) as date, round(min(profit),0) AS min_profit, round(max(profit),0) AS max_profit, COUNT(*) AS cnt_trades, round(SUM(profit),0) AS sum_profit \
             FROM `trades_history` t, `accounts` a \
             WHERE t.account=a.account_id \
              AND a.refId=? \
              AND exit_time >= ? \
            GROUP BY date(t.exit_time) order by date(t.exit_time)',
            bigIntAsNumber: true
        };

        const result = await executeQuery(query, [refID, startdate]);

        log(LOG_LEVELS.INFO, 'getDayStatisticsIndependent', 'Successfully retrieved day statistics', {
            refID,
            startdate,
            resultCount: result?.length || 0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getDayStatisticsIndependent', 'Failed to fetch day statistics', {
            error: err.message,
            stack: err.stack,
            refID,
            startdate
        });
        throw err;
    } finally {
        logPerformance('getDayStatisticsIndependent', startTime);
    }
}

async function getDayStatistics(req, res) {
    try {
        const result = await withCacheWrapper(
            'GENERAL',
            'getDayStatistics',
            () => getDayStatisticsIndependent(req.query.refID, req.query.startdate),
            [req.query.refID, req.query.startdate]
        );
        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getDayStatistics');
        res.status(errorResponse.status).json(errorResponse);
    }
}

/**
 * Lädt Statistiken für den aktuellen Tag
 */
async function getCurrentDayStatisticsIndependent(refID) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getCurrentDayStatisticsIndependent', 'Starting current day statistics retrieval', {
            refID
        });

        refID = validateStringParam(refID, {
            required: true,
            paramName: 'refID'
        });

        const query = {
            sql: 'SELECT YEAR(t.exit_time) AS YEAR, MONTH(t.exit_time) AS MONTH, DATE(t.exit_time) AS DATE, ROUND(MIN(profit),0) AS min_profit, ROUND(MAX(profit),0) AS max_profit, COUNT(*) AS cnt_trades, ROUND(SUM(profit),0) AS sum_profit\
                    FROM `trades_history` t, `accounts` a, (SELECT ACCOUNT, DATE(MAX(exit_time)) AS maxdate FROM `trades_history` GROUP BY account) md \
                   WHERE t.account=a.account_id AND a.refId=? AND DATE(t.exit_time)=md.maxdate AND md.account=a.account_id',
            bigIntAsNumber: true
        };

        const result = await executeQuery(query, [refID]);

        log(LOG_LEVELS.INFO, 'getCurrentDayStatisticsIndependent', 'Successfully retrieved current day statistics', {
            refID,
            resultCount: result?.length || 0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getCurrentDayStatisticsIndependent', 'Failed to fetch current day statistics', {
            error: err.message,
            stack: err.stack,
            refID
        });
        throw err;
    } finally {
        logPerformance('getCurrentDayStatisticsIndependent', startTime);
    }
}

async function getCurrentDayStatistics(req, res) {
    try {
        const result = await withCacheWrapper(
            'GENERAL',
            'getCurrentDayStatistics',
            () => getCurrentDayStatisticsIndependent(req.query.refID),
            [req.query.refID]
        );
        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getCurrentDayStatistics');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getDayStatistics,
    getDayStatisticsIndependent,
    getCurrentDayStatistics,
    getCurrentDayStatisticsIndependent
};
