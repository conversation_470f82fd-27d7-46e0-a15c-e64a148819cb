# Microsoft ToDo Integration - Environment Variables

## Required Environment Variables

Die folgenden Umgebungsvariablen müssen für die Microsoft ToDo Integration konfiguriert werden:

### Microsoft Azure App Registration

```env
# Microsoft Tenant ID (Directory ID)
MICROSOFT_TENANT_ID=your-tenant-id-here

# Microsoft Application (Client) ID
MICROSOFT_CLIENT_ID=your-client-id-here

# Microsoft Client Secret
MICROSOFT_CLIENT_SECRET=your-client-secret-here
```

## Azure App Registration Setup

### 1. Azure Portal Setup

1. Gehe zu [Azure Portal](https://portal.azure.com)
2. Navigiere zu "Azure Active Directory" > "App registrations"
3. Klicke auf "New registration"
4. Konfiguriere die App:
   - **Name**: `Algotrader-API-ToDo-Integration`
   - **Supported account types**: `Accounts in this organizational directory only`
   - **Redirect URI**: Nicht erforderlich für Client Credentials Flow

### 2. API Permissions

Nach der App-Erstellung:

1. <PERSON><PERSON><PERSON> zu "API permissions"
2. Klicke auf "Add a permission"
3. <PERSON><PERSON><PERSON><PERSON> "Microsoft Graph"
4. W<PERSON>hle "Application permissions"
5. <PERSON>e und füge hinzu:
   - `Tasks.ReadWrite` - Lesen und Schreiben von Aufgaben
6. Klicke auf "Grant admin consent"

### 3. Client Secret erstellen

1. Gehe zu "Certificates & secrets"
2. Klicke auf "New client secret"
3. Beschreibung: `Algotrader API Secret`
4. Expires: `24 months` (empfohlen)
5. Kopiere den **Value** (nicht die Secret ID!)

### 4. Tenant und Client ID abrufen

1. Gehe zu "Overview"
2. Kopiere:
   - **Application (client) ID** → `MICROSOFT_CLIENT_ID`
   - **Directory (tenant) ID** → `MICROSOFT_TENANT_ID`

## Beispiel .env Konfiguration

```env
# Existing variables...
FMP_API_KEY=your-fmp-key

# Microsoft ToDo Integration
MICROSOFT_TENANT_ID=12345678-1234-1234-1234-123456789012
MICROSOFT_CLIENT_ID=87654321-4321-4321-4321-210987654321
MICROSOFT_CLIENT_SECRET=your-very-long-client-secret-value-here
```

## Sicherheitshinweise

### 1. Client Secret Sicherheit
- ⚠️ **Niemals** Client Secrets in Code oder öffentlichen Repositories speichern
- 🔄 Regelmäßige Rotation der Client Secrets (alle 6-12 Monate)
- 🔒 Verwende sichere Umgebungsvariablen-Verwaltung

### 2. Berechtigungen
- 📋 Verwende nur die minimal erforderlichen Berechtigungen
- 🔍 Regelmäßige Überprüfung der App-Berechtigungen
- 👥 Admin Consent für Application Permissions erforderlich

### 3. Monitoring
- 📊 Überwache API-Aufrufe und Token-Verwendung
- 🚨 Implementiere Alerting für ungewöhnliche Aktivitäten
- 📝 Logge alle OAuth-Events für Audit-Zwecke

## Troubleshooting

### Häufige Fehler

#### 1. "invalid_client" Error
```
Ursache: Falsche Client ID oder Client Secret
Lösung: Überprüfe MICROSOFT_CLIENT_ID und MICROSOFT_CLIENT_SECRET
```

#### 2. "insufficient_privileges" Error
```
Ursache: Fehlende API-Berechtigungen oder Admin Consent
Lösung: Überprüfe Tasks.ReadWrite Berechtigung und Admin Consent
```

#### 3. "invalid_tenant" Error
```
Ursache: Falsche Tenant ID
Lösung: Überprüfe MICROSOFT_TENANT_ID in Azure Portal
```

### Validierung der Konfiguration

```bash
# Test der Umgebungsvariablen
echo "Tenant ID: $MICROSOFT_TENANT_ID"
echo "Client ID: $MICROSOFT_CLIENT_ID"
echo "Client Secret: ${MICROSOFT_CLIENT_SECRET:0:10}..." # Nur erste 10 Zeichen
```

## Deployment Considerations

### Production Environment
- Verwende Azure Key Vault für Secret-Management
- Implementiere Secret-Rotation-Automatisierung
- Konfiguriere Monitoring und Alerting

### Development Environment
- Verwende separate Azure App Registration für Development
- Teste mit eingeschränkten Berechtigungen
- Dokumentiere Test-Accounts und -Listen

## Weiterführende Links

- [Microsoft Graph API Documentation](https://docs.microsoft.com/en-us/graph/)
- [Azure App Registration Guide](https://docs.microsoft.com/en-us/azure/active-directory/develop/quickstart-register-app)
- [Microsoft Graph Permissions Reference](https://docs.microsoft.com/en-us/graph/permissions-reference)
