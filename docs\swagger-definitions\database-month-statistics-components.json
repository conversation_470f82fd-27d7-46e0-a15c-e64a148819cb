{"components": {"schemas": {"MonthStatistics": {"type": "object", "properties": {"year": {"type": "integer", "description": "Jahr der Statistik", "example": 2023}, "month": {"type": "integer", "description": "Monat der Statistik (1-12)", "minimum": 1, "maximum": 12, "example": 4}, "min_profit": {"type": "number", "format": "float", "description": "Minimaler Gewinn/Verlust in diesem Monat", "example": -350}, "max_profit": {"type": "number", "format": "float", "description": "Maximaler Gewinn/Verlust in diesem Monat", "example": 780}, "cnt_trades": {"type": "integer", "description": "<PERSON><PERSON><PERSON> der Trades in diesem Monat", "example": 120}, "sum_profit": {"type": "number", "format": "float", "description": "Gesamtgewinn/-verlust in diesem Monat", "example": 2350}}}, "CurrentMonthStatistics": {"type": "object", "properties": {"year": {"type": "integer", "description": "Jahr der Statistik", "example": 2023}, "month": {"type": "integer", "description": "Monat der Statistik (1-12)", "minimum": 1, "maximum": 12, "example": 4}, "min_profit": {"type": "number", "format": "float", "description": "Minimaler Gewinn/Verlust im aktuellen Monat", "example": -350}, "max_profit": {"type": "number", "format": "float", "description": "Maximaler Gewinn/Verlust im aktuellen Monat", "example": 780}, "cnt_trades": {"type": "integer", "description": "Anzahl der Trades im aktuellen Monat", "example": 120}, "sum_profit": {"type": "number", "format": "float", "description": "Gesamtgewinn/-verlust im aktuellen Monat", "example": 2350}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Parameter 'refID' is required"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getMonthStatistics"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Parameter 'refID' is required", "function": "getMonthStatistics"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch month statistics", "function": "getMonthStatistics"}}}}}}}