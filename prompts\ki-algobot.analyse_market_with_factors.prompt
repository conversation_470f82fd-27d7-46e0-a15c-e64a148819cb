You are tasked with creating an overall assessment of the current market condition based on the provided information. You will analyze the data and provide a comprehensive evaluation with a score ranging from -50 to +50, where -50 represents "ALL IN SHORT" and +50 represents "ALL IN LONG".

Here is the market data to analyze:
<market_data>
{{MARKET_DATA}}
</market_data>

Follow these steps to complete the task:

1. Carefully analyze the provided market data.

2. Identify key factors that influence the market condition. These could include economic indicators, market trends, geopolitical events, or any other relevant information present in the data.

3. For each identified factor, assign a sub-score between -50 and +50 based on its impact on the market condition. Use your judgment to weigh the importance of each factor.

4. Calculate the final overall score by considering all the sub-scores. This should be a weighted average that reflects the relative importance of each factor.

5. Write a brief summary (maximum 200 words) explaining your assessment of the market condition based on the analyzed data.

6. Format your response as a JSON structure with the following elements:
   - "summary": Your brief explanation of the market condition (string, max 200 words)
   - "score": The final calculated score (number between -50 and +50)
   - "reasoning": An array of objects, each representing a factor you considered, with:
     - A key representing the factor name (string)
     - An object containing:
       - "name": A short name for the factor (string)
       - "score": The sub-score assigned to this factor (number between -50 and +50)

7. Ensure that your entire output is a single, valid JSON structure.

Here's an example of the expected JSON structure (do not use this data, it's just for reference):

{
  "summary": "The market shows mixed signals with a slight bearish tendency. While economic indicators suggest stability, geopolitical tensions and inflationary pressures are causing concern. Tech sector performance is strong, but traditional industries are lagging.",
  "score": -15,
  "reasoning": [
    {
      "economic_indicators": {
        "name": "Economic Stability",
        "score": 10
      }
    },
    {
      "geopolitical_situation": {
        "name": "Global Tensions",
        "score": -30
      }
    },
    {
      "sector_performance": {
        "name": "Tech Sector Strength",
        "score": 20
      }
    },
    {
      "inflation": {
        "name": "Inflationary Pressures",
        "score": -25
      }
    }
  ]
}

Remember to provide only the JSON structure as your final output, without any additional text or explanations outside of the JSON. Use ALWAYS German for all content!