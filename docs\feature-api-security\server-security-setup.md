# Express.js API-Sicherheitskonfiguration für ML-Algotrader

## Übersicht

Dieses Dokument enthält eine konkrete Konfigurationsanleitung für die Absicherung der ML-Algotrader Express.js API-Middleware basierend auf der Netzwerkanalyse vom 17. Mai 2025. Die Konfiguration implementiert einen mehrschichtigen Sicherheitsansatz, der netzwerkbasierte Kontrollen mit zusätzlichen Sicherheitsmaßnahmen kombiniert.

## Netzwerkumgebungsanalyse

Basierend auf der durchgeführten Netzwerkanalyse wurden folgende relevante Informationen identifiziert:

### Lokale Netzwerkumgebung

- **Primäre IP-Adressen**: 
  - WLAN (en0): **************
  - Ethernet (en1): **************
- **Netzwerk-Subnetz**: *************/24
- **Gateway**: *************
- **DNS-Server**: *************

### ML-Algotrader Domains

- **api.ml-algotrader.com**: ************
- **ml-algotrader.com**: ************
- **app.ml-algotrader.com**: ************

### Offene Ports und Dienste

Relevante offene Ports auf dem lokalen System:
- Port 3000: Typischer Entwicklungsport für Node.js-Anwendungen
- Port 3306: MySQL-Datenbankdienst
- Port 80/443: HTTP/HTTPS-Webdienste
## Sicherheitskonfiguration für Express.js

Basierend auf der Netzwerkanalyse und den Sicherheitsanforderungen wird folgende Konfiguration empfohlen:

### 1. Grundlegende Express.js-Konfiguration mit Sicherheitsmaßnahmen

```javascript
// server-config.js
const express = require('express');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const cors = require('cors');
const morgan = require('morgan');
const fs = require('fs');
const path = require('path');
const ipfilter = require('express-ipfilter').IpFilter;

// Konfigurationsobjekt
const config = {
  // Netzwerk-Konfiguration
  port: process.env.PORT || 3000,
  
  // IP-Whitelist basierend auf der Netzwerkanalyse
  ipWhitelist: [
    // Lokales Netzwerk
    '*************/24',
    // Localhost
    '127.0.0.1',
    '::1',
    // ML-Algotrader Server
    '************'
  ],
  
  // Domain-Whitelist
  domainWhitelist: [
    'localhost',
    'ml-algotrader.com',
    'api.ml-algotrader.com',
    'app.ml-algotrader.com'
  ],
  
  // Rate Limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 Minuten
    max: 100, // 100 Anfragen pro IP innerhalb des Zeitfensters
    standardHeaders: true,
    legacyHeaders: false
  },
  
  // CORS-Optionen
  cors: {
    origin: [
      'https://ml-algotrader.com',
      'https://api.ml-algotrader.com',
      'https://app.ml-algotrader.com',
      'http://localhost:3000'
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
  },
  
  // API-Schlüssel (sollte in Produktionsumgebung über Umgebungsvariablen gesetzt werden)
  apiKey: process.env.API_KEY || 'dev-api-key-change-in-production'
};

// Express-App initialisieren
const initializeApp = () => {
  const app = express();
  
  // Logging-Konfiguration
  const accessLogStream = fs.createWriteStream(
    path.join(__dirname, 'access.log'),
    { flags: 'a' }
  );
  
  // Middleware für grundlegende Sicherheit
  app.use(helmet()); // Setzt verschiedene HTTP-Header für mehr Sicherheit
  
  // IP-Filterung
  app.use(ipfilter(config.ipWhitelist, { 
    mode: 'allow',
    logLevel: 'deny',
    log: (message) => {
      console.log(`[IP-FILTER] ${message}`);
      fs.appendFileSync(
        path.join(__dirname, 'security.log'),
        `${new Date().toISOString()} - ${message}\n`
      );
    }
  }));
  
  // Domain-Filterung
  app.use((req, res, next) => {
    const host = req.hostname;
    if (config.domainWhitelist.includes(host)) {
      next();
    } else {
      const message = `Zugriff verweigert: Unerlaubte Domain ${host}`;
      console.log(`[DOMAIN-FILTER] ${message}`);
      fs.appendFileSync(
        path.join(__dirname, 'security.log'),
        `${new Date().toISOString()} - ${message}\n`
      );
      res.status(403).send('Zugriff verweigert');
    }
  });
  
  // Rate Limiting
  const limiter = rateLimit(config.rateLimit);
  app.use('/api/', limiter);
  
  // CORS-Konfiguration
  app.use(cors(config.cors));
  
  // Request-Logging
  app.use(morgan('combined', { stream: accessLogStream }));
  
  // Body-Parser
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  
  // API-Schlüssel-Authentifizierung
  app.use('/api/', (req, res, next) => {
    const apiKey = req.header('X-API-Key');
    if (apiKey === config.apiKey) {
      next();
    } else {
      const message = `Ungültiger API-Schlüssel: ${apiKey}`;
      console.log(`[API-KEY] ${message}`);
      fs.appendFileSync(
        path.join(__dirname, 'security.log'),
        `${new Date().toISOString()} - ${message}\n`
      );
      res.status(401).send('Ungültiger API-Schlüssel');
    }
  });
  
  return app;
};

module.exports = {
  config,
  initializeApp
};
```
### 2. Implementierung der CIDR-Prüfung

```javascript
// ip-utils.js
const ipRangeCheck = require('ip-range-check');

/**
 * Prüft, ob eine IP-Adresse in einem CIDR-Bereich liegt
 * @param {string} ip - Die zu prüfende IP-Adresse
 * @param {string} cidr - Der CIDR-Bereich (z.B. '***********/24')
 * @returns {boolean} - True, wenn die IP im Bereich liegt
 */
function isIpInCidrRange(ip, cidr) {
  return ipRangeCheck(ip, cidr);
}

/**
 * Prüft, ob eine IP-Adresse in einer Liste von CIDR-Bereichen liegt
 * @param {string} ip - Die zu prüfende IP-Adresse
 * @param {Array<string>} cidrList - Liste von CIDR-Bereichen
 * @returns {boolean} - True, wenn die IP in mindestens einem Bereich liegt
 */
function isIpAllowed(ip, cidrList) {
  return cidrList.some(cidr => isIpInCidrRange(ip, cidr));
}

module.exports = {
  isIpInCidrRange,
  isIpAllowed
};
```

### 3. Hauptserver-Datei

```javascript
// server.js
const https = require('https');
const fs = require('fs');
const path = require('path');
const { initializeApp, config } = require('./server-config');

// Express-App initialisieren
const app = initializeApp();

// API-Routen definieren
app.get('/api/data', (req, res) => {
  res.json({ message: 'Daten erfolgreich abgerufen' });
});

// Gesundheitscheck-Endpunkt (ohne API-Schlüssel-Authentifizierung)
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Fehlerbehandlung für nicht gefundene Routen
app.use((req, res) => {
  res.status(404).json({ error: 'Nicht gefunden' });
});

// Globale Fehlerbehandlung
app.use((err, req, res, next) => {
  console.error(`[ERROR] ${err.stack}`);
  fs.appendFileSync(
    path.join(__dirname, 'error.log'),
    `${new Date().toISOString()} - ${err.stack}\n`
  );
  res.status(500).json({ error: 'Interner Serverfehler' });
});

// HTTPS-Server starten (für Produktion)
if (process.env.NODE_ENV === 'production') {
  const httpsOptions = {
    key: fs.readFileSync(path.join(__dirname, 'ssl', 'private.key')),
    cert: fs.readFileSync(path.join(__dirname, 'ssl', 'certificate.crt'))
  };
  
  https.createServer(httpsOptions, app).listen(config.port, () => {
    console.log(`HTTPS-Server läuft auf Port ${config.port}`);
  });
} else {
  // HTTP-Server starten (für Entwicklung)
  app.listen(config.port, () => {
    console.log(`HTTP-Server läuft auf Port ${config.port}`);
  });
}
## Installationsanleitung

1. Installieren Sie die erforderlichen Abhängigkeiten:

```bash
npm install express helmet express-rate-limit cors morgan express-ipfilter ip-range-check
```

2. Erstellen Sie die Verzeichnisstruktur:

```bash
mkdir -p ssl logs
```

3. Für die Produktionsumgebung, generieren Sie SSL-Zertifikate:

```bash
# Selbstsigniertes Zertifikat für Entwicklung (für Produktion ein echtes Zertifikat verwenden)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout ssl/private.key -out ssl/certificate.crt
```

4. Konfigurieren Sie Umgebungsvariablen (für Produktion):

```bash
# .env Datei
NODE_ENV=production
PORT=3000
API_KEY=ihr-sicherer-api-schlüssel
```

## Sicherheitsmaßnahmen im Detail

### 1. Netzwerkbasierte Sicherheit

Die Konfiguration implementiert mehrere netzwerkbasierte Sicherheitsmaßnahmen:

- **IP-Filterung**: Beschränkt den Zugriff auf definierte IP-Adressen und Subnetze
- **Domain-Filterung**: Erlaubt nur Anfragen von autorisierten Domains
- **CORS-Konfiguration**: Begrenzt Cross-Origin-Anfragen auf vertrauenswürdige Quellen

### 2. Authentifizierung und Autorisierung

- **API-Schlüssel**: Einfache Authentifizierung über HTTP-Header
- Für höhere Sicherheitsanforderungen kann die Implementierung um JWT-basierte Authentifizierung erweitert werden

### 3. Schutz vor gängigen Angriffen

- **Helmet**: Setzt wichtige Sicherheits-HTTP-Header
- **Rate Limiting**: Schützt vor Brute-Force- und DoS-Angriffen
- **HTTPS**: Verschlüsselt die Datenübertragung

### 4. Logging und Monitoring

- **Morgan**: Protokolliert alle HTTP-Anfragen
- **Benutzerdefinierte Logs**: Separate Logs für Sicherheitsereignisse und Fehler

## Anpassung an die ML-Algotrader-Umgebung

Basierend auf der Netzwerkanalyse wurden folgende spezifische Anpassungen vorgenommen:

1. **IP-Whitelist**: Enthält das lokale Subnetz (*************/24) und die ML-Algotrader-Server-IP (************)
2. **Domain-Whitelist**: Enthält alle relevanten ML-Algotrader-Domains
3. **CORS-Konfiguration**: Erlaubt nur Anfragen von den ML-Algotrader-Domains und localhost für Entwicklungszwecke

## Testverfahren

Nach der Implementierung sollten folgende Tests durchgeführt werden:

1. **Zugriff aus erlaubtem Netzwerk**: Testen Sie den API-Zugriff von einem Gerät im lokalen Netzwerk
2. **Zugriff mit gültigem API-Schlüssel**: Verifizieren Sie, dass Anfragen mit korrektem API-Schlüssel akzeptiert werden
3. **Zugriff von unerlaubter IP**: Stellen Sie sicher, dass Anfragen von nicht autorisierten IPs abgelehnt werden
4. **Zugriff ohne API-Schlüssel**: Überprüfen Sie, dass Anfragen ohne gültigen API-Schlüssel abgelehnt werden
5. **Rate-Limiting-Test**: Testen Sie, ob das Rate-Limiting bei zu vielen Anfragen greift

## Fazit

Diese Konfiguration bietet einen mehrschichtigen Sicherheitsansatz für die ML-Algotrader Express.js API-Middleware. Sie kombiniert netzwerkbasierte Kontrollen mit zusätzlichen Sicherheitsmaßnahmen, um einen robusten Schutz zu gewährleisten.

Die Konfiguration ist speziell auf die in der Netzwerkanalyse identifizierte Umgebung zugeschnitten, kann aber bei Bedarf an veränderte Netzwerkbedingungen angepasst werden.
```