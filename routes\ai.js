const express = require('express');
const router = express.Router();
const controller_gpt = require('../controllers/gpt/controller_gpt');

// GPT Routes
router.get('/news_summarize', controller_gpt.doUdateKISummaryAndStoreInDatabase);
router.post('/assistant', controller_gpt.aiAssistant);
router.post('/storeNews', controller_gpt.storeNews); // New route for storing news items
router.put('/refresh_factor_map', controller_gpt.refreshFactorMap);
router.post('/prompt', controller_gpt.aiLangfusePromptRequest);

module.exports = router;