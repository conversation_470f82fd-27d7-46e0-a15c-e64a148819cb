{"openapi": "3.0.0", "info": {"title": "ML Algotrader API", "version": "2.0.0", "description": "REST API for algorithmic trading with IG Markets and integrated AI/ML capabilities", "contact": {"name": "API Support", "url": "https://app.ml-algotrader.com"}, "license": {"name": "Private License"}}, "servers": [{"url": "https://api.ml-algotrader.com", "description": "Production server"}, {"url": "http://localhost:8080", "description": "Development server"}], "tags": [{"name": "IG Trading", "description": "Execute and manage trades on IG Markets platform"}, {"name": "IG Market Data", "description": "Market data and price information from IG Markets platform"}, {"name": "Statistics", "description": "Trading performance and analysis statistics"}, {"name": "AI Integration", "description": "AI-powered market analysis and predictions"}, {"name": "Mirror Trading", "description": "Copy trading configuration and monitoring"}, {"name": "Chart Data", "description": "Historical OHLCV data for technical analysis"}, {"name": "Trade History", "description": "Trading history and performance data"}, {"name": "Financial Data", "description": "Financial Modeling Prep (FMP) market data and financial information"}], "components": {"securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-KEY", "description": "API key authentication"}}, "schemas": {"MarketInfo": {"type": "object", "properties": {"instrument": {"type": "object", "properties": {"name": {"type": "string", "description": "Name des Instruments", "example": "EUR/USD"}, "epic": {"type": "string", "description": "Epic-Code des Instruments", "example": "CS.D.EURUSD.TODAY.IP"}, "type": {"type": "string", "description": "Typ des Instruments", "example": "CURRENCIES"}, "marginFactor": {"type": "number", "format": "float", "description": "Margin-Faktor in Prozent", "example": 3.33}, "onePipMeans": {"type": "string", "description": "Bedeutung eines Pips", "example": "0.0001 price movement"}}}, "snapshot": {"type": "object", "properties": {"marketStatus": {"type": "string", "description": "Status des Marktes", "example": "TRADEABLE"}, "bid": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON><PERSON> (Bid)", "example": 1.08543}, "offer": {"type": "number", "format": "float", "description": "<PERSON>rief<PERSON><PERSON> (Ask)", "example": 1.08553}, "high": {"type": "number", "format": "float", "description": "<PERSON>esh<PERSON>", "example": 1.08743}, "low": {"type": "number", "format": "float", "description": "Tagestief", "example": 1.08343}}}}}, "PriceResponse": {"type": "object", "properties": {"bid": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON><PERSON> (Bid)", "example": 1.08543}, "ask": {"type": "number", "format": "float", "description": "<PERSON>rief<PERSON><PERSON> (Ask)", "example": 1.08553}, "spread": {"type": "number", "format": "float", "description": "Spread (Ask - Bid)", "example": 0.0001}, "timestamp": {"type": "string", "format": "date-time", "description": "Zeitstempel der Preisabfrage"}}}, "CandleData": {"type": "object", "properties": {"snapshotTime": {"type": "string", "format": "date-time", "description": "Zeitstempel des Kerzencharts"}, "openPrice": {"type": "number", "format": "float", "description": "Eröffnungskurs", "example": 1.08543}, "closePrice": {"type": "number", "format": "float", "description": "Schlusskurs", "example": 1.08563}, "highPrice": {"type": "number", "format": "float", "description": "Höchstkurs", "example": 1.08583}, "lowPrice": {"type": "number", "format": "float", "description": "Tiefstkurs", "example": 1.08523}, "lastTradedVolume": {"type": "number", "format": "integer", "description": "Handelsvolumen", "example": 12345}}}, "HistoricalPriceResponse": {"type": "array", "items": {"$ref": "#/components/schemas/CandleData"}}, "LastCandleResponse": {"type": "object", "$ref": "#/components/schemas/CandleData"}, "MarketDataError": {"type": "object", "properties": {"error": {"type": "string", "description": "Fehlermeldung", "example": "Failed to retrieve market information"}}}}, "responses": {"MarketDataError": {"description": "Fehler bei der Abfrage von <PERSON>daten", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarketDataError"}}}}}}, "security": [{"ApiKeyAuth": []}], "paths": {"/api/v1/ai/predictions": {"get": {"summary": "Get AI price predictions", "description": "Retrieves AI-generated price predictions for specified trading symbols", "tags": ["AI Integration"], "parameters": [{"in": "query", "name": "symbol", "schema": {"type": "string"}, "description": "Trading symbol (e.g. EURUSD, USDJPY)", "required": true}, {"in": "query", "name": "timeframe", "schema": {"type": "integer", "enum": [15, 30, 60, 240, 1440], "default": 60}, "description": "Prediction timeframe in minutes"}], "responses": {"200": {"description": "AI predictions retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"symbol": {"type": "string", "example": "EURUSD"}, "timeframe": {"type": "integer", "example": 60}, "predictions": {"type": "array", "items": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "direction": {"type": "string", "enum": ["UP", "DOWN", "NEUTRAL"]}, "confidence": {"type": "number", "format": "float", "minimum": 0, "maximum": 1}, "target_price": {"type": "number", "format": "float"}}}}}}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/ai/prompt": {"post": {"summary": "Execute an AI prompt", "description": "Executes a predefined AI prompt with the specified LLM model and variables. This endpoint allows for flexible interaction with various LLM models using structured prompts.", "tags": ["AI Integration"], "parameters": [{"in": "query", "name": "promptID", "schema": {"type": "string"}, "description": "ID of the predefined prompt to execute (e.g., n8n.mail.summary, n8n.mail.relevancescore)", "required": true, "example": "n8n.mail.summary"}, {"in": "query", "name": "llmModel", "schema": {"type": "string", "enum": ["llama-3.3-70b-versatile", "claude-3-5-haiku-20241022", "gpt-4o-mini", "mistral-large-latest", "gemini-2.5-pro-exp-03-25", "gemini-2.0-flash"]}, "description": "LLM model to use for processing the prompt", "example": "gpt-4o-mini"}, {"in": "query", "name": "promptVersion", "schema": {"type": "integer", "default": 1}, "description": "Version of the prompt to use"}, {"in": "query", "name": "promptTitle", "schema": {"type": "string"}, "description": "Alternative to promptID, specifies the title of the prompt to execute", "example": "ki-algobot.llm-news-aggregation"}], "requestBody": {"description": "Variables to be used in the prompt template", "required": true, "content": {"text/plain": {"schema": {"type": "string", "description": "JSON string containing variables for the prompt", "example": "{\n  \"TEXT_TO_ANALYZE\": \"Nvidia's AI system ACE will power in-game character interactions in Mecha Break, a mech battle game launching in 2025.\"\n}"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PromptVariables"}}}}, "responses": {"200": {"description": "Prompt executed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"result": {"type": "string", "description": "The LLM-generated response to the prompt"}, "model": {"type": "string", "description": "The LLM model used for processing"}, "promptID": {"type": "string", "description": "The ID of the prompt that was executed"}, "promptVersion": {"type": "integer", "description": "The version of the prompt that was used"}}}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/fmp/calendar": {"get": {"summary": "Get economic calendar events", "description": "Retrieves economic calendar events for a specified date range.\n\nTechnical Details:\n- Date range defaults to current month if not specified\n- Maximum range of 30 days when only one date is provided\n- Returns events with their estimates and actual values\n- Data is sourced from Financial Modeling Prep API\n\nUse Cases:\n- Economic event monitoring\n- Market impact analysis\n- Trading decision support\n- Fundamental analysis", "tags": ["Financial Data"], "parameters": [{"in": "query", "name": "from", "schema": {"type": "string", "format": "date"}, "description": "Start date for calendar events (YYYY-MM-DD format)", "example": "2025-01-01"}, {"in": "query", "name": "to", "schema": {"type": "string", "format": "date"}, "description": "End date for calendar events (YYYY-MM-DD format)", "example": "2025-01-31"}], "responses": {"200": {"description": "Successfully retrieved calendar events", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "data": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "format": "date", "description": "Date of the economic event", "example": "2025-01-15"}, "event": {"type": "string", "description": "Name of the economic event", "example": "US CPI Data Release"}, "symbol": {"type": "string", "nullable": true, "description": "Related market symbol if applicable", "example": "USD"}, "time": {"type": "string", "nullable": true, "description": "Time of the event", "example": "08:30:00"}, "estimate": {"type": "string", "nullable": true, "description": "Estimated value for the event", "example": "0.3%"}, "actual": {"type": "string", "nullable": true, "description": "Actual reported value for the event", "example": "0.4%"}}}}, "count": {"type": "integer", "description": "Number of events returned", "example": 42}}}}}}, "500": {"description": "Server error occurred", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "error"}, "message": {"type": "string", "example": "Failed to fetch calendar data"}, "error": {"type": "string", "example": "External API connection timeout"}}}}}}}}}, "/api/v1/fmp/news": {"get": {"summary": "Get financial news articles", "description": "Retrieves financial news articles with pagination support.\n\nTechnical Details:\n- Paginated results with configurable page size\n- Default page size is 50 articles\n- Data is sourced from Financial Modeling Prep API\n- Returns full article content and metadata\n\nUse Cases:\n- Market news monitoring\n- Sentiment analysis\n- Event-driven trading\n- Research and analysis", "tags": ["Financial Data"], "parameters": [{"in": "query", "name": "size", "schema": {"type": "integer", "default": 50, "minimum": 1, "maximum": 1000}, "description": "Number of news articles to return per page", "example": 50}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 0, "minimum": 0}, "description": "Page number for pagination (0-based)", "example": 0}], "responses": {"200": {"description": "Successfully retrieved news articles"}, "500": {"description": "Server error occurred"}}}}, "/api/v1/fmp/chart": {"get": {"summary": "Get intraday price chart data", "description": "Retrieves intraday OHLCV (Open-High-Low-Close-Volume) chart data for a specific symbol and timeframe.", "tags": ["Financial Data"], "parameters": [{"in": "query", "name": "symbol", "schema": {"type": "string"}, "required": true, "description": "Trading symbol to retrieve chart data for", "example": "AAPL"}, {"in": "query", "name": "timeframe", "schema": {"type": "string", "enum": ["1min", "5min", "15min", "30min", "1hour", "4hour"]}, "required": true, "description": "Chart timeframe/interval", "example": "15min"}], "responses": {"200": {"description": "Successfully retrieved chart data"}, "400": {"description": "Invalid parameters provided"}, "500": {"description": "Server error occurred"}}}}, "/api/v1/db/account_settings": {"get": {"summary": "Kontoinformationen abrufen", "description": "Ruft detaillierte Einstellungen und Informationen für ein bestimmtes Konto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Validierung des refID-Parameters\n- Fehlerbehandlung für nicht gefundene Konten\n\nAnwendungsfälle:\n- An<PERSON><PERSON> von Kontoinformationen im Dashboard\n- Überprüfung des Kontostatus\n- Abfrage von Kontoeinstellungen für Trading-Entscheidungen", "tags": ["Account Management"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-<PERSON> des Kontos (z.B. 'IG-D1', 'IG-P1')", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Kontoeinstellungen", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountSettings"}}, "example": [{"account_id": 1, "refId": "IG-D1", "account_name": "Demo Account 1", "account_type": "DEMO", "account_currency": "EUR", "account_balance": 10000, "account_leverage": 30, "account_status": "ACTIVE"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/trading_mode": {"get": {"summary": "Trading-Modus abrufen", "description": "Ruft den aktuellen Trading-Modus für ein bestimmtes Konto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Validierung des refID-Parameters\n- Fehlerbehandlung für nicht gefundene Konten\n\nAnwendungsfälle:\n- Überprüfung, ob automatisches Trading aktiviert ist\n- Anzeige des Trading-Status im Dashboard\n- Entscheidungsgrundlage für Trading-Strategien", "tags": ["Account Management"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-<PERSON> des Kontos (z.B. 'IG-D1', 'IG-P1')", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage des Trading-Modus", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradingMode"}}, "example": [{"account_id": 1, "refId": "IG-D1", "trading_mode": "AUTOMATIC", "trading_mode_since": "2023-04-15T10:30:00Z", "trading_mode_reason": "User setting"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/ai_predictions": {"get": {"summary": "KI-Vorhersagen für Handelssymbole abrufen", "description": "Ruft KI-generierte Vorhersagen für ein bestimmtes Symbol oder alle verfügbaren Symbole ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Optionale Filterung nach Symbol\n- Enthält Konfidenzwerte und Richtungsvorhersagen\n\nAnwendungsfälle:\n- <PERSON><PERSON><PERSON> von KI-Vorhersagen im Trading-Dashboard\n- Integration in automatisierte Trading-Strategien\n- Entscheidungsunterstützung für manuelle Trades\n- Vergleich mit tatsächlichen Marktbewegungen", "tags": ["AI Integration"], "parameters": [{"in": "query", "name": "symbol", "required": false, "schema": {"type": "string"}, "description": "Trading-Symbol, für das Vorhersagen abgerufen werden sollen. Wenn nicht angegeben, werden Vorhersagen für alle verfügbaren Symbole zurückgegeben.", "example": "EURUSD"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der KI-Vorhersagen", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIPrediction"}}, "example": [{"symbol": "EURUSD", "prediction_date": "2023-04-15T10:30:00Z", "prediction_value": 1.0865, "prediction_direction": "<PERSON>", "confidence": 0.85, "model_name": "LSTM-v2.3", "timeframe": "1D"}, {"symbol": "EURUSD", "prediction_date": "2023-04-15T10:30:00Z", "prediction_value": 1.0842, "prediction_direction": "Short", "confidence": 0.67, "model_name": "Transformer-v1.2", "timeframe": "4H"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/calendar/list": {"get": {"summary": "Wirtschaftskalender abrufen", "description": "Ruft eine Liste von Wirtschaftskalendereinträgen für einen bestimmten Zeitraum ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Filtert nach Mindest-Impact-Level (konfigurierbar)\n- Filtert nach erlaubten Ländern (konfigurierbar)\n- Zeitstempel im deutschen Format\n\nAnwendungsfälle:\n- Anzeige des Wirtschaftskalenders im Trading-Dashboard\n- Planung von Trading-Aktivitäten um wichtige Wirtschaftsereignisse\n- Identifizierung von Hochvolatilitätsperioden\n- Risikomanagement durch Deaktivierung von Trades während wichtiger Ereignisse", "tags": ["Calendar"], "parameters": [{"in": "query", "name": "minus_days", "schema": {"type": "integer", "default": 7, "minimum": 0, "maximum": 365}, "description": "<PERSON>zahl der Tage in der Vergangenheit, für die Kalendereinträge abgerufen werden sollen", "example": 7}, {"in": "query", "name": "plus_days", "schema": {"type": "integer", "default": 14, "minimum": 0, "maximum": 365}, "description": "<PERSON>zahl der Tage in der Zukunft, für die Kalendereinträge abgerufen werden sollen", "example": 14}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Kalenderliste", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CalendarEntry"}}, "example": [{"id": 1436, "event_date": "2023-04-15T14:30:00Z", "country": "US", "impact": 3, "event_name": "Non-Farm Payrolls", "actual": "236K", "forecast": "240K", "previous": "311K", "currency": "USD", "is_today": false, "is_tomorrow": true, "toggle_deactivate_high_volume_trades": true}, {"id": 1437, "event_date": "2023-04-16T12:00:00Z", "country": "EU", "impact": 2, "event_name": "ECB Interest Rate Decision", "actual": null, "forecast": "3.5%", "previous": "3.5%", "currency": "EUR", "is_today": false, "is_tomorrow": false, "toggle_deactivate_high_volume_trades": false}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/calendar/delta_report": {"get": {"summary": "Delta-Report für Wirtschaftsereignisse abrufen", "description": "Ruft einen detaillierten Delta-Report ab, der die Preisänderungen eines bestimmten Symbols vor und nach einem Wirtschaftsereignis zeigt.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Berechnet Preisänderungen für verschiedene Zeitintervalle (1, 5, 15, 30, 60 Minuten)\n- Enthält Volatilitätsdaten vor und nach dem Ereignis\n\nAnwendungsfälle:\n- Analyse der Marktreaktion auf bestimmte Wirtschaftsereignisse\n- Entwicklung von Trading-Strategien basierend auf historischen Ereignisreaktionen\n- Risikobewertung für bestimmte Ereignistypen\n- Backtesting von ereignisbasierten Trading-Strategien", "tags": ["Calendar"], "parameters": [{"in": "query", "name": "calendar_id", "required": true, "schema": {"type": "string"}, "description": "ID des Kalendereintrags", "example": "1436"}, {"in": "query", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das der Delta-Report abgerufen werden soll", "example": "EURUSD"}], "responses": {"200": {"description": "Erfolgreiche Abfrage des Delta-Reports", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeltaReport"}}, "example": [{"calendar_id": 1436, "symbol": "EURUSD", "event_date": "2023-04-15T14:30:00Z", "event_name": "Non-Farm Payrolls", "delta_1min": 0.0012, "delta_5min": 0.0025, "delta_15min": 0.0018, "delta_30min": -0.0005, "delta_60min": -0.0022, "price_before": 1.0865, "price_after_1min": 1.0877, "price_after_60min": 1.0843, "volatility_before": 0.0003, "volatility_after": 0.0012}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/chartdata": {"get": {"summary": "Chart-<PERSON><PERSON> a<PERSON>", "description": "Ruft historische OHLCV-Daten (Open-High-Low-Close-Volume) für ein bestimmtes Symbol, eine Zeitperiode und einen Zeitraum ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Daten werden für die angegebene Zeitperiode aggregiert\n- Unterstützt verschiedene Zeitperioden (1, 5, 15, 30, 60, 240, 1440 Minuten)\n- Optimierte Datenbankabfragen für schnelle Antwortzeiten\n\nAnwendungsfälle:\n- Anzeige von Preischarts in Trading-Anwendungen\n- Technische Analyse und Mustererkennung\n- Backtesting von Trading-Strategien\n- Volatilitätsanalyse über verschiedene Zeiträume", "tags": ["Chart Data"], "parameters": [{"in": "query", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das Chart-Daten abgerufen werden sollen", "example": "EURUSD"}, {"in": "query", "name": "period", "required": true, "schema": {"type": "integer", "minimum": 1, "maximum": 1440}, "description": "Zeitperiode in Minuten (z.B. 1, 5, 15, 30, 60, 240, 1440)", "example": 15}, {"in": "query", "name": "days", "required": true, "schema": {"type": "integer", "minimum": 1, "maximum": 365}, "description": "<PERSON><PERSON><PERSON> der Tage in der Vergangenheit, für die Daten abgerufen werden sollen", "example": 30}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Chart-Daten", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChartDataPoint"}}, "example": [{"timestamp": "2023-04-15T14:30:00Z", "open": 1.0865, "high": 1.0878, "low": 1.086, "close": 1.0872, "volume": 1250.5}, {"timestamp": "2023-04-15T14:45:00Z", "open": 1.0872, "high": 1.088, "low": 1.0868, "close": 1.0875, "volume": 980.2}, {"timestamp": "2023-04-15T15:00:00Z", "open": 1.0875, "high": 1.0882, "low": 1.087, "close": 1.0878, "volume": 1120.8}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/statistics/day": {"get": {"summary": "Tagesstatistiken abrufen", "description": "Ruft Handelsstatistiken auf Tagesbasis für ein bestimmtes Konto ab, beginnend mit einem angegebenen Startdatum.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Gruppiert nach Handelstagen\n- Enthält Min/Max-<PERSON><PERSON>inn, Anzahl der Trades und Gesamtgewinn pro Tag\n- Standardmäßig werden die letzten 30 Tage abgerufen\n\nAnwendungsfälle:\n- Anzeige von täglichen Performance-Metriken im Dashboard\n- <PERSON><PERSON><PERSON> von Handelsmustern über verschiedene Tage\n- Identifizierung von besonders profitablen oder verlustbringenden Tagen\n- Berechnung von täglichen Durchschnittswerten für Performance-Berichte", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}, {"in": "query", "name": "startdate", "required": false, "schema": {"type": "string", "format": "date"}, "description": "Startdatum für die Statistiken im Format YYYY-MM-DD. Wenn nicht angegeben, werden die letzten 30 Tage verwendet.", "example": "2023-03-15"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Tagesstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DayStatistics"}}, "example": [{"date": "2023-04-15", "min_profit": -120, "max_profit": 350, "cnt_trades": 12, "sum_profit": 230}, {"date": "2023-04-16", "min_profit": -80, "max_profit": 420, "cnt_trades": 15, "sum_profit": 340}, {"date": "2023-04-17", "min_profit": -150, "max_profit": 280, "cnt_trades": 10, "sum_profit": 130}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/statistics/current_day": {"get": {"summary": "Statistiken des aktuellen Tages abrufen", "description": "Ruft Handelsstatistiken für den aktuellen Handelstag eines bestimmten Kontos ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 1 Minute)\n- Enthält nur Daten vom letzten Handelstag mit Aktivität\n- Enthält Min/Max-G<PERSON>inn, Anzahl der Trades und Gesamtgewinn\n- Enthält zusätzlich Jahr, Monat und Datum für einfache Filterung\n\nAnwendungsfälle:\n- Echtzeit-Überwachung der aktuellen Tagesperformance\n- Dashboard-Anzeige für den aktuellen Handelstag\n- Schnelle Überprüfung des Tagesergebnisses\n- Vergleich mit historischen Tageswerten", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der aktuellen Tagesstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrentDayStatistics"}}, "example": [{"YEAR": 2023, "MONTH": 4, "DATE": "2023-04-17", "min_profit": -150, "max_profit": 280, "cnt_trades": 10, "sum_profit": 130}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/mirrorTradingSettings": {"get": {"summary": "Mirror-Trading-Einstellungen abrufen", "description": "Ruft die Konfigurationseinstellungen für das Mirror Trading zwischen einem Quell- und einem Zielkonto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält alle Konfigurationsparameter für das Mirror Trading\n- Validiert die Existenz beider Konten\n\nAnwendungsfälle:\n- Anzeige der aktuellen Mirror-Trading-Konfiguration im Dashboard\n- Überprüfung des Mirror-Trading-Status\n- Anzeige der Volumen-Faktoren und Limits\n- Überprüfung der gespiegelten und ausgeschlossenen Symbole", "tags": ["Mirror Trading"], "parameters": [{"in": "query", "name": "targetRefID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Zielkontos", "example": "IG-P1"}, {"in": "query", "name": "sourceRefID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Quellkontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Mirror-Trading-Einstellungen", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MirrorTradingSettings"}}, "example": [{"source_account_id": 1, "source_refId": "IG-D1", "target_account_id": 2, "target_refId": "IG-P1", "mirror_active": true, "volume_factor": 0.5, "max_volume": 5, "min_volume": 0.1, "mirror_since": "2023-04-01T10:00:00Z", "mirror_symbols": "EURUSD,GBPUSD,USDJPY", "exclude_symbols": "USDCAD,AUDUSD"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/mirrorTradingLogs": {"get": {"summary": "Trading-Logs abrufen", "description": "Ruft detaillierte Trading-Logs für ein bestimmtes Konto oder eine Kombination von Konten ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Informationen zu Trading-Aktivitäten\n- Filterbar nach Zeitraum in Tagen\n- Limitierbar auf eine bestimmte Anzahl von Einträgen\n- Option zur Reduzierung der Nachrichtenlänge für kompaktere Anzeige\n\nAnwendungsfälle:\n- Überwachung von Trading-Aktivitäten\n- Fehleranalyse bei fehlgeschlagenen Trades\n- Audit-Trail für Trading-Operationen\n- Debugging von Trading-Systemen", "tags": ["Trade Logs"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Primäre Referenz-ID des Kontos", "example": "IG-D1"}, {"in": "query", "name": "refID_alternate", "required": false, "schema": {"type": "string", "default": "-"}, "description": "Alternative Referenz-ID für die Filterung (optional)", "example": "IG-P1"}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 400, "minimum": 1, "maximum": 1000}, "description": "Maximale Anzahl der zurückzugebenden Log-Einträge", "example": 400}, {"in": "query", "name": "days", "required": false, "schema": {"type": "integer", "default": 365, "minimum": 1, "maximum": 365}, "description": "<PERSON><PERSON><PERSON> der Tage in der Vergangenheit, für die Logs abgerufen werden sollen", "example": 365}, {"in": "query", "name": "reducemessage", "required": false, "schema": {"type": "boolean", "default": false}, "description": "<PERSON>n true, werden die Nachrichtentexte gekürzt, um die Antwortgröße zu reduzieren", "example": false}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Trading-Logs", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeLog"}}, "example": [{"log_id": 12345, "timestamp": "2023-04-15T14:30:00Z", "refId": "IG-D1", "refId_alternate": "IG-P1", "action": "OPEN_TRADE", "symbol": "EURUSD", "message": "Opening trade for EURUSD at 1.0865, volume 1.5, SL at 1.0840, TP at 1.0900", "level": "INFO", "source": "TradeExecutor", "trade_id": "DIAAABC123", "status": "SUCCESS", "error_message": null}, {"log_id": 12344, "timestamp": "2023-04-15T13:45:00Z", "refId": "IG-D1", "refId_alternate": "IG-P1", "action": "CLOSE_TRADE", "symbol": "GBPUSD", "message": "Closing trade DIAAABC122 for GBPUSD at 1.2430, profit: 20.0", "level": "INFO", "source": "TradeExecutor", "trade_id": "DIAAABC122", "status": "SUCCESS", "error_message": null}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/statistics/month": {"get": {"summary": "Monatsstatistiken abrufen", "description": "Ruft Handelsstatistiken auf Monatsbasis für ein bestimmtes Konto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Gruppiert nach Jahr und Monat\n- Enthält Min/Max-Gewinn, Anzahl der Trades und Gesamtgewinn pro Monat\n- Standardmäßig werden die letzten 3 Monate abgerufen\n\nAnwendungsfälle:\n- Anzeige von monatlichen Performance-Metriken im Dashboard\n- Analyse von Handelsmustern über verschiedene Monate\n- Identifizierung von saisonalen Trends\n- Berechnung von monatlichen Durchschnittswerten für Performance-Berichte", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Monatsstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MonthStatistics"}}, "example": [{"year": 2023, "month": 4, "min_profit": -350, "max_profit": 780, "cnt_trades": 120, "sum_profit": 2350}, {"year": 2023, "month": 3, "min_profit": -280, "max_profit": 650, "cnt_trades": 105, "sum_profit": 1980}, {"year": 2023, "month": 2, "min_profit": -320, "max_profit": 720, "cnt_trades": 98, "sum_profit": 2120}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/statistics/current_month": {"get": {"summary": "Statistiken des aktuellen Monats abrufen", "description": "Ruft Handelsstatistiken für den aktuellen Monat eines bestimmten Kontos ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält nur Daten vom aktuellen Monat\n- Enthält Min/Max-Gewinn, Anzahl der Trades und Gesamtgewinn\n- Filtert automatisch nach dem aktuellen Jahr und Monat\n\nAnwendungsfälle:\n- Echtzeit-Überwachung der aktuellen Monatsperformance\n- Dashboard-Anzeige für den laufenden Monat\n- Vergleich mit Zielvorgaben für den aktuellen Monat\n- Schnelle Überprüfung des Monatsergebnisses", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der aktuellen Monatsstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrentMonthStatistics"}}, "example": [{"year": 2023, "month": 4, "min_profit": -350, "max_profit": 780, "cnt_trades": 120, "sum_profit": 2350}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/news": {"get": {"summary": "Nachrichten abrufen", "description": "Ruft Finanznachrichten und Marktanalysen aus der Datenbank ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Sortiert nach Veröffentlichungsdatum (neueste zu<PERSON>t)\n- Limitierbar auf eine bestimmte Anzahl von Einträgen\n- Filterbar nach Zeitraum in Tagen\n- Optional filterbar nach Lesestatus (nur ungelesene Nachrichten)\n- Optional filterbar nach Lesezeichenstatus (nur mit Lesezeichen versehene Nachrichten)\n- Optional durchsuchbar nach Stichworten (UND-Verknüpfung)\n- Alle Filter können kombiniert werden für präzise Ergebnisse\n\nSuch-Funktionalität:\n- Keyword-Suche in Titel, Text und Zusammenfassung der Nachrichten\n- UND-Verknüpfung: Alle angegebenen Keywords müssen gefunden werden\n- Case-insensitive Suche\n- Keywords werden durch Kommas getrennt\n\nAnwendungsfälle:\n- An<PERSON><PERSON> von aktuellen Finanznachrichten im Trading-Dashboard\n- <PERSON><PERSON><PERSON> von Marktstimmungen und -trends\n- Identifizierung von potenziellen Handelsmöglichkeiten basierend auf Nachrichtenereignissen\n- Überwachung von relevanten Wirtschaftsnachrichten für bestimmte Handelsinstrumente\n- Filtern von ungelesenen Nachrichten für bessere Übersichtlichkeit\n- Zugriff auf gespeicherte/wichtige Nachrichten über Lesezeichen-Filter\n- Gezielte Suche nach spezifischen Themen oder Währungen (z.B. 'USD,EUR' für Dollar-Euro-Nachrichten)", "tags": ["News"], "parameters": [{"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 50, "minimum": 1, "maximum": 1000}, "description": "Maximale Anzahl der zurückzugebenden Nachrichteneinträge", "example": 50}, {"in": "query", "name": "days", "required": false, "schema": {"type": "integer", "default": 3, "minimum": 0, "maximum": 365}, "description": "<PERSON><PERSON><PERSON> der Tage in der Vergangenheit, für die Nachrichten abgerufen werden sollen", "example": 3}, {"in": "query", "name": "onlyUnread", "required": false, "schema": {"type": "boolean"}, "description": "Filter für nur ungelesene Nachrichten. Wenn auf true gesetzt, werden nur Nachrichten mit is_read=0 zurückgegeben. Akzeptiert boolean-Werte oder String-Repräsentationen (true/false, 1/0)", "example": true}, {"in": "query", "name": "onlyBookmarked", "required": false, "schema": {"type": "boolean"}, "description": "Filter für nur mit Lesezeichen versehene Nachrichten. Wenn auf true gesetzt, werden nur Nachrichten mit is_bookmarked=1 zurückgegeben. Akzeptiert boolean-Werte oder String-Repräsentationen (true/false, 1/0)", "example": true}, {"in": "query", "name": "searchKeywords", "required": false, "schema": {"type": "string", "maxLength": 500}, "description": "Kommagetrennte Liste von Stichworten zur Suche in Nachrichten. Suchlogik: UND-Verknüpfung (alle Keywords müssen gefunden werden). Durchsucht Titel, Text und Zusammenfassung der Nachrichten (case-insensitive). Beispiel: 'USD,EUR' findet nur Nachrichten, die sowohl USD als auch EUR enthalten.", "example": "USD,Bitcoin,Federal Reserve"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Nachrichten", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NewsItem"}}, "example": [{"news_id": 12345, "title": "US Federal Reserve Raises Interest Rates by 25 Basis Points", "content": "The Federal Reserve raised its benchmark interest rate by 25 basis points on Wednesday, bringing it to a range of 5.00% to 5.25%, the highest level since 2007.", "publish_date": "2023-04-15T14:30:00Z", "source": "Bloomberg", "url": "https://www.bloomberg.com/news/articles/2023-04-15/fed-raises-rates", "category": "Economy", "sentiment": -0.25, "relevance": 0.85, "symbols": "EURUSD,GBPUSD,USDJPY"}, {"news_id": 12344, "title": "ECB Signals Potential Rate Hike in June", "content": "European Central Bank officials signaled they're on track to raise interest rates again in June as inflation remains persistent.", "publish_date": "2023-04-14T10:15:00Z", "source": "Reuters", "url": "https://www.reuters.com/markets/europe/ecb-signals-rate-hike-june", "category": "Economy", "sentiment": -0.15, "relevance": 0.78, "symbols": "EURUSD,EURGBP"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/factor_map/latest": {"get": {"summary": "Neueste Faktorkarte abrufen", "description": "Ruft die neueste Faktorkarte ab, die verschiedene Marktfaktoren und ihre aktuellen Werte enthält.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält Faktoren wie Zinssätze, Inflation, BIP-Wachstum, etc.\n- Enthält eine Zusammenfassung der aktuellen Marktlage\n- Limitierbar auf eine bestimmte Anzahl von Einträgen (standardmäßig nur der neueste Eintrag)\n\nAnwendungsfälle:\n- Anzeige der aktuellen Marktfaktoren im Trading-Dashboard\n- Entscheidungsunterstützung für Trading-Strategien\n- Risikobewertung basierend auf aktuellen Marktbedingungen\n- Ana<PERSON><PERSON> von Markttrends und -stimmungen", "tags": ["News"], "parameters": [{"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1, "maximum": 100}, "description": "Maximale Anzahl der zurückzugebenden Faktorkarten", "example": 1}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Faktorkarte", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FactorMap"}}, "example": [{"factor_map_id": 789, "timestamp": "2023-04-15T14:30:00Z", "factors": {"interest_rates": 0.85, "inflation": 0.72, "gdp_growth": -0.25, "unemployment": 0.15, "consumer_confidence": -0.35}, "market_sentiment": "Bearish", "risk_level": "High", "summary": "Markets are cautious due to rising interest rates and inflation concerns, with a bearish outlook for the near term."}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/news/read-status": {"post": {"summary": "Status einer Nachricht aktualisieren", "description": "Aktualisiert den Lese- und/oder Lesezeichenstatus einer bestimmten Nachricht in der Datenbank.\n\nTechnische Details:\n- Direktes Update in der Datenbank ohne Caching\n- Erfordert die Nachrichten-ID im Request-Body\n- Unterstützt die Aktualisierung von is_read und/oder is_bookmarked\n\nAnwendungsfälle:\n- Mark<PERSON><PERSON> von Nachrichten als gelesen/ungelesen im Benutzerinterface\n- Setzen/Entfernen von Lesezeichen für Nachrichten\n- Tracking des Lesestatus für Benutzeranalysen\n- Filterung von ungelesenen oder mit Lesezeichen versehenen Nachrichten", "tags": ["News"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID der Nachricht, deren Status aktualisiert werden soll"}, "is_read": {"type": "boolean", "description": "<PERSON><PERSON>er Lesestatus der Nachricht (true = gelesen, false = ungelesen)"}, "is_bookmarked": {"type": "boolean", "description": "<PERSON><PERSON>er Lesezeichenstatus der Nachricht (true = mit Lesezeichen versehen, false = ohne Lesezeichen)"}}, "required": ["id"], "anyOf": [{"required": ["is_read"]}, {"required": ["is_bookmarked"]}]}, "examples": {"read-only": {"value": {"id": 12345, "is_read": true}}, "bookmark-only": {"value": {"id": 12345, "is_bookmarked": true}}, "both": {"value": {"id": 12345, "is_read": true, "is_bookmarked": true}}}}}}, "responses": {"200": {"description": "Status erfolgreich aktualisiert", "content": {"application/json": {"schema": {"type": "object", "properties": {"state": {"type": "string", "enum": ["successful"]}, "message": {"type": "string"}}}, "example": {"state": "successful", "message": "Updated status for news item 12345"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "404": {"description": "<PERSON><PERSON><PERSON>t nicht gefunden", "content": {"application/json": {"schema": {"type": "object", "properties": {"state": {"type": "string", "enum": ["error"]}, "message": {"type": "string"}}}, "example": {"state": "error", "message": "News item with ID 12345 not found"}}}}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/pivotpoints": {"get": {"summary": "Pivot-Punkte abrufen", "description": "Ruft Pivot-Punkte für verschiedene Trading-Symbole ab. Pivot-Punkte sind wichtige Preisniveaus, die als potenzielle Unterstützungs- und Widerstandsbereiche dienen.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält Standard-Pivot-Punkte (Pivot, R1-R3, S1-S3)\n- Berechnet aus historischen Hoch-, Tief- und Schlusskursen\n- Filterbar nach Zeitraum in Tagen\n\nAnwendungsfälle:\n- Identifizierung von wichtigen Unterstützungs- und Widerstandsniveaus\n- Integration in Trading-Strategien für Ein- und Ausstiegspunkte\n- Technische Analyse und Preisvorhersage\n- Risikomanagement durch Setzen von Stop-Loss und Take-Profit-Levels an Pivot-Punkten", "tags": ["Technical Analysis"], "parameters": [{"in": "query", "name": "days", "required": false, "schema": {"type": "integer", "default": 3000, "minimum": 1, "maximum": 3650}, "description": "<PERSON>zahl der Tage in der Vergangenheit, für die Pivot-Punkte abgerufen werden sollen", "example": 3000}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Pivot-Punkte", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PivotPoint"}}, "example": [{"pivot_id": 12345, "symbol": "EURUSD", "date": "2023-04-15", "timeframe": "D1", "pivot": 1.0865, "r1": 1.0892, "r2": 1.0918, "r3": 1.0945, "s1": 1.0838, "s2": 1.0812, "s3": 1.0785, "high": 1.0925, "low": 1.0805, "close": 1.0865, "calculation_method": "Standard"}, {"pivot_id": 12346, "symbol": "GBPUSD", "date": "2023-04-15", "timeframe": "D1", "pivot": 1.245, "r1": 1.2475, "r2": 1.25, "r3": 1.2525, "s1": 1.2425, "s2": 1.24, "s3": 1.2375, "high": 1.251, "low": 1.239, "close": 1.245, "calculation_method": "Standard"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/risk_management/state": {"get": {"summary": "Aktuellen Risikomanagement-Status abrufen", "description": "Ruft den aktuellen Risikomanagement-Status für ein bestimmtes Symbol oder alle Symbole ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 1 Sekunde)\n- Enthält Risikomodus, Marktsituation und Volatilitätsdaten\n- Optionale Filterung nach Symbol\n\nAnwendungsfälle:\n- Überwachung des aktuellen Risikomanagement-Status im Trading-Dashboard\n- Entscheidungsunterstützung für Trading-Strategien\n- Automatisierte Anpassung von Trading-Parametern basierend auf dem Risikomodus\n- Überwachung der Marktvolatilität", "tags": ["Risk Management"], "parameters": [{"in": "query", "name": "symbol", "required": false, "schema": {"type": "string"}, "description": "Trading-Symbol, für das der Risikomanagement-Status abgerufen werden soll. Wenn nicht angegeben, werden Daten für alle Symbole zurückgegeben.", "example": "EURUSD"}], "responses": {"200": {"description": "Erfolgreiche Abfrage des Risikomanagement-Status", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RiskManagementState"}}, "example": [{"timestamp": "2023-04-15T14:30:00Z", "symbol": "EURUSD", "risk_on_mode": "Standard", "market_situation": "Neutral-Wait", "reasoning": "Market volatility has decreased, switching to neutral stance", "vola_15": 0.0012, "vola_30": 0.0018, "vola_60": 0.0025}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/risk_management/states/{symbol}": {"get": {"summary": "Risikomanagement-Statushistorie abrufen", "description": "Ruft die Historie der Risikomanagement-Statusänderungen für ein bestimmtes Symbol ab.\n\nTechnische Details:\n- Enthält nur Einträge, bei denen sich der Risikomodus oder die Marktsituation geändert hat\n- Sortiert nach Zeitstempel (älteste zu<PERSON>t)\n- Begrenzt auf die letzten 60 Tage\n\nAnwendungsfälle:\n- Analyse der Risikomanagement-Strategie über Zeit\n- Überprüfung der Häufigkeit von Statusänderungen\n- Korrelation von Statusänderungen mit Marktbewegungen\n- Historische Analyse für die Optimierung der Risikomanagement-Strategie", "tags": ["Risk Management"], "parameters": [{"in": "path", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das die Risikomanagement-Statushistorie abgerufen werden soll", "example": "EURUSD"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Risikomanagement-Statushistorie", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RiskManagementStateHistory"}}, "example": [{"timestamp": "2023-04-10T09:15:00Z", "symbol": "EURUSD", "risk_on_mode": "Low-Risk", "market_situation": "Neutral-Wait", "reasoning": "High market uncertainty due to economic data release"}, {"timestamp": "2023-04-12T14:30:00Z", "symbol": "EURUSD", "risk_on_mode": "Standard", "market_situation": "Neutral-Wait", "reasoning": "Market volatility has normalized"}, {"timestamp": "2023-04-15T10:45:00Z", "symbol": "EURUSD", "risk_on_mode": "Standard", "market_situation": "<PERSON>", "reasoning": "Strong upward trend confirmed by multiple indicators"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/risk_management/mode/{symbol}": {"put": {"summary": "Risikomodus aktualisieren", "description": "Aktualisiert den Risikomodus für ein bestimmtes Symbol.\n\nTechnische Details:\n- Beh<PERSON>lt die aktuelle Marktsituation bei\n- Speichert den Zeitpunkt der Änderung\n- Unterstützt drei Risikomodi: Low-Risk, Standard, High-Risk\n\nAnwendungsfälle:\n- Manuelle Anpassung des Risikomodus basierend auf Marktbedingungen\n- Implementierung von Risikomanagement-Strategien\n- Reaktion auf unerwartete Marktereignisse\n- Anpassung der Trading-Parameter für verschiedene Risikoniveaus", "tags": ["Risk Management"], "parameters": [{"in": "path", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das der Risikomodus aktualisiert werden soll", "example": "EURUSD"}, {"in": "query", "name": "mode", "required": true, "schema": {"type": "string", "enum": ["Low-Risk", "Standard", "High-Risk"]}, "description": "<PERSON><PERSON>er Risiko<PERSON>dus", "example": "Standard"}], "responses": {"200": {"description": "Erfolgreiche Aktualisierung des Risikomodus", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RiskModeUpdateResponse"}, "example": {"status": "success", "message": "Risk mode updated to Standard for EURUSD"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/risk_management/situation/{symbol}": {"put": {"summary": "Marktsituation aktualisieren", "description": "Aktualisiert die Marktsituation für ein bestimmtes Symbol.\n\nTechnische Details:\n- Behält den aktuellen Risikomodus bei\n- Speichert den Zeitpunkt der Änderung\n- Unterstützt drei Marktsituationen: Short, Neutral-Wait, Long\n\nAnwendungsfälle:\n- Manuelle Anpassung der Marktsituation basierend auf technischer Analyse\n- Implementierung von direktionalen Trading-Strategien\n- Reaktion auf Trendänderungen\n- Anpassung der Trading-Richtung basierend auf der Marktanalyse", "tags": ["Risk Management"], "parameters": [{"in": "path", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das die Marktsituation aktualisiert werden soll", "example": "EURUSD"}, {"in": "query", "name": "situation", "required": true, "schema": {"type": "string", "enum": ["Short", "Neutral-Wait", "<PERSON>"]}, "description": "Neue Marktsituation", "example": "Neutral-Wait"}], "responses": {"200": {"description": "Erfolgreiche Aktualisierung der Marktsituation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarketSituationUpdateResponse"}, "example": {"status": "success", "message": "Market situation updated to Neutral-Wait for EURUSD"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/symbols/pip_values": {"get": {"summary": "Symbole mit Pip-Werten abrufen", "description": "Ruft alle verwendeten Trading-Symbole mit ihren entsprechenden Pip-Werten ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält nur Symbole, die in der Symbol-Setup-Tabelle verwendet werden\n- Enthält nur Symbole mit definierten Pip-Werten\n- Pip-Werte sind in EUR pro Lot angegeben\n\nAnwendungsfälle:\n- Berechnung von Gewinn/Verlust in EUR\n- Risikomanagement und Position-Sizing\n- Berechnung von Margin-Anforderungen\n- Anzeige von Pip-Werten im Trading-Dashboard", "tags": ["Symbol Management"], "responses": {"200": {"description": "Erfolgreiche Abfrage der Symbole mit Pip-Werten", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SymbolPipValue"}}, "example": [{"symbol": "EURUSD", "pipValuePerLotInProfitEUR": 10}, {"symbol": "GBPUSD", "pipValuePerLotInProfitEUR": 12.5}, {"symbol": "USDJPY", "pipValuePerLotInProfitEUR": 8.2}, {"symbol": "DAX", "pipValuePerLotInProfitEUR": 25}]}}}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/symbol_setups": {"get": {"summary": "Symbol-Setups abrufen (alle Symbole)", "description": "Ruft die Trading-Konfigurationen für alle Symbole ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Konfigurationsparameter für jedes Symbol\n- Filterbar nach Timeframe und Handelsrichtung\n- Enthält Deadzone-Informationen für Risikomanagement\n\nAnwendungsfälle:\n- Initialisierung von Trading-Systemen\n- Konfiguration von Trading-Parametern\n- Überwachung von Deadzones für Risikomanagement\n- Anzeige von Symbol-Konfigurationen im Trading-Dashboard", "tags": ["Symbol Management"], "parameters": [{"in": "query", "name": "timeframe", "required": false, "schema": {"type": "integer", "default": 30}, "description": "Zeitrahmen in Minuten", "example": 30}, {"in": "query", "name": "cmd", "required": false, "schema": {"type": "integer", "default": 0, "enum": [0, 1]}, "description": "Handelsrichtung (0 = <PERSON>, 1 = Short)", "example": 0}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Symbol-Setups", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SymbolSetup"}}, "example": [{"symbol": "EURUSD", "timeframe": 30, "cmd": 0, "defaulthighchancevolume": 2, "defaultmidchancevolume": 1, "chartPriceOffsetXTBandTradeView": 0.0005, "stoppOffset": 0.0025, "stoppOffSet_targetTF": 0.0035, "stoppOffsetmidchancevolume": 0.002, "trailstoppOffset": 0.0015, "trailstoppOffsetForWinningPositions": 0.001, "minimum_volume_m15": 0.5, "deadZoneLow": 1.085, "deadZoneHigh": 1.087, "deadZoneUpdateTime": "2023-04-15T14:30:00Z", "deadZoneValidUntil": "2023-04-24", "deadZoneUpdateSource": "Postman", "deadZoneFromTime": "08:00:00", "deadZoneToTime": "16:00:00"}, {"symbol": "GBPUSD", "timeframe": 30, "cmd": 0, "defaulthighchancevolume": 1.5, "defaultmidchancevolume": 0.8, "chartPriceOffsetXTBandTradeView": 0.0005, "stoppOffset": 0.003, "stoppOffSet_targetTF": 0.004, "stoppOffsetmidchancevolume": 0.0025, "trailstoppOffset": 0.002, "trailstoppOffsetForWinningPositions": 0.0015, "minimum_volume_m15": 0.4, "deadZoneLow": 1.245, "deadZoneHigh": 1.247, "deadZoneUpdateTime": "2023-04-15T14:30:00Z", "deadZoneValidUntil": "2023-04-24", "deadZoneUpdateSource": "Postman", "deadZoneFromTime": "08:00:00", "deadZoneToTime": "16:00:00"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/symbol_setup/{symbol}": {"get": {"summary": "Symbol-Setup für ein bestimmtes Symbol abrufen", "description": "Ruft die Trading-Konfiguration für ein bestimmtes Symbol ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Konfigurationsparameter für das angegebene Symbol\n- Filterbar nach Timeframe und Handelsrichtung\n- Enthält Deadzone-Informationen für Risikomanagement\n\nAnwendungsfälle:\n- Konfiguration von Trading-Parametern für ein bestimmtes Symbol\n- Überwachung von Deadzones für Risikomanagement\n- Anzeige von Symbol-Konfigurationen im Trading-Dashboard\n- Initialisierung von symbolspezifischen Trading-Strategien", "tags": ["Symbol Management"], "parameters": [{"in": "path", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das die Konfiguration abgerufen werden soll", "example": "EURUSD"}, {"in": "query", "name": "timeframe", "required": false, "schema": {"type": "integer", "default": 30}, "description": "Zeitrahmen in Minuten", "example": 30}, {"in": "query", "name": "cmd", "required": false, "schema": {"type": "integer", "default": 0, "enum": [0, 1]}, "description": "Handelsrichtung (0 = <PERSON>, 1 = Short)", "example": 0}], "responses": {"200": {"description": "Erfolgreiche Abfrage des Symbol-Setups", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SymbolSetup"}}, "example": [{"symbol": "EURUSD", "timeframe": 30, "cmd": 0, "defaulthighchancevolume": 2, "defaultmidchancevolume": 1, "chartPriceOffsetXTBandTradeView": 0.0005, "stoppOffset": 0.0025, "stoppOffSet_targetTF": 0.0035, "stoppOffsetmidchancevolume": 0.002, "trailstoppOffset": 0.0015, "trailstoppOffsetForWinningPositions": 0.001, "minimum_volume_m15": 0.5, "deadZoneLow": 1.085, "deadZoneHigh": 1.087, "deadZoneUpdateTime": "2023-04-15T14:30:00Z", "deadZoneValidUntil": "2023-04-24", "deadZoneUpdateSource": "Postman", "deadZoneFromTime": "08:00:00", "deadZoneToTime": "16:00:00"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/trade_history": {"get": {"summary": "Handelshistorie abrufen", "description": "Ruft die Handelshistorie für ein bestimmtes Konto ab, optional gefiltert nach Symbol und Zeitraum.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Informationen zu jedem Trade\n- Filterbar nach Symbol und Zeitraum in Tagen\n- Standardmäßig werden die letzten 7 Tage abgerufen (oder 3 Tage bei Filterung nach Symbol)\n\nAnwendungsfälle:\n- Analyse der Trading-Performance\n- Überprüfung vergangener Trades\n- Berechnung von Performance-Metriken\n- Identifizierung von erfolgreichen und nicht erfolgreichen Trading-Mustern", "tags": ["Trade History"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}, {"in": "query", "name": "symbol", "required": false, "schema": {"type": "string"}, "description": "Trading-Symbol, nach dem gefiltert werden soll", "example": "EURUSD"}, {"in": "query", "name": "days", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 365}, "description": "Anzahl der Tage in der Vergangenheit, für die Trades abgerufen werden sollen. Standardwert: 7 Tage (ohne Symbol-Filter) oder 3 Tage (mit Symbol-Filter)", "example": 7}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Handelshistorie", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeHistoryItem"}}, "example": [{"trade_id": 12345, "account": 1, "refId": "IG-D1", "symbol": "EURUSD", "cmd": 0, "volume": 1.5, "open_price": 1.0865, "close_price": 1.0885, "sl": 1.084, "tp": 1.09, "profit": 30, "commission": 1.5, "swap": 0.5, "entry_time": "2023-04-15T10:30:00Z", "exit_time": "2023-04-15T14:30:00Z", "comment": "Manual close", "magic_number": 12345, "duration_minutes": 240}, {"trade_id": 12346, "account": 1, "refId": "IG-D1", "symbol": "GBPUSD", "cmd": 1, "volume": 1, "open_price": 1.245, "close_price": 1.243, "sl": 1.247, "tp": 1.24, "profit": 20, "commission": 1, "swap": 0, "entry_time": "2023-04-15T11:15:00Z", "exit_time": "2023-04-15T13:45:00Z", "comment": "TP hit", "magic_number": 12345, "duration_minutes": 150}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/trade_history_stop_level": {"get": {"summary": "Stop-Loss-Levels aus der Handelshistorie abrufen", "description": "Ruft die Stop-Loss-Levels aus der Handelshistorie für ein bestimmtes Konto ab, optional gefiltert nach Symbol und Zeitraum.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält nur relevante Informationen für Stop-Loss-Analyse\n- Filterbar nach Symbol und Zeitraum in Tagen\n- Standardmäßig werden die letzten 7 Tage abgerufen (oder 3 Tage bei Filterung nach Symbol)\n\nAnwendungsfälle:\n- Analyse der Stop-Loss-Platzierung\n- Optimierung von Stop-Loss-Strategien\n- Identifizierung von Mustern bei Stop-Loss-Auslösungen\n- Berechnung von durchschnittlichen Stop-Loss-Distanzen", "tags": ["Trade History"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}, {"in": "query", "name": "symbol", "required": false, "schema": {"type": "string"}, "description": "Trading-Symbol, nach dem gefiltert werden soll", "example": "EURUSD"}, {"in": "query", "name": "days", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 365}, "description": "Anzahl der Tage in der Vergangenheit, für die Stop-Loss-Levels abgerufen werden sollen. Standardwert: 7 Tage (ohne Symbol-Filter) oder 3 Tage (mit Symbol-Filter)", "example": 7}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Stop-Loss-Levels", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeHistoryStopLevel"}}, "example": [{"trade_id": 12345, "symbol": "EURUSD", "cmd": 0, "open_price": 1.0865, "sl": 1.084, "entry_time": "2023-04-15T10:30:00Z"}, {"trade_id": 12346, "symbol": "GBPUSD", "cmd": 1, "open_price": 1.245, "sl": 1.247, "entry_time": "2023-04-15T11:15:00Z"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/trade_history_equity": {"get": {"summary": "Equity-<PERSON>rve a<PERSON>", "description": "Ruft die historische Equity-Kurve für ein bestimmtes Konto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Equity-Informationen im Zeitverlauf\n- Berechnet Drawdown und High-Watermark\n- Berücksichtigt den aktuellen Kontostand für die Berechnung\n\nAnwendungsfälle:\n- Visualisierung der Equity-Entwicklung\n- Ana<PERSON><PERSON> von Drawdown-Perioden\n- Berechnung von Performance-Metriken wie Sharpe Ratio\n- Überwachung der Margin-Nutzung im Zeitverlauf", "tags": ["Trade History"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}, {"in": "query", "name": "account", "required": false, "schema": {"type": "string"}, "description": "Optionale Sub-Konto-ID", "example": "12345"}, {"in": "query", "name": "currentbalance", "required": true, "schema": {"type": "number", "format": "float", "minimum": 0}, "description": "Aktueller Kontostand für die Berechnung", "example": 10000}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Equity-Kurve", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeHistoryEquity"}}, "example": [{"exit_time": "2023-04-15T14:30:00Z", "equity": 10250.5, "day_profit": 150.25, "balance": 10200, "floating_pl": 50.5, "margin_used": 500, "free_margin": 9750.5, "margin_level": 2050.1, "drawdown": 150, "high_watermark": 10400.5}, {"exit_time": "2023-04-15T15:00:00Z", "equity": 10280.75, "day_profit": 180.5, "balance": 10200, "floating_pl": 80.75, "margin_used": 500, "free_margin": 9780.75, "margin_level": 2056.15, "drawdown": 119.75, "high_watermark": 10400.5}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/statistics/week": {"get": {"summary": "Wochenstatistiken abrufen", "description": "Ruft Handelsstatistiken auf Wochenbasis für ein bestimmtes Konto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Gruppiert nach Jahr und Kalenderwoche\n- Enthält Min/Max-Gewinn, Anzahl der Trades und Gesamtgewinn pro Woche\n- Standardmäßig werden die letzten 3 Monate abgerufen\n\nAnwendungsfälle:\n- An<PERSON><PERSON> von wöchentlichen Performance-Metriken im Dashboard\n- Ana<PERSON><PERSON> von Handelsmustern über verschiedene Wochen\n- Identifizierung von wöchentlichen Trends\n- Berechnung von wöchentlichen Durchschnittswerten für Performance-Berichte", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Wochenstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeekStatistics"}}, "example": [{"year": 2023, "week": 15, "min_profit": -180, "max_profit": 420, "cnt_trades": 35, "sum_profit": 850}, {"year": 2023, "week": 14, "min_profit": -150, "max_profit": 380, "cnt_trades": 32, "sum_profit": 720}, {"year": 2023, "week": 13, "min_profit": -200, "max_profit": 450, "cnt_trades": 38, "sum_profit": 780}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/statistics/current_week": {"get": {"summary": "Statistiken der aktuellen Woche abrufen", "description": "Ruft Handelsstatistiken für die aktuelle Woche eines bestimmten Kontos ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält nur Daten der aktuellen Woche\n- Enthält Min/Max-Gewinn, Anzahl der Trades und Gesamtgewinn\n- Filtert automatisch nach dem aktuellen Jahr und der aktuellen Kalenderwoche\n\nAnwendungsfälle:\n- Echtzeit-Überwachung der aktuellen Wochenperformance\n- Dashboard-Anzeige für die laufende Woche\n- Vergleich mit Zielvorgaben für die aktuelle Woche\n- Schnelle Überprüfung des Wochenergebnisses", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der aktuellen Wochenstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrentWeekStatistics"}}, "example": [{"year": 2023, "week": 15, "min_profit": -180, "max_profit": 420, "cnt_trades": 35, "sum_profit": 850}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/ig/trades": {"get": {"summary": "Aktuelle offene Trades abrufen", "description": "Ruft alle aktuell offenen Trades von der IG Markets-Plattform ab.\n\nTechnische Details:\n- Gecachte Antworten (kurze TTL)\n- Enthält Pip-Wert-Berechnungen\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Portfolio-Überwachung\n- Positionsmanagement\n- Risikobewertung\n- Gewinn-/Verlustüberwachung", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto", "example": "IG-P1"}], "responses": {"200": {"description": "Offene Trades erfolgreich abgerufen", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IGTrade"}}}}}, "500": {"$ref": "#/components/responses/IGError"}}}}, "/api/v1/ig/trades/history": {"get": {"summary": "Handelshistorie abrufen", "description": "Ruft historische Handelstransaktionen von der IG Markets-Plattform ab.\n\nTechnische Details:\n- Gecachte Antworten (mittlere TTL)\n- Unterstützt Datumsbereichsfilterung\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Handelsanalyse\n- Leistungsüberwachung\n- Steuerberichterstattung\n- Strategieoptimierung", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}, {"name": "from", "in": "query", "schema": {"type": "string", "format": "date"}, "description": "Startdatum für die Handelshistorie (Format: YYYY-MM-DD)", "example": "2025-04-20"}, {"name": "to", "in": "query", "schema": {"type": "string", "format": "date"}, "description": "Enddatum für die Handelshistorie (Format: YYYY-MM-DD)", "example": "2025-04-21"}, {"name": "symbol", "in": "query", "schema": {"type": "string"}, "description": "Filtern nach Handelssymbol", "example": "CS.D.EURUSD.MINI.IP"}, {"name": "minus_days", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "Anzahl der Tage in der Vergangenheit, wenn kein Startdatum angegeben ist"}], "responses": {"200": {"description": "Handelshistorie erfolgreich abgerufen", "content": {"application/json": {"schema": {"type": "object", "properties": {"activities": {"type": "array", "items": {"$ref": "#/components/schemas/IGTradeHistory"}}}}}}}, "500": {"$ref": "#/components/responses/IGError"}}}}, "/api/v1/ig/accounts": {"get": {"summary": "Kontoinformationen abrufen", "description": "Ruft Kontoinformationen von der IG Markets-Plattform ab.\n\nTechnische Details:\n- Gecachte Antworten (kurze TTL)\n- Enthält Kontostand, verfügbare Mittel und Margin-Informationen\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Kontoüberwachung\n- Risikomanagement\n- Kapitalallokation", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "responses": {"200": {"description": "Kontoinformationen erfolgreich abgerufen", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IGAccount"}}}}, "500": {"$ref": "#/components/responses/IGError"}}}}, "/api/v1/ig/trades/margin-trade": {"get": {"summary": "Margin-Anforderungen berechnen", "description": "Berechnet die Margin-Anforderungen für einen potenziellen Trade.\n\nTechnische Details:\n- Gecachte Antworten (mittlere TTL)\n- Berücksichtigt Instrumentenspezifikationen und aktuelle Marktpreise\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Handelsplanung\n- Risikomanagement\n- Kapitalallokation", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}, {"name": "symbol", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Handelssymbol, für das die Margin berechnet werden soll", "example": "CS.D.EURUSD.MINI.IP"}, {"name": "direction", "in": "query", "required": true, "schema": {"type": "string", "enum": ["BUY", "SELL"]}, "description": "Handelsrichtung (BUY oder SELL)"}, {"name": "size", "in": "query", "schema": {"type": "number", "format": "float", "default": 1}, "description": "Handelsvolumen für die Margin-Berechnung", "example": 2}], "responses": {"200": {"description": "Margin-Anforderungen erfolgreich berechnet", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IGMarginCalculation"}}}}, "500": {"$ref": "#/components/responses/IGError"}}}}, "/api/v1/ig/trades/bridge": {"post": {"summary": "Trade im Bridge-Format ausführen", "description": "Führt einen Trade im Legacy-Bridge-Format aus, das mit älteren Systemen kompatibel ist.", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-D1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["tradeTransInfo"], "properties": {"tradeTransInfo": {"type": "object", "required": ["cmd", "symbol", "volume"], "properties": {"cmd": {"type": "integer", "description": "Handelsrichtung (0 = BUY, 1 = SELL)", "enum": [0, 1]}, "symbol": {"type": "string", "description": "Trading-Symbol"}, "volume": {"type": "number", "format": "float", "description": "Handelsvolumen"}, "sl": {"type": "number", "format": "float", "description": "Stop-Loss-Level"}}}, "simulation": {"type": "boolean", "description": "Gibt an, ob der Trade simuliert werden soll"}, "strategy": {"type": "string", "description": "Name der Handelsstrategie"}, "timeframe": {"type": "string", "description": "Zeitrahmen für den Trade"}}}}}}, "responses": {"200": {"description": "Trade erfolgreich ausgeführt", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"]}, "dealId": {"type": "string", "description": "ID des ausgeführten Trades"}, "dealReference": {"type": "string", "description": "Referenz des Trades"}}}}}}, "400": {"description": "Ungültige Anfrage"}, "500": {"description": "Fehler bei der Ausführung des Trades"}}}}, "/api/v1/ig/trades/execute": {"post": {"summary": "Trade ausführen", "description": "Führt einen Trade auf der IG Markets-Plattform aus.", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-D1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["epic", "direction", "size", "orderType"], "properties": {"epic": {"type": "string", "description": "Epic-Code des Instruments"}, "direction": {"type": "string", "enum": ["BUY", "SELL"], "description": "Handelsrichtung"}, "size": {"type": "string", "description": "Größe der Position"}, "expiry": {"type": "string", "description": "Ablaufdatum für den Auftrag"}, "orderType": {"type": "string", "enum": ["MARKET", "LIMIT", "STOP"], "description": "Art des Auftrags"}, "timeInForce": {"type": "string", "enum": ["EXECUTE_AND_ELIMINATE", "FILL_OR_KILL"], "description": "Zeitbedingung für den Auftrag"}, "guaranteedStop": {"type": "string", "enum": ["true", "false"], "description": "G<PERSON>t an, ob ein garantierter Stop verwendet werden soll"}, "forceOpen": {"type": "string", "enum": ["true", "false"], "description": "<PERSON>rz<PERSON>t das Öffnen einer neuen Position"}, "stopLevel": {"type": "string", "description": "Stop-Loss-Level"}, "dealReference": {"type": "string", "description": "Benutzerdefinierte Referenz für den Trade"}, "currencyCode": {"type": "string", "description": "Währungscode für den Trade"}}}}}}, "responses": {"200": {"description": "Trade erfolgreich ausgeführt", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"]}, "dealId": {"type": "string", "description": "ID des ausgeführten Trades"}, "dealReference": {"type": "string", "description": "Referenz des Trades"}}}}}}, "400": {"description": "Ungültige Anfrage"}, "500": {"description": "Fehler bei der Ausführung des Trades"}}}}, "/api/v1/ig/trades/status": {"post": {"summary": "Trade-Status abrufen", "description": "Prüft den Status eines Trades anhand seiner Deal-Referenz.", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-D1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["dealReference"], "properties": {"dealReference": {"type": "string", "description": "Referenz des Trades"}}}}}}, "responses": {"200": {"description": "Trade-Status erfolgreich abgerufen", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "description": "Status des Trades"}, "dealReference": {"type": "string", "description": "Referenz des Trades"}}}}}}, "400": {"description": "Ungültige Anfrage"}, "500": {"description": "Fehler bei der Statusabfrage"}}}}, "/api/v1/ig/trades/modify": {"post": {"summary": "Position ändern", "description": "Ändert eine bestehende Position auf der IG Markets-Plattform.", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-D1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["dealId"], "properties": {"dealId": {"type": "string", "description": "ID der zu ändernden Position"}, "stopLevel": {"type": "string", "description": "Neues Stop-Loss-Level"}}}}}}, "responses": {"200": {"description": "Position erfolgreich geändert", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"]}}}}}}, "400": {"description": "Ungültige Anfrage"}, "500": {"description": "Fehler bei der Änderung der Position"}}}}, "/api/v1/ig/trades/close": {"post": {"summary": "Position schließen", "description": "Sc<PERSON><PERSON><PERSON>t eine bestehende Position auf der IG Markets-Plattform.", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-D1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["dealId", "direction"], "properties": {"dealId": {"type": "string", "description": "ID der zu schließenden Position"}, "direction": {"type": "string", "enum": ["BUY", "SELL"], "description": "Richtung zum Schließen (entgegengesetzt zur Eröffnungsrichtung)"}, "epic": {"type": "string", "description": "Epic-Code des Instruments"}, "size": {"type": "string", "description": "Größe der zu schließenden Position"}, "orderType": {"type": "string", "enum": ["MARKET", "LIMIT", "STOP"], "description": "Art des Schließungsauftrags"}}}}}}, "responses": {"200": {"description": "Position erfolgreich geschlossen", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"]}}}}}}, "400": {"description": "Ungültige Anfrage"}, "500": {"description": "Fehler beim Schließen der Position"}}}}, "/api/v1/ig/markets/info": {"get": {"summary": "Marktinformationen abrufen", "description": "Ruft detaillierte Informationen über einen bestimmten Markt/Instrument ab.\n\nTechnische Details:\n- Gecachte Antworten (120 Sekunden TTL)\n- Enthält Instrumentenspezifikationen und aktuelle Marktpreise\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Handelsplanung\n- Marktanalyse\n- Instrumentenspezifikationen prüfen\n- Margin-Anforderungen berechnen", "tags": ["IG Market Data"], "parameters": [{"name": "symbol", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Handelssymbol, für das Informationen abgerufen werden sollen", "example": "CS.D.EURUSD.TODAY.IP"}], "responses": {"200": {"description": "Marktinformationen erfolgreich abgerufen", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarketInfo"}}}}, "404": {"description": "Marktinformationen nicht gefunden", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Market information not found"}}}}}}, "500": {"$ref": "#/components/responses/MarketDataError"}}}}, "/api/v1/ig/markets/prices": {"get": {"summary": "Aktuelle Preise abrufen", "description": "Ruft aktuelle Geld-/Briefkurse für ein bestimmtes Instrument ab.\n\nTechnische Details:\n- Gecachte Antworten (120 Sekunden TTL)\n- Berechnet den Spread zwischen Geld- und Briefkurs\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Preisüberwachung\n- Handelsausführung\n- Spread-Analyse", "tags": ["IG Market Data"], "parameters": [{"name": "symbol", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Handelssymbol, für das Preise abgerufen werden sollen", "example": "CS.D.EURUSD.TODAY.IP"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "Anzahl der abzurufenden Preise"}, {"name": "resolution", "in": "query", "schema": {"type": "string", "default": "MINUTE"}, "description": "Zeitauflösung für die Preise (z.B. MINUTE, HOUR, DAY)"}, {"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "responses": {"200": {"description": "<PERSON>ise erfolgreich abgerufen", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceResponse"}}}}, "404": {"description": "Preisdaten nicht gefunden", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Price data not found"}}}}}}, "500": {"$ref": "#/components/responses/MarketDataError"}}}}, "/api/v1/ig/markets/prices/history": {"get": {"summary": "Historische Preise abrufen", "description": "Ruft historische Preisdaten für ein bestimmtes Instrument und einen bestimmten Zeitraum ab.\n\nTechnische Details:\n- Gecachte Antworten (120 Sekunden TTL)\n- Unterstützt verschiedene Zeitauflösungen\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Technische Analyse\n- Backtesting von Handelsstrategien\n- Chartdarstellung\n- Musteranalyse", "tags": ["IG Market Data"], "parameters": [{"name": "symbol", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Handelssymbol, für das historische Preise abgerufen werden sollen", "example": "CS.D.EURUSD.TODAY.IP"}, {"name": "resolution", "in": "query", "required": true, "schema": {"type": "string", "enum": ["MINUTE", "MINUTE_5", "MINUTE_15", "MINUTE_30", "HOUR", "HOUR_4", "DAY", "WEEK", "MONTH"]}, "description": "Zeitauflösung für die historischen Preise"}, {"name": "from", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time"}, "description": "Startdatum für den Abfragezeitraum", "example": "2023-01-01T00:00:00Z"}, {"name": "to", "in": "query", "schema": {"type": "string", "format": "date-time"}, "description": "Enddatum für den Abfragezeitraum (Standard: aktuelles Datum/Uhrzeit)", "example": "2023-01-31T23:59:59Z"}, {"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "responses": {"200": {"description": "Historische Preise erfolgreich abgerufen", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HistoricalPriceResponse"}}}}, "400": {"description": "Ungültige Anfrageparameter", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Missing required parameters"}}}}}}, "404": {"description": "<PERSON>ine historischen Daten gefunden", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "No historical data found for the specified parameters"}}}}}}, "500": {"$ref": "#/components/responses/MarketDataError"}}}}, "/api/v1/ig/markets/prices/last": {"get": {"summary": "Letzte Kerze abrufen", "description": "Ruft die neueste Preiskerze für ein bestimmtes Instrument und eine bestimmte Zeitauflösung ab.\n\nTechnische Details:\n- Gecachte Antworten (120 Sekunden TTL)\n- Liefert OHLC-Daten für die letzte abgeschlossene Kerze\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Aktuelle Marktbedingungen überwachen\n- Handelssignale generieren\n- Technische Indikatoren aktualisieren", "tags": ["IG Market Data"], "parameters": [{"name": "symbol", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Handelssymbol, für das die letzte Kerze abgerufen werden soll", "example": "CS.D.EURUSD.TODAY.IP"}, {"name": "resolution", "in": "query", "required": true, "schema": {"type": "string", "enum": ["MINUTE", "MINUTE_5", "MINUTE_15", "MINUTE_30", "HOUR", "HOUR_4", "DAY", "WEEK", "MONTH"]}, "description": "Zeitauflösung für die Kerze"}, {"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "responses": {"200": {"description": "Letzte Kerze erfolgreich abgerufen", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LastCandleResponse"}}}}, "400": {"description": "Ungültige Anfrageparameter", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Missing required parameters"}}}}}}, "404": {"description": "<PERSON><PERSON>n gefunden", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "No recent candle data found"}}}}}}, "500": {"$ref": "#/components/responses/MarketDataError"}}}}, "/api/v1/mirrorTradingLogs/losts": {"get": {"summary": "Get lost mirror trades", "description": "Retrieves detailed information about losing trades from the mirror trading system.\nIncludes trade entry/exit points, profit/loss metrics, and trade parameters.\nData can be optionally refreshed from the source trading system.\n\nUse this endpoint to:\n- Analyze trading performance\n- Identify problematic trade patterns\n- Monitor risk management effectiveness\n", "tags": ["Mirror Trading"], "parameters": [{"in": "query", "name": "refreshData", "schema": {"type": "boolean", "default": false}, "description": "When true, synchronizes data with the trading system before returning results.\nThis may add a few seconds to the response time but ensures latest data.\n"}, {"in": "query", "name": "targetRefID", "schema": {"type": "string", "example": "D2", "pattern": "^[A-Z][0-9]$"}, "description": "Trading account identifier.\nFormat: Letter followed by number (e.g. D1, P2)\n- D* = Demo account\n- P* = Production account\n"}], "responses": {"200": {"description": "Successfully retrieved lost trades data", "content": {"application/json": {"schema": {"type": "object", "required": ["results"], "properties": {"results": {"type": "array", "items": {"type": "object", "required": ["log_row_number", "targetRefID", "log_order", "log_order2"], "properties": {"log_row_number": {"type": "integer", "description": "Sequential number of the log entry", "example": 1}, "total_rows": {"type": "integer", "description": "Total number of trades in the dataset", "example": 100}, "targetRefID": {"type": "string", "description": "Account identifier", "example": "D2"}, "log_order": {"type": "integer", "description": "Internal order reference", "example": 12345}, "log_order2": {"type": "integer", "description": "External broker order ID", "example": 67890}, "min_time": {"type": "string", "format": "date-time", "description": "Trade entry time", "example": "2025-01-12T10:30:00Z"}, "max_time": {"type": "string", "format": "date-time", "description": "Trade exit time", "example": "2025-01-12T14:45:00Z"}, "min_profit": {"type": "number", "format": "double", "description": "Minimum profit reached during trade", "example": -150.5}, "max_profit": {"type": "number", "format": "double", "description": "Maximum profit reached during trade", "example": -50.25}, "avg_profit": {"type": "number", "format": "double", "description": "Average profit during trade duration", "example": -85.75}, "cnt": {"type": "integer", "description": "Number of price updates for this trade", "example": 24}}}}, "refreshMirrorTradingLogs2LogsProfit": {"type": "string", "description": "Status of data refresh operation", "example": "Data processed successfully"}, "refreshEntryExitPoint2LogsProfit": {"type": "string", "description": "Status of entry/exit points refresh", "example": "Data processed: 15 rows changed"}}}}}}, "400": {"description": "Invalid parameters provided", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationError"}}}}, "500": {"description": "Server error occurred:\n- Database connection failed\n- Query execution error\n- Data synchronization failed\n", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DatabaseError"}}}}}}}, "/api/v1/mirrorTradingLogs/profits": {"get": {"summary": "Get profitable mirror trades", "description": "Retrieves mirror trading logs for trades that resulted in a profit", "tags": ["Mirror Trading"], "parameters": [{"in": "query", "name": "refreshData", "schema": {"type": "boolean"}, "description": "Whether to refresh the data before returning"}, {"in": "query", "name": "targetRefID", "schema": {"type": "string"}, "description": "Target account reference ID"}], "responses": {"200": {"description": "Profitable trades data", "content": {"application/json": {"schema": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"log_row_number": {"type": "integer"}, "total_rows": {"type": "integer"}, "targetRefID": {"type": "string"}, "log_order": {"type": "integer"}, "log_order2": {"type": "integer"}, "min_time": {"type": "string", "format": "date-time"}, "max_time": {"type": "string", "format": "date-time"}, "min_profit": {"type": "number"}, "max_profit": {"type": "number"}, "avg_profit": {"type": "number"}, "cnt": {"type": "integer"}}}}}}}}}, "500": {"description": "Database error"}}}}, "/api/v1/mirrorTradingLogs/losts/stats": {"get": {"summary": "Get lost trades statistics", "description": "Retrieves statistical analysis of losing mirror trades", "tags": ["Mirror Trading"], "responses": {"200": {"description": "Lost trades statistics", "content": {"application/json": {"schema": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"symbol": {"type": "string"}, "total_losses": {"type": "integer"}, "avg_loss": {"type": "number"}, "max_loss": {"type": "number"}, "total_loss_amount": {"type": "number"}}}}}}}}}, "500": {"description": "Database error"}}}}, "/api/v1/mirrorTradingLogs/bes/stats": {"get": {"summary": "Get break-even stop statistics", "description": "Retrieves statistics about break-even stop usage in mirror trading", "tags": ["Mirror Trading"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Account reference ID"}], "responses": {"200": {"description": "Break-even stop statistics", "content": {"application/json": {"schema": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"refID": {"type": "string"}, "symbol": {"type": "string"}, "total_bes_trades": {"type": "integer"}, "successful_bes": {"type": "integer"}, "total_sum": {"type": "number"}}}}}}}}}, "400": {"description": "Invalid parameters"}, "500": {"description": "Database error"}}}}, "/api/v1/mirrorTradingLogs/optimizations": {"get": {"summary": "Get optimization data", "description": "Retrieves optimization settings and results for mirror trading", "tags": ["Mirror Trading"], "parameters": [{"in": "query", "name": "targetRefID", "schema": {"type": "string", "default": "D1"}, "description": "Target account reference ID"}, {"in": "query", "name": "days", "schema": {"type": "integer", "default": 3, "minimum": 1, "maximum": 365}, "description": "Number of days to analyze"}, {"in": "query", "name": "timeframe", "schema": {"type": "integer", "default": 1, "minimum": 1, "maximum": 1440}, "description": "Timeframe in minutes"}], "responses": {"200": {"description": "Optimization data", "content": {"application/json": {"schema": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"targetRefID": {"type": "string"}, "date": {"type": "string", "format": "date"}, "optimization_type": {"type": "string"}, "parameters": {"type": "object"}, "result": {"type": "object"}}}}}}}}}, "500": {"description": "Database error"}}}}, "/api/v1/mirrorTradingLogs/profitcurves": {"get": {"summary": "Get profit curves", "description": "Retrieves profit curve data for mirror trading analysis", "tags": ["Mirror Trading"], "parameters": [{"in": "query", "name": "targetRefID", "schema": {"type": "string", "default": "D1"}, "description": "Target account reference ID"}, {"in": "query", "name": "days", "schema": {"type": "integer", "default": 3, "minimum": 1, "maximum": 365}, "description": "Number of days to analyze"}, {"in": "query", "name": "timeframe", "schema": {"type": "integer", "default": 1, "minimum": 1, "maximum": 1440}, "description": "Timeframe in minutes"}], "responses": {"200": {"description": "Profit curve data", "content": {"application/json": {"schema": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"targetRefID": {"type": "string"}, "log_timestamp": {"type": "string", "format": "date-time"}, "log_order2": {"type": "integer"}, "log_order": {"type": "integer"}, "profit": {"type": "number"}, "status": {"type": "string"}, "sl": {"type": "number"}, "tp": {"type": "number"}}}}}}}}}, "500": {"description": "Database error"}}}}, "/api/v1/statistics/weekday": {"get": {"summary": "Get weekday statistics", "description": "Retrieves trading statistics aggregated by weekday", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Account reference ID"}, {"in": "query", "name": "weeks", "schema": {"type": "integer", "minimum": 1, "maximum": 52, "default": 2}, "description": "Number of weeks to analyze"}], "responses": {"200": {"description": "Weekday trading statistics", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"refID": {"type": "string"}, "week": {"type": "integer"}, "dayOfWeek": {"type": "integer", "minimum": 1, "maximum": 7}, "cnt_trades": {"type": "integer"}, "profit": {"type": "number"}}}}}}}, "400": {"description": "Invalid parameters"}, "500": {"description": "Database error"}}}}, "/api/v1/statistics/dayhour": {"get": {"summary": "Get hour of day statistics", "description": "Retrieves trading statistics aggregated by hour of day", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Account reference ID"}, {"in": "query", "name": "weeks", "schema": {"type": "integer", "minimum": 1, "maximum": 52, "default": 2}, "description": "Number of weeks to analyze"}], "responses": {"200": {"description": "Hour of day trading statistics", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"refID": {"type": "string"}, "week": {"type": "integer"}, "hour": {"type": "integer", "minimum": 0, "maximum": 23}, "cnt_trades": {"type": "integer"}, "profit": {"type": "number"}}}}}}}, "400": {"description": "Invalid parameters"}, "500": {"description": "Database error"}}}}, "/api/v1/statistics/strategy_symbols": {"get": {"summary": "Get strategy-symbol statistics", "description": "Retrieves trading statistics aggregated by strategy and symbol", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Account reference ID"}, {"in": "query", "name": "months", "schema": {"type": "integer", "minimum": 1, "maximum": 12, "default": 2}, "description": "Number of months to analyze"}], "responses": {"200": {"description": "Strategy-symbol trading statistics", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"symbol": {"type": "string"}, "strategy": {"type": "string"}, "refId": {"type": "string"}, "activate_ig_d1": {"type": "boolean"}, "cnt_trades": {"type": "integer"}, "profit": {"type": "number"}}}}}}}, "400": {"description": "Invalid parameters"}, "500": {"description": "Database error"}}}}, "/api/v1/statistics/symbols_strategy": {"get": {"summary": "Get symbol-strategy statistics", "description": "Retrieves trading statistics aggregated by symbol and strategy", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Account reference ID"}, {"in": "query", "name": "months", "schema": {"type": "integer", "minimum": 1, "maximum": 12, "default": 2}, "description": "Number of months to analyze"}], "responses": {"200": {"description": "Symbol-strategy trading statistics", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"strategy": {"type": "string"}, "symbol": {"type": "string"}, "refId": {"type": "string"}, "activate_ig_d1": {"type": "boolean"}, "cnt_trades": {"type": "integer"}, "profit": {"type": "number"}}}}}}}, "400": {"description": "Invalid parameters"}, "500": {"description": "Database error"}}}}, "/api/v1/statistics/day": {"get": {"summary": "Retrieve daily trading performance", "description": "Fetches detailed trading statistics aggregated by day.\n\nTechnical Details:\n- Cached responses (5 min TTL)\n- Timezone-aware calculations\n- Profit rounding to whole numbers\n- Account-specific filtering\n\nPerformance Metrics:\n- Daily profit/loss totals\n- Trade count analysis\n- Min/max trade performance\n- Success rate calculation\n\nUse Cases:\n- Daily performance tracking\n- Risk management analysis\n- Strategy effectiveness monitoring\n- Pattern identification\n", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string", "enum": ["P1", "P2", "D1", "D2", "SimD1", "SimD2", "SimD3"]}, "description": "Account reference ID"}, {"in": "query", "name": "startDate", "schema": {"type": "string", "format": "date"}, "description": "Start date for statistics (defaults to 14 days ago)"}], "responses": {"200": {"description": "Daily trading statistics", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "format": "date", "description": "Trading day"}, "min_profit": {"type": "number", "description": "Minimum profit achieved that day", "example": -50.5}, "max_profit": {"type": "number", "description": "Maximum profit achieved that day", "example": 120.75}, "cnt_trades": {"type": "integer", "description": "Number of trades executed", "example": 15}, "sum_profit": {"type": "number", "description": "Total profit/loss for the day", "example": 350.25}}}}}}}, "400": {"description": "Invalid parameters", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "refID is required"}}}}}}, "500": {"description": "Database error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Failed to retrieve daily statistics"}}}}}}}}}, "/api/v1/statistics/week": {"get": {"summary": "Retrieve weekly trading performance", "description": "Fetches detailed trading statistics aggregated by week.\n\nTechnical Details:\n- Cached responses (5 min TTL)\n- Week numbering (ISO-8601)\n- Rolling 3-month analysis\n- Performance benchmarking\n\nPerformance Metrics:\n- Weekly profit/loss totals\n- Trade frequency analysis\n- Win/loss ratio tracking\n- Risk-adjusted returns\n\nUse Cases:\n- Weekly performance review\n- Strategy optimization\n- Risk exposure analysis\n- Trading consistency check\n", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string", "enum": ["P1", "P2", "D1", "D2", "SimD1", "SimD2", "SimD3"]}, "description": "Account reference ID"}], "responses": {"200": {"description": "Weekly trading statistics", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"year": {"type": "integer", "description": "Year of the trading week", "example": 2024}, "week": {"type": "integer", "description": "Week number (1-52)", "example": 2}, "min_profit": {"type": "number", "description": "Minimum profit achieved that week", "example": -150.5}, "max_profit": {"type": "number", "description": "Maximum profit achieved that week", "example": 320.75}, "cnt_trades": {"type": "integer", "description": "Number of trades executed", "example": 45}, "sum_profit": {"type": "number", "description": "Total profit/loss for the week", "example": 850.25}}}}}}}, "400": {"description": "Invalid parameters", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "refID is required"}}}}}}, "500": {"description": "Database error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Failed to retrieve weekly statistics"}}}}}}}}}, "/api/v1/statistics/month": {"get": {"summary": "Retrieve monthly trading performance", "description": "Fetches detailed trading statistics aggregated by month.\n\nTechnical Details:\n- Cached responses (5 min TTL)\n- Monthly rollover handling\n- Quarter-end calculations\n- Year-to-date tracking\n\nPerformance Metrics:\n- Monthly profit/loss totals\n- Trade volume analysis\n- Performance trends\n- Capital efficiency\n\nUse Cases:\n- Monthly performance review\n- Strategy assessment\n- Capital allocation planning\n- Long-term trend analysis\n", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Account reference ID"}], "responses": {"200": {"description": "Monthly trading statistics", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"year": {"type": "integer"}, "month": {"type": "integer"}, "min_profit": {"type": "number"}, "max_profit": {"type": "number"}, "cnt_trades": {"type": "integer"}, "sum_profit": {"type": "number"}}}}}}}, "400": {"description": "Invalid parameters"}, "500": {"description": "Database error"}}}}, "/api/v1/trade_history_equity": {"get": {"summary": "Retrieve historical equity curve", "description": "Calculates detailed historical equity curve for a trading account.\nIncludes running balance, floating P/L, and daily performance metrics.\n\nTechnical Details:\n- Cached response (5 min TTL)\n- 1-minute data resolution\n- Includes margin utilization\n- Tracks drawdown periods\n\nAnalysis Features:\n- Maximum drawdown calculation\n- Equity curve smoothing\n- Performance metrics\n- Risk-adjusted returns\n", "tags": ["Trade History"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Account reference ID for filtering data"}, {"in": "query", "name": "account", "schema": {"type": "string"}, "description": "Optional sub-account identifier"}, {"in": "query", "name": "currentbalance", "required": true, "schema": {"type": "number", "minimum": 0, "format": "float"}, "description": "Current account balance for calculations"}], "responses": {"200": {"description": "Successfully retrieved equity history", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"exit_time": {"type": "string", "format": "date-time", "description": "Timestamp of equity point (UTC)"}, "equity": {"type": "number", "format": "float", "description": "Total equity at this point"}, "day_profit": {"type": "number", "format": "float", "description": "Profit/loss for the day"}, "balance": {"type": "number", "format": "float", "description": "Account balance without floating P/L"}, "floating_pl": {"type": "number", "format": "float", "description": "Current floating profit/loss"}, "margin_used": {"type": "number", "format": "float", "description": "Margin currently in use"}, "free_margin": {"type": "number", "format": "float", "description": "Available margin for new trades"}, "margin_level": {"type": "number", "format": "float", "description": "Current margin level percentage"}, "drawdown": {"type": "number", "format": "float", "description": "Current drawdown from peak"}, "high_watermark": {"type": "number", "format": "float", "description": "Highest recorded equity level"}}}}}}}, "400": {"description": "Invalid parameters provided", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "boolean", "example": true}, "status": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "Invalid currentbalance parameter"}}}}}}, "500": {"description": "Database or server error occurred", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "boolean", "example": true}, "status": {"type": "integer", "example": 500}, "message": {"type": "string", "example": "Database operation failed"}}}}}}}}}}}