const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam, validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');

async function getSymbolSetupsIndependent(symbol, timeframe = 30, cmd = 0) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getSymbolSetupsIndependent', 'Starting symbol setup retrieval', {
            symbol,
            timeframe,
            cmd
        });

        // Validate timeframe
        timeframe = validateNumericParam(timeframe, {
            defaultValue: 30,
            min: 1,
            max: 1440,
            paramName: 'timeframe'
        });

        // Validate cmd
        cmd = validateNumericParam(cmd, {
            defaultValue: 0,
            min: 0,
            max: 1,
            paramName: 'cmd'
        });

        if (symbol !== undefined) {
            symbol = validateStringParam(symbol, {
                minLength: 1,
                maxLength: 50,
                paramName: 'symbol'
            });
        }

        const sql = symbol === undefined ?
            `SELECT s.*, ss.defaulthighchancevolume, ss.defaultmidchancevolume, ss.chartPriceOffsetXTBandTradeView,
                ss.stoppOffset, stoppOffSet_targetTF, ss.stoppOffsetmidchancevolume, ss.trailstoppOffset, ss.trailstoppOffsetForWinningPositions,
                ss.minimum_volume_m15, 
                ss.deadZoneLow, ss.deadZoneHigh, ss.deadZoneUpdateTime, ss.deadZoneValidUntil, ss.deadZoneUpdateSource, ss.deadZoneFromTime, ss.deadZoneToTime  
                from \`symbol_setups\` ss, symbols s where ss.symbol=s.symbol and ss.timeframe=? and ss.cmd=?` :
            `SELECT s.*, ss.defaulthighchancevolume, ss.defaultmidchancevolume, ss.chartPriceOffsetXTBandTradeView,
                ss.stoppOffset, stoppOffSet_targetTF, ss.stoppOffsetmidchancevolume, ss.trailstoppOffset, ss.trailstoppOffsetForWinningPositions,
                ss.minimum_volume_m15,
                ss.deadZoneLow, ss.deadZoneHigh, ss.deadZoneUpdateTime, ss.deadZoneValidUntil, ss.deadZoneUpdateSource, ss.deadZoneFromTime, ss.deadZoneToTime
                from \`symbol_setups\` ss, symbols s where ss.symbol=s.symbol and s.symbol=upper(?) and ss.timeframe=? and ss.cmd=?`;

        const query = {
            sql,
            bigIntAsNumber: true,
            timezone: 'de_de'
        };

        log(LOG_LEVELS.DEBUG, 'getSymbolSetupsIndependent', 'Executing query', {
            symbol,
            timeframe,
            cmd
        });

        const result = await executeQuery(query, symbol === undefined ? [timeframe, cmd] : [symbol, timeframe, cmd]);
        
        if (!result) {
            throw new DatabaseError('No symbol setups found');
        }

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getSymbolSetupsIndependent', 'Failed to fetch symbol setups', {
            error: err.message,
            stack: err.stack,
            symbol,
            timeframe,
            cmd
        });
        throw err;
    } finally {
        logPerformance('getSymbolSetupsIndependent', startTime);
    }
}

async function getSymbolSetups(req, res) {
    try {
        const symbol = req.params.symbol;
        const timeframe = req.params.timeframe || 30;
        const cmd = req.params.cmd || 0;

        const result = await withCacheWrapper(
            'GENERAL',
            'getSymbolSetups',
            () => getSymbolSetupsIndependent(symbol, timeframe, cmd),
            [symbol, timeframe, cmd]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getSymbolSetups');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getSymbolSetups,
    getSymbolSetupsIndependent
};
