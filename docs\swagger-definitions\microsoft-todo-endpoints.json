{"paths": {"/api/v1/microsoft/todo/tasks": {"post": {"summary": "Create a new task in Microsoft ToDo", "description": "Creates a new task in the specified Microsoft ToDo list using Microsoft Graph API.\n\nTechnical Details:\n- Uses OAuth 2.0 Client Credentials Flow for authentication\n- Integrates with Microsoft Graph API v1.0\n- Supports task categorization and due dates\n- Automatic retry logic for rate limiting and server errors\n- Comprehensive input validation and error handling\n\nUse Cases:\n- Automated task creation from trading alerts\n- Integration with workflow management systems\n- Task scheduling for trading operations\n- Client meeting and report reminders\n- Risk management task automation\n\nAuthentication:\n- Requires valid Azure App Registration\n- Uses application permissions (not delegated)\n- Supports multi-tenant and single-tenant configurations\n\nRate Limiting:\n- Respects Microsoft Graph API throttling limits\n- Implements exponential backoff retry strategy\n- Handles 429 responses gracefully", "tags": ["Microsoft ToDo"], "security": [{"ApiKeyAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftTodoTaskCreate"}, "examples": {"basic_task": {"summary": "Basic task creation", "description": "Simple task with just title and list ID", "value": {"listId": "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA==", "title": "Review quarterly reports", "importance": "normal"}}, "detailed_task": {"summary": "Detailed task with due date", "description": "Comprehensive task with description, due date, and categories", "value": {"listId": "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA==", "title": "Prepare presentation for client meeting", "body": "Include market analysis, trading performance metrics, and future strategy recommendations", "dueDateTime": "2025-01-15T14:00:00.000Z", "importance": "high", "categories": ["Work", "Client Meeting", "Presentation"], "timeZone": "Europe/Berlin"}}, "trading_alert": {"summary": "Trading-related task", "description": "Task created from automated trading system alert", "value": {"listId": "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA==", "title": "Review EURUSD position", "body": "Check stop loss levels and consider profit taking based on technical analysis. Current position: Long 1.2 lots at 1.0850", "importance": "high", "categories": ["Trading", "EURUSD", "Risk Management"]}}, "risk_management": {"summary": "Risk management task", "description": "Automated risk management reminder", "value": {"listId": "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA==", "title": "Daily risk assessment", "body": "Review portfolio exposure, check correlation matrix, and validate position sizing according to risk management rules", "dueDateTime": "2025-01-03T09:00:00.000Z", "importance": "high", "categories": ["Risk Management", "Daily Tasks", "Portfolio"], "timeZone": "UTC"}}}}}}, "responses": {"201": {"description": "Task created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftTodoTaskResponse"}, "examples": {"successful_creation": {"summary": "Successful task creation", "value": {"success": true, "message": "Task created successfully", "data": {"id": "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwBGAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAhHKQZHItDEOVCn8U3xuA2AABmQeVQAAAA==", "title": "Review quarterly reports", "body": null, "status": "notStarted", "importance": "normal", "createdDateTime": "2025-01-02T10:30:00.000Z", "lastModifiedDateTime": "2025-01-02T10:30:00.000Z", "dueDateTime": null, "categories": [], "webUrl": "https://to-do.office.com/tasks/id/AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwBGAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAhHKQZHItDEOVCn8U3xuA2AABmQeVQAAAA==", "listId": "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA=="}, "meta": {"requestId": "req_1704193800000_abc123", "processingTime": 1250, "timestamp": "2025-01-02T10:30:00.000Z"}}}}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"missing_title": {"summary": "Missing required title", "value": {"success": false, "message": "Invalid input data", "error": "title is required and must be a non-empty string", "details": {}, "meta": {"requestId": "req_1704193800000_abc123", "processingTime": 45, "timestamp": "2025-01-02T10:30:00.000Z"}}}, "invalid_date": {"summary": "Invalid due date format", "value": {"success": false, "message": "Invalid input data", "error": "dueDateTime must be a valid ISO 8601 date string if provided", "details": {}, "meta": {"requestId": "req_1704193800000_def456", "processingTime": 32, "timestamp": "2025-01-02T10:30:00.000Z"}}}}}}}, "401": {"description": "Authentication failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"auth_failed": {"summary": "Microsoft authentication failed", "value": {"success": false, "message": "Microsoft authentication failed", "error": "Invalid or expired credentials", "meta": {"requestId": "req_1704193800000_abc123", "processingTime": 2100, "timestamp": "2025-01-02T10:30:00.000Z"}}}}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"insufficient_permissions": {"summary": "Access denied", "value": {"success": false, "message": "Insufficient permissions", "error": "Access denied to Microsoft ToDo resources", "meta": {"requestId": "req_1704193800000_ghi789", "processingTime": 890, "timestamp": "2025-01-02T10:30:00.000Z"}}}}}}}, "404": {"description": "ToDo list not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"list_not_found": {"summary": "Specified list not found", "value": {"success": false, "message": "Resource not found", "error": "The specified ToDo list was not found", "meta": {"requestId": "req_1704193800000_jkl012", "processingTime": 750, "timestamp": "2025-01-02T10:30:00.000Z"}}}}}}}, "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"rate_limit": {"summary": "Too many requests", "value": {"success": false, "message": "Rate limit exceeded", "error": "Too many requests to Microsoft Graph API", "meta": {"requestId": "req_1704193800000_mno345", "processingTime": 120, "timestamp": "2025-01-02T10:30:00.000Z"}}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"server_error": {"summary": "Unexpected server error", "value": {"success": false, "message": "Internal server error", "error": "An unexpected error occurred", "meta": {"requestId": "req_1704193800000_pqr678", "processingTime": 3200, "timestamp": "2025-01-02T10:30:00.000Z"}}}}}}}}}}}}