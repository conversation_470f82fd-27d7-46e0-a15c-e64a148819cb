const { ValidationError } = require('../controllers/database/errors/database_errors');

/**
 * <PERSON><PERSON><PERSON>t einen numerischen Parameter
 *
 * @param {any} value - Zu validierender Wert
 * @param {Object} options - Validierungsoptionen
 * @param {boolean} [options.required=false] - Ob der Parameter erforderlich ist
 * @param {number} [options.defaultValue] - Standardwert falls nicht angegeben
 * @param {number} [options.min] - Minimaler erlaubter Wert
 * @param {number} [options.max] - Maximaler erlaubter Wert
 * @param {string} options.paramName - Name des Parameters für Fehlermeldungen
 * @returns {number} Validierter Wert
 * @throws {ValidationError} Bei ungültigem Wert
 */
function validateNumericParam(value, options = {}) {
    const {
        required = false,
        defaultValue,
        min,
        max,
        paramName = 'parameter'
    } = options;

    if (value === undefined || value === null || value==='') {
        if (required) {
            throw new ValidationError(`${paramName} is required`);
        }
        return defaultValue;
    }

    const numValue = Number(value);
    if (isNaN(numValue)) {
        throw new ValidationError(`${paramName} must be a number`);
    }

    if (min !== undefined && numValue < min) {
        throw new ValidationError(`${paramName} must be at least ${min}`);
    }

    if (max !== undefined && numValue > max) {
        throw new ValidationError(`${paramName} must be at most ${max}`);
    }

    return numValue;
}

/**
 * Validiert einen String-Parameter
 *
 * @param {any} value - Zu validierender Wert
 * @param {Object} options - Validierungsoptionen
 * @param {boolean} [options.required=false] - Ob der Parameter erforderlich ist
 * @param {string} [options.defaultValue] - Standardwert falls nicht angegeben
 * @param {number} [options.minLength] - Minimale Länge
 * @param {number} [options.maxLength] - Maximale Länge
 * @param {string} options.paramName - Name des Parameters für Fehlermeldungen
 * @returns {string} Validierter Wert
 * @throws {ValidationError} Bei ungültigem Wert
 */
function validateStringParam(value, options = {}) {
    const {
        required = false,
        defaultValue,
        minLength,
        maxLength,
        paramName = 'parameter'
    } = options;

    if (value === undefined || value === null) {
        if (required) {
            throw new ValidationError(`${paramName} is required`);
        }
        return defaultValue;
    }

    const strValue = String(value);

    if (minLength !== undefined && strValue.length < minLength) {
        throw new ValidationError(`${paramName} must be at least ${minLength} characters long`);
    }

    if (maxLength !== undefined && strValue.length > maxLength) {
        throw new ValidationError(`${paramName} must be at most ${maxLength} characters long`);
    }

    return strValue;
}


/**
 * Validiert einen Boolean-Parameter
 *
 * @param {any} value - Zu validierender Wert
 * @param {Object} options - Validierungsoptionen
 * @param {boolean} [options.required=false] - Ob der Parameter erforderlich ist
 * @param {boolean} [options.defaultValue] - Standardwert falls nicht angegeben
 * @param {string} options.paramName - Name des Parameters für Fehlermeldungen
 * @returns {boolean|undefined} Validierter Wert
 * @throws {ValidationError} Bei ungültigem Wert
 */
function validateBooleanParam(value, options = {}) {
    const {
        required = false,
        defaultValue,
        paramName = 'parameter'
    } = options;

    if (value === undefined || value === null || value === '') {
        if (required) {
            throw new ValidationError(`${paramName} is required`);
        }
        return defaultValue;
    }

    // Handle string representations of boolean values
    if (typeof value === 'string') {
        const lowerValue = value.toLowerCase();
        if (lowerValue === 'true' || lowerValue === '1') {
            return true;
        }
        if (lowerValue === 'false' || lowerValue === '0') {
            return false;
        }
        throw new ValidationError(`${paramName} must be a boolean value (true/false, 1/0)`);
    }

    // Handle numeric representations
    if (typeof value === 'number') {
        if (value === 1) return true;
        if (value === 0) return false;
        throw new ValidationError(`${paramName} must be a boolean value (true/false, 1/0)`);
    }

    // Handle actual boolean values
    if (typeof value === 'boolean') {
        return value;
    }

    throw new ValidationError(`${paramName} must be a boolean value (true/false, 1/0)`);
}


/**
 * Validiert einen Datums-Parameter
 * @param {*} value - Der zu validierende Wert
 * @param {Object} options - Validierungsoptionen
 * @returns {Date} - Das validierte Datum
 */
function validateDateParam(value, options = {}) {
    const {
        required = false,
        defaultValue,
        minDate,
        maxDate,
        paramName = 'Parameter'
    } = options;

    if (value === undefined || value === null) {
        if (required) {
            throw new ValidationError(`${paramName} ist erforderlich`);
        }
        return defaultValue;
    }

    const dateValue = new Date(value);
    if (isNaN(dateValue.getTime())) {
        throw new ValidationError(`${paramName} muss ein gültiges Datum sein`);
    }

    if (minDate && dateValue < minDate) {
        throw new ValidationError(`${paramName} muss nach ${minDate.toISOString()} liegen`);
    }

    if (maxDate && dateValue > maxDate) {
        throw new ValidationError(`${paramName} muss vor ${maxDate.toISOString()} liegen`);
    }

    return dateValue;
}

/**
 * Validiert einen Array-Parameter
 * @param {*} value - Der zu validierende Wert
 * @param {Object} options - Validierungsoptionen
 * @returns {Array} - Das validierte Array
 */
function validateArrayParam(value, options = {}) {
    const {
        required = false,
        defaultValue = [],
        minLength,
        maxLength,
        elementValidator,
        paramName = 'Parameter'
    } = options;

    if (value === undefined || value === null) {
        if (required) {
            throw new ValidationError(`${paramName} ist erforderlich`);
        }
        return defaultValue;
    }

    if (!Array.isArray(value)) {
        throw new ValidationError(`${paramName} muss ein Array sein`);
    }

    if (minLength !== undefined && value.length < minLength) {
        throw new ValidationError(`${paramName} muss mindestens ${minLength} Elemente enthalten`);
    }

    if (maxLength !== undefined && value.length > maxLength) {
        throw new ValidationError(`${paramName} darf nicht mehr als ${maxLength} Elemente enthalten`);
    }

    if (elementValidator) {
        return value.map((element, index) => {
            try {
                return elementValidator(element);
            } catch (error) {
                throw new ValidationError(`${paramName}[${index}]: ${error.message}`);
            }
        });
    }

    return value;
}

module.exports = {
    validateNumericParam,
    validateStringParam,
    validateDateParam,
    validateArrayParam,
    validateBooleanParam
};
