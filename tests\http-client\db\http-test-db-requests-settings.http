
###

// @name mirrorTradingSettings
GET {{API_BASE_URL}}/api/v1/db/mirrorTradingSettings?targetRefID=IG-P1&sourceRefID=IG-D1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name accountSettings
GET {{API_BASE_URL}}/api/v1/db/accountSettings?refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###
