@demo-epic = CS.D.EURUSD.MINI.IP

### accounts: Basis-Infos
GET {{API_BASE_URL}}/api/v1/ig/accounts

> {%
    client.test("Account Margin Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Account Margin Response contains accounts array", function() {
        const body = response.body;
        client.assert(Array.isArray(body.accounts), "Response should contain accounts array");
        client.assert(body.accounts.length > 0, "Accounts array should not be empty");
    });

    client.test("Account objects have required properties", function() {
        const account = response.body.accounts[0];
        client.assert(account.hasOwnProperty('accountId'), "Missing accountId");
        client.assert(account.hasOwnProperty('accountName'), "Missing accountName");
        client.assert(account.hasOwnProperty('status'), "Missing status");
        client.assert(account.hasOwnProperty('accountType'), "Missing accountType");
        client.assert(account.hasOwnProperty('preferred'), "Missing preferred");
        client.assert(account.hasOwnProperty('balance'), "Missing balance");
        client.assert(account.hasOwnProperty('currency'), "Missing currency");
    });

    client.test("Account balance object has required properties", function() {
        const balance = response.body.accounts[0].balance;
        client.assert(balance.hasOwnProperty('balance'), "Missing balance.balance");
        client.assert(balance.hasOwnProperty('deposit'), "Missing balance.deposit");
        client.assert(balance.hasOwnProperty('profitLoss'), "Missing balance.profitLoss");
        client.assert(balance.hasOwnProperty('available'), "Missing balance.available");
    });

    client.test("Account values have correct types", function() {
        const account = response.body.accounts[0];
        client.assert(typeof account.accountId === 'string', "accountId should be string");
        client.assert(typeof account.accountName === 'string', "accountName should be string");
        client.assert(typeof account.status === 'string', "status should be string");
        client.assert(typeof account.preferred === 'boolean', "preferred should be boolean");
        client.assert(typeof account.balance.balance === 'number', "balance.balance should be number");
        client.assert(typeof account.balance.available === 'number', "balance.available should be number");
    });
%}
