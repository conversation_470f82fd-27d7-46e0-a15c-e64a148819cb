
const db = require('../../../configs/config_db')
const moment = require("moment");

exports.getStrategyDocumentation = async (req, res) => {
    try {
        const p_query = {
            sql: 'SELECT * from `strategy_documentation` order by timestamp desc',
            bigIntAsNumber: true,
            dateStrings: false,
            timezone: 'de_de'
        }
        console.log("[db_get_strategies][getStrategyDocumentation] Query");
        const result = await db.pool.query(p_query, []);
        res.send(result);
    } catch (err) {
        throw err;
    }
};

exports.getStrategyActivationReportAggregated = async (req, res) => {
    try {
        var p_startdate = req.query.startdate;
        var p_weeks = req.query.weeks;
        var p_months = req.query.months;
        var p_useStatsWithLastStrategyChance = req.query.useStatsWithLastStrategyChance || 'any';
        var p_only_d1_activated = (req.query.only_d1_activated === true || req.query.only_d1_activated === 'true' || req.query.only_d1_activated === '1') || false;
        var p_minimize_analyze = (req.query.minimize_analyze === true || req.query.minimize_analyze === 'true' || req.query.minimize_analyze === '1') || false;
        var p_ignore_inactive_state = (req.query.ignoreInActiveState === true || req.query.ignoreInActiveState === 'true' || req.query.ignoreInActiveState === '1') || false;

        if (p_weeks != undefined) {
            p_startdate = moment().subtract(p_weeks, 'weeks').format('YYYY-MM-DD');
        } else if (p_months != undefined) {
            p_startdate = moment().subtract(p_months, 'months').format('YYYY-MM-DD');
        } else if (p_startdate == undefined) {
            p_startdate = moment().subtract(4, 'months').format('YYYY-MM-DD'); 
        }

        if (process.env.NODE_ENV !== 'production')
            console.log("[db_get_strategies][getStrategyActivationReportAggregated] Start date:", p_startdate, ", activated-d1-only", p_only_d1_activated, ", ignoreInActiveState:", p_ignore_inactive_state);

        // Build WHERE clause for st.inactive
        const whereInactive = p_ignore_inactive_state ? ' ' : ' st.inactive = 0 AND ';

        const p_query = {
            sql: `SELECT
        LOWER(CONCAT(st.strategy, ": ", st.symbol, ": ", st.timeframe, "m")) AS COMMENT,
        st.symbol AS symbol,
        st.timeframe AS timeframe,
        st.strategy,
        st.strategy_updated,
        -- D1
        ROUND(SUM(IF(d.refID = 'IG-D1' and d.cmd=0, d.profit, 0)), 1) AS profit_ig_d1_long,
        ROUND(SUM(IF(d.refID = 'IG-D1' and d.cmd=0 and d.exit_time>=subdate(current_date(),INTERVAL 7 day), d.profit, 0)), 1) AS profit_ig_d1_last7d_long,
        ROUND(SUM(IF(d.refID = 'IG-D1' and d.cmd=0 and d.exit_time>=subdate(current_date(),INTERVAL 1 month), d.profit, 0)), 1) AS profit_ig_d1_last1m_long,
        ROUND(SUM(IF(d.refID = 'IG-D1' and d.cmd=1, d.profit, 0)), 1) AS profit_ig_d1_short,
        ROUND(SUM(IF(d.refID = 'IG-D1' and d.cmd=1 and d.exit_time>=subdate(current_date(),INTERVAL 7 day), d.profit, 0)), 1) AS profit_ig_d1_last7d_short,
        ROUND(SUM(IF(d.refID = 'IG-D1' and d.cmd=1 and d.exit_time>=subdate(current_date(),INTERVAL 1 month), d.profit, 0)), 1) AS profit_ig_d1_last1m_short,
        ROUND(SUM(IF(d.refID = 'IG-D1', d.profit, 0)), 1) AS profit_ig_d1,
        ROUND(SUM(IF(d.refID = 'IG-D1' and d.exit_time>=subdate(current_date(),INTERVAL 7 day), d.profit, 0)), 1) AS profit_ig_d1_last7d,
        ROUND(SUM(IF(d.refID = 'IG-D1' and d.exit_time>=subdate(current_date(),INTERVAL 1 month), d.profit, 0)), 1) AS profit_ig_d1_last1m,
        COUNT(IF(d.refID = 'IG-D1', d.profit, NULL)) AS trades_ig_d1,
        
        -- P1
        ROUND(SUM(IF(d.refID = 'IG-P1' and d.cmd=0, d.profit, 0)), 1) AS profit_ig_p1_long,
        ROUND(SUM(IF(d.refID = 'IG-P1' and d.cmd=0 and d.exit_time>=subdate(current_date(),INTERVAL 7 day), d.profit, 0)), 1) AS profit_ig_p1_last7d_long,
        ROUND(SUM(IF(d.refID = 'IG-P1' and d.cmd=0 and d.exit_time>=subdate(current_date(),INTERVAL 1 month), d.profit, 0)), 1) AS profit_ig_p1_last1m_long,
        ROUND(SUM(IF(d.refID = 'IG-P1' and d.cmd=1, d.profit, 0)), 1) AS profit_ig_p1_short,
        ROUND(SUM(IF(d.refID = 'IG-P1' and d.cmd=1 and d.exit_time>=subdate(current_date(),INTERVAL 7 day), d.profit, 0)), 1) AS profit_ig_p1_last7d_short,
        ROUND(SUM(IF(d.refID = 'IG-P1' and d.cmd=1 and d.exit_time>=subdate(current_date(),INTERVAL 1 month), d.profit, 0)), 1) AS profit_ig_p1_last1m_short,
        ROUND(SUM(IF(d.refID = 'IG-P1', d.profit, 0)), 1) AS profit_ig_p1,
        ROUND(SUM(IF(d.refID = 'IG-P1' and d.exit_time>=subdate(current_date(),INTERVAL 7 day), d.profit, 0)), 1) AS profit_ig_p1_last7d,
        ROUND(SUM(IF(d.refID = 'IG-P1' and d.exit_time>=subdate(current_date(),INTERVAL 1 month), d.profit, 0)), 1) AS profit_ig_p1_last1m,
        COUNT(IF(d.refID = 'IG-P1', d.profit, NULL)) AS trades_ig_p1,

        -- Hit rate for D1 (total, last 7 days, last month)
        ROUND(SUM(IF(d.refID = 'IG-D1' AND d.profit > 0, 1, 0)) / COUNT(IF(d.refID = 'IG-D1', 1, NULL)) * 100, 2) AS hitrate_ig_d1,
        ROUND(SUM(IF(d.refID = 'IG-D1' AND d.exit_time>=subdate(current_date(),INTERVAL 7 day) AND d.profit > 0, 1, 0)) / COUNT(IF(d.refID = 'IG-D1' AND d.exit_time>=subdate(current_date(),INTERVAL 7 day), 1, NULL)) * 100, 2) AS hitrate_ig_d1_last7d,
        ROUND(SUM(IF(d.refID = 'IG-D1' AND d.exit_time>=subdate(current_date(),INTERVAL 1 month) AND d.profit > 0, 1, 0)) / COUNT(IF(d.refID = 'IG-D1' AND d.exit_time>=subdate(current_date(),INTERVAL 1 month), 1, NULL)) * 100, 2) AS hitrate_ig_d1_last1m,
        
        -- Hit rate for P1 (total, last 7 days, last month)
        ROUND(SUM(IF((d.refID = 'IG-P1') AND d.profit > 0, 1, 0)) / COUNT(IF(d.refID = 'IG-P1', 1, NULL)) * 100, 2) AS hitrate_ig_p1,
        ROUND(SUM(IF((d.refID = 'IG-P1') AND d.exit_time>=subdate(current_date(),INTERVAL 7 day) AND d.profit > 0, 1, 0)) / COUNT(IF((d.refID = 'IG-P1' ) AND d.exit_time>=subdate(current_date(),INTERVAL 7 day), 1, NULL)) * 100, 2) AS hitrate_ig_p1_last7d,
        ROUND(SUM(IF((d.refID = 'IG-P1') AND d.exit_time>=subdate(current_date(),INTERVAL 1 month) AND d.profit > 0, 1, 0)) / COUNT(IF((d.refID = 'IG-P1' ) AND d.exit_time>=subdate(current_date(),INTERVAL 1 month), 1, NULL)) * 100, 2) AS hitrate_ig_p1_last1m,
        
        MIN(d.exit_time) AS first_action,
        MAX(d.exit_time) AS last_action,
        st.ID AS StrategyToggleID,
        st.activate_ig_d1,
        st.activate_ig_d1_proposal,
        st.activate_ig_p1,
        st.activate_error_replay_p,
        st.activate_high_volume,
        st.activate_high_volume_proposal,
        st.activate_break_even_stop,
        st.activate_trailing_stops,
        st.activate_initial_stop_multiple,
        st.inactive,
        s.preCalculated_marginTradeMinLotSize,
        s.preCalculated_marginTradeDateTime,
        s.lotMin,
        s.lotMax,
        s.lotStep,
        s.leverage,
        ss.minimum_volume_m15, ss.defaulthighchancevolume, ss.defaultmidchancevolume, ss.stoppOffset, ss.stoppOffsetmidchancevolume, ss.trailstoppOffset,
        b.backtest_snapshot_time,
        b.backtest_trades,
        b.backtest_hitrate,
        b.backtest_profit_factor,
        b.backtest_netwin_percentage, 
        b.backtest_maxdrawdown_percentage,
        b.backtest_sharpeRatio
    FROM \`strategy_toggle\` AS st
    LEFT JOIN \`trades_history\` AS d ON d.strategy = st.strategy and d.symbol=st.symbol and d.timeframe=st.timeframe AND d.symbol = st.symbol AND d.strategy = st.strategy and d.timeframe = st.timeframe
        AND d.\`exit_time\` >= ?
        ${p_useStatsWithLastStrategyChance === 'after' ? 'AND d.\`exit_time\` >= st.strategy_updated' :
                    p_useStatsWithLastStrategyChance === 'before' ? 'AND (d.\`exit_time\` < st.strategy_updated OR st.strategy_updated IS NULL)' : ''}
        AND d.refID IN ('IG-D1', 'IG-P1')
    LEFT JOIN \`symbol_setups\` AS ss ON ss.symbol = st.symbol AND ss.timeframe = st.timeframe and ss.cmd= d.cmd
    LEFT JOIN \`symbols\` AS s ON s.symbol = st.symbol
    LEFT JOIN \`strategy_backtests\` AS b ON st.strategy = b.strategy AND st.timeframe = b.timeframe AND st.symbol = b.symbol \
    WHERE
        ${whereInactive}
        ${p_only_d1_activated ? ' st.activate_ig_d1 <> 0 AND ' : ''}
        st.strategy NOT IN ("break-even-stop","trailing-stop-activate","[s/l]","vstop") AND st.strategy <> ""
    GROUP BY st.symbol, st.timeframe, st.strategy, st.activate_ig_p1, st.activate_ig_d1, st.activate_high_volume, ss.minimum_volume_m15
    ORDER BY profit_ig_d1 desc, st.strategy ASC, st.symbol ASC, st.timeframe ASC;`,
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        if (process.env.NODE_ENV !== 'production')
            console.log("[db_get_strategies][getStrategyActivationReportAggregated] Query: startdate: ", p_startdate, ", weeks: ", p_weeks, ", months:", p_months, ", p_useStatsWithLastStrategyChance", p_useStatsWithLastStrategyChance, "p_only_d1_activated:", p_only_d1_activated, "p_minimize_analyze:", p_minimize_analyze, "ignoreInActiveState:", p_ignore_inactive_state);
        const result = await db.pool.query(p_query, [p_startdate]);
        if (process.env.NODE_ENV !== 'production')
            console.log("[db_get_strategies][getStrategyActivationReportAggregated] Result set size:", result.length);

        if (p_minimize_analyze) {
            const minimizedResult = result.map(row => ({
                symbol: row.symbol,
                timeframe: row.timeframe,
                strategy: row.strategy,
                profit_ig_d1_long: row.profit_ig_d1_long,
                profit_ig_d1_short: row.profit_ig_d1_short,
                backtest_snapshot_time: row.backtest_snapshot_time,
                backtest_trades: row.backtest_trades,
                backtest_hitrate: row.backtest_hitrate,
                backtest_profit_factor: row.backtest_profit_factor,
                backtest_netwin_percentage: row.backtest_netwin_percentage,
                backtest_maxdrawdown_percentage: row.backtest_maxdrawdown_percentage,
                backtest_sharpeRatio: row.backtest_sharpeRatio
            }));
            res.send(minimizedResult);
        } else {
            res.send(result);
        }
    } catch (err) {
        throw err;
    }
};

exports.getStrategyReportForRefID = async (req, res) => {
    try {
        var p_refID = req.query.refID;
        var p_startdate = req.query.startdate;
        var p_weeks = req.query.weeks;
        if (p_weeks != undefined) {
            p_startdate = moment().subtract(p_weeks, 'weeks').format('YYYY-MM-DD');
            console.log("[db_get_strategies][getStrategyReportForRefID] Start date:", p_startdate);
        }
        var p_months = req.query.months;
        if (p_months != undefined) {
            p_startdate = moment().subtract(p_months, 'months').format('YYYY-MM-DD');
            console.log("[db_get_strategies][getStrategyReportForRefID] Start date:", p_startdate);
        }
        var p_group_by_day = req.query.group_by_day;
        if (p_group_by_day == undefined || p_group_by_day != "true") {
            p_group_by_day = '';
            console.log("[db_get_strategies][getStrategyReportForRefID] Not using group_by_day");
        } else {
            p_group_by_day = ', exit_time';
            console.log("[db_get_strategies][getStrategyReportForRefID] Using group_by_day");
        }

        const p_query = {
            sql: `WITH cte AS (SELECT type,
                                      norm_profit,
                                      COMMENT     as defaultcomment,
                                      v.symbol    as symbol,
                                      v.strategy  as strategy,
                                      v.timeframe as timeframe,
                                      v.exit_time
                               FROM strategy_results_view v,
                                    \`strategy_toggle\` tog
                               WHERE TYPE = ?
                                 AND v.symbol = tog.symbol
                                 AND v.timeframe = tog.timeframe
                                 AND v.strategy = tog.strategy
                                 AND exit_time >= ?),
                       perc AS (SELECT cte.type,
                                       cte.strategy,
                                       cte.timeframe,
                                       cte.symbol,
                                       round(PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY norm_profit) OVER (PARTITION BY type, cte.strategy, cte.timeframe),0) AS q1_profit,
                                       round(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY norm_profit) OVER (PARTITION BY type, cte.strategy, cte.timeframe), 0) AS median_profit,
                                       round(PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY norm_profit) OVER (PARTITION BY type, cte.strategy, cte.timeframe), 0) AS q3_profit
                                FROM cte group by type, strategy, timeframe, symbol)
                  SELECT cte.type,
                         cte.defaultcomment,
                         cte.strategy,
                         cte.symbol,
                         perc.q1_profit,
                         perc.median_profit,
                         perc.q3_profit,
                         cte.timeframe,
                         round(avg(norm_profit), 0) AS avg_profit,
                         round(min(norm_profit), 0) AS min_profit,
                         round(max(norm_profit), 0) AS max_profit,
                         SUM(norm_profit)           as sum_profit,
                         COUNT(*)                   AS cnt_trades,
                         MIN(cte.exit_time)         as first_action_time,
                         t.activate_ig_p1,
                         t.activate_ig_d1,
                         t.activate_ig_d1_proposal, 
                         t.activate_high_volume, 
                         t.activate_high_volume_proposal
                  FROM cte,
                       \`strategy_toggle\` t,
                       perc
                  WHERE cte.symbol = t.symbol
                    AND cte.timeframe = t.timeframe
                    AND cte.strategy = t.strategy
                    AND cte.symbol = perc.symbol
                    and cte.timeframe = perc.timeframe
                    AND cte.strategy = perc.strategy
                  GROUP BY TYPE, strategy`,
            bigIntAsNumber: true
        }
        console.log("[db_get_strategies][getStrategyReportForRefID] Query:", p_refID, p_startdate, p_startdate);
        const result = await db.pool.query(p_query, [p_refID, p_startdate]);
        res.send(result);
    } catch (err) {
        throw err;
    }
};

exports.getStrategyLastEvents = async (req, res) => {
    try {
        const p_refID = req.query.refID || 'IG-D1';
        const p_query = {
            sql: `SELECT t.symbol,
                         t.timeframe,
                         t.strategy,
                         t.id,
                         t.trade_volume,
                         t.\`create\`,
                         t.cmd,
                         t.stopploss,
                         t.price,
                         st.activate_ig_d1,
                         st.activate_high_volume,
                         st.activate_ig_p1
                  FROM trades t
                           JOIN (SELECT symbol, timeframe, strategy, MAX(id) AS max_id
                                 FROM trades
                                 WHERE refid = ?
                                   AND strategy <> ''
                                   AND timeframe >= 15
                                   AND MODE NOT LIKE 'E:%' 
                                 GROUP BY symbol, timeframe, strategy) m ON t.symbol = m.symbol
                      AND t.timeframe = m.timeframe
                      AND t.strategy = m.strategy
                      AND t.id = m.max_id
                           JOIN (SELECT *
                                 FROM \`strategy_toggle\` st) st ON st.strategy = t.strategy
                      AND st.timeframe = t.timeframe
                      AND st.symbol = t.symbol
                  WHERE t.refid = ?
                    AND t.strategy <> ''
                    AND t.timeframe >= 15
                    AND t.cmd <> ''
                    AND t.strategy <> 'vstop'
                    AND st.inactive = 0
                    AND t.MODE NOT LIKE 'E:%'
                  order by st.activate_ig_p1 desc, st.activate_ig_d1 desc, t.\`create\` desc`,
            values: [p_refID, p_refID],
            bigIntAsNumber: true,
            timezone: 'de_de'
        };

        const result = await db.pool.query(p_query);
        res.send(result);
    } catch (err) {
        console.error('[getStrategyLastEvents] Error:', err);
        res.status(500).send(err);
    }
};
