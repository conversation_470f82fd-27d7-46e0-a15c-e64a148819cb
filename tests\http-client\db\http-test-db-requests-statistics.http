###

// @name statistics/day
GET {{API_BASE_URL}}/api/v1/db/statistics/day?refID=IG-P1&startdate=2025-01-01
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name statistics/week
GET {{API_BASE_URL}}/api/v1/db/statistics/week?refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name statistics/week
GET {{API_BASE_URL}}/api/v1/db/statistics/month?refID=IG-D1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name statistics/current_day
GET {{API_BASE_URL}}/api/v1/db/statistics/current_day?refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name statistics/current_week
GET {{API_BASE_URL}}/api/v1/db/statistics/current_week?refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name statistics/current_month
GET {{API_BASE_URL}}/api/v1/db/statistics/current_month?refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name statistics/current_year
GET {{API_BASE_URL}}/api/v1/db/statistics/current_year?refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name statistics/weekday
GET {{API_BASE_URL}}/api/v1/db/statistics/weekday?refID=IG-P1&weeks=6
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name statistics/dayhour
GET {{API_BASE_URL}}/api/v1/db/statistics/dayhour?refID=IG-P1&weeks=6
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name strategy_symbols
GET {{API_BASE_URL}}/api/v1/db/statistics/strategy_symbols?refID=IG-P1&months=2
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name symbols_strategy
GET {{API_BASE_URL}}/api/v1/db/statistics/symbols_strategy?refID=IG-P1&weeks=1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

