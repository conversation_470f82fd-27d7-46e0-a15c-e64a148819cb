### Microsoft ToDo API Integration Tests
### These tests require valid Microsoft Graph API credentials in environment variables

@test-list-id = AQMkADAwATM0MDAAMS1iNTcyLWI2NjEtMDACLTAwCgAuAAADiQ8RE6fqakSzjqWM_1OpPAEA
@basic-task-title = Test Task from API
@detailed-task-title = Detailed Trading Alert Task
@trading-alert-title = EURUSD Trading Signal Alert
@risk-mgmt-title = Risk Management Review

### Create Basic Task
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "listId": "{{test-list-id}}",
    "title": "{{basic-task-title}}",
    "body": "This is a basic test task created via the API integration.",
    "importance": "normal"
}

> {%
    client.test("Basic Task Creation successful", function() {
        client.assert(response.status === 201, "Response status is not 201");
        client.assert(response.body.success === true, "Response success is not true");
        client.assert(response.body.data.hasOwnProperty('id'), "Missing task ID");
        client.assert(response.body.data.title === client.global.get("basic-task-title"), "Title mismatch");
    });

    client.test("Response contains required metadata", function() {
        client.assert(response.body.hasOwnProperty('meta'), "Missing meta object");
        client.assert(response.body.meta.hasOwnProperty('requestId'), "Missing request ID");
        client.assert(response.body.meta.hasOwnProperty('processingTime'), "Missing processing time");
        client.assert(response.body.meta.hasOwnProperty('timestamp'), "Missing timestamp");
    });

    client.test("Task data structure is correct", function() {
        const task = response.body.data;
        client.assert(task.hasOwnProperty('status'), "Missing task status");
        client.assert(task.hasOwnProperty('importance'), "Missing task importance");
        client.assert(task.hasOwnProperty('createdDateTime'), "Missing creation date");
        client.assert(task.hasOwnProperty('webUrl'), "Missing web URL");
    });
%}

### Create Detailed Task with Due Date and Categories
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "listId": "{{test-list-id}}",
    "title": "{{detailed-task-title}}",
    "body": "This task includes a due date, high importance, and multiple categories for comprehensive testing.",
    "dueDateTime": "2025-01-15T14:00:00.000Z",
    "importance": "high",
    "categories": ["API Testing", "Integration", "High Priority"]
}

> {%
    client.test("Detailed Task Creation successful", function() {
        client.assert(response.status === 201, "Response status is not 201");
        client.assert(response.body.success === true, "Response success is not true");
        client.assert(response.body.data.importance === "high", "Importance not set correctly");
    });

    client.test("Due date and categories are set correctly", function() {
        const task = response.body.data;
        client.assert(task.hasOwnProperty('dueDateTime'), "Missing due date");
        client.assert(task.hasOwnProperty('categories'), "Missing categories");
        client.assert(Array.isArray(task.categories), "Categories is not an array");
        client.assert(task.categories.length === 3, "Incorrect number of categories");
    });
%}

### Create Trading Alert Task
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "listId": "{{test-list-id}}",
    "title": "{{trading-alert-title}}",
    "body": "EURUSD has reached resistance level at 1.0850. Consider taking profit on long positions. Stop loss recommended at 1.0820. Market conditions: High volatility expected due to ECB announcement.",
    "dueDateTime": "2025-01-02T16:30:00.000Z",
    "importance": "high",
    "categories": ["Trading", "EURUSD", "Alert", "Take Profit"]
}

> {%
    client.test("Trading Alert Task Creation successful", function() {
        client.assert(response.status === 201, "Response status is not 201");
        client.assert(response.body.data.title === client.global.get("trading-alert-title"), "Trading alert title mismatch");
    });

    client.test("Trading task has correct urgency", function() {
        client.assert(response.body.data.importance === "high", "Trading alert should have high importance");
        client.assert(response.body.data.categories.includes("Trading"), "Missing Trading category");
        client.assert(response.body.data.categories.includes("Alert"), "Missing Alert category");
    });
%}

### Create Risk Management Task
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "listId": "{{test-list-id}}",
    "title": "{{risk-mgmt-title}}",
    "body": "Weekly risk management review: Analyze portfolio exposure, check position sizes, review stop losses, and assess correlation risks across all active trades.",
    "dueDateTime": "2025-01-03T09:00:00.000Z",
    "importance": "normal",
    "categories": ["Risk Management", "Weekly Review", "Portfolio"]
}

> {%
    client.test("Risk Management Task Creation successful", function() {
        client.assert(response.status === 201, "Response status is not 201");
        client.assert(response.body.data.categories.includes("Risk Management"), "Missing Risk Management category");
    });
%}

### Test Input Validation - Missing Title
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "listId": "{{test-list-id}}",
    "body": "Task without title should fail validation"
}

> {%
    client.test("Missing Title Validation", function() {
        client.assert(response.status === 400, "Response status should be 400 for missing title");
        client.assert(response.body.success === false, "Response success should be false");
        client.assert(response.body.error.includes("title"), "Error message should mention title");
    });
%}

### Test Input Validation - Missing List ID
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "title": "Task without list ID",
    "body": "This should fail validation"
}

> {%
    client.test("Missing List ID Validation", function() {
        client.assert(response.status === 400, "Response status should be 400 for missing listId");
        client.assert(response.body.success === false, "Response success should be false");
        client.assert(response.body.error.includes("listId"), "Error message should mention listId");
    });
%}

### Test Input Validation - Invalid Importance
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "listId": "{{test-list-id}}",
    "title": "Task with invalid importance",
    "importance": "invalid_value"
}

> {%
    client.test("Invalid Importance Validation", function() {
        client.assert(response.status === 400, "Response status should be 400 for invalid importance");
        client.assert(response.body.error.includes("importance"), "Error message should mention importance");
    });
%}

### Test Input Validation - Invalid Due Date Format
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "listId": "{{test-list-id}}",
    "title": "Task with invalid due date",
    "dueDateTime": "invalid-date-format"
}

> {%
    client.test("Invalid Due Date Validation", function() {
        client.assert(response.status === 400, "Response status should be 400 for invalid date");
        client.assert(response.body.error.includes("dueDateTime"), "Error message should mention dueDateTime");
    });
%}

### Test Input Validation - Title Too Long
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "listId": "{{test-list-id}}",
    "title": "This is an extremely long title that exceeds the maximum allowed length of 255 characters. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."
}

> {%
    client.test("Title Too Long Validation", function() {
        client.assert(response.status === 400, "Response status should be 400 for title too long");
        client.assert(response.body.error.includes("255"), "Error message should mention character limit");
    });
%}

### Test Input Validation - Too Many Categories
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "listId": "{{test-list-id}}",
    "title": "Task with too many categories",
    "categories": ["Cat1", "Cat2", "Cat3", "Cat4", "Cat5", "Cat6", "Cat7", "Cat8", "Cat9", "Cat10", "Cat11", "Cat12", "Cat13", "Cat14", "Cat15", "Cat16", "Cat17", "Cat18", "Cat19", "Cat20", "Cat21", "Cat22", "Cat23", "Cat24", "Cat25", "Cat26"]
}

> {%
    client.test("Too Many Categories Validation", function() {
        client.assert(response.status === 400, "Response status should be 400 for too many categories");
        client.assert(response.body.error.includes("25"), "Error message should mention category limit");
    });
%}

### Test Error Handling - Non-existent List ID
POST {{API_BASE_URL}}/api/v1/microsoft/todo/tasks
Content-Type: application/json

{
    "listId": "non-existent-list-id-12345",
    "title": "Task for non-existent list",
    "body": "This should return a 404 error"
}

> {%
    client.test("Non-existent List ID Error Handling", function() {
        client.assert(response.status === 404, "Response status should be 404 for non-existent list");
        client.assert(response.body.success === false, "Response success should be false");
        client.assert(response.body.message.includes("not found"), "Error message should indicate resource not found");
    });
%}
