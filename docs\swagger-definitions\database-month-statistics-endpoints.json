{"paths": {"/api/v1/db/statistics/month": {"get": {"summary": "Monatsstatistiken abrufen", "description": "Ruft Handelsstatistiken auf Monatsbasis für ein bestimmtes Konto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Gruppiert nach Jahr und Monat\n- Enthält Min/Max-Gewinn, Anzahl der Trades und Gesamtgewinn pro Monat\n- Standardmäßig werden die letzten 3 Monate abgerufen\n\nAnwendungsfälle:\n- Anzeige von monatlichen Performance-Metriken im Dashboard\n- Analyse von Handelsmustern über verschiedene Monate\n- Identifizierung von saisonalen Trends\n- Berechnung von monatlichen Durchschnittswerten für Performance-Berichte", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Monatsstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MonthStatistics"}}, "example": [{"year": 2023, "month": 4, "min_profit": -350, "max_profit": 780, "cnt_trades": 120, "sum_profit": 2350}, {"year": 2023, "month": 3, "min_profit": -280, "max_profit": 650, "cnt_trades": 105, "sum_profit": 1980}, {"year": 2023, "month": 2, "min_profit": -320, "max_profit": 720, "cnt_trades": 98, "sum_profit": 2120}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/statistics/current_month": {"get": {"summary": "Statistiken des aktuellen Monats abrufen", "description": "Ruft Handelsstatistiken für den aktuellen Monat eines bestimmten Kontos ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält nur Daten vom aktuellen Monat\n- Enthält Min/Max-Gewinn, Anzahl der Trades und Gesamtgewinn\n- Filtert automatisch nach dem aktuellen Jahr und Monat\n\nAnwendungsfälle:\n- Echtzeit-Überwachung der aktuellen Monatsperformance\n- Dashboard-Anzeige für den laufenden Monat\n- Vergleich mit Zielvorgaben für den aktuellen Monat\n- Schnelle Überprüfung des Monatsergebnisses", "tags": ["Statistics"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der aktuellen Monatsstatistiken", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrentMonthStatistics"}}, "example": [{"year": 2023, "month": 4, "min_profit": -350, "max_profit": 780, "cnt_trades": 120, "sum_profit": 2350}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}