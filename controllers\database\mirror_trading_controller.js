const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { TIME_CONSTANTS, QUERY_LIMITS } = require('../../configs/constants');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam, validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');
const { buildMirrorTradingSettingsQuery, buildMirrorTradingLogsQuery } = require('./queries/mirror_trading_queries');

async function getMirrorTradingSettingsIndependent(targetRefID, sourceRefID) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getMirrorTradingSettingsIndependent', 'Starting mirror trading settings retrieval', {
            targetRefID,
            sourceRefID
        });

        // Validate targetRefID
        targetRefID = validateStringParam(targetRefID, {
            required: true,
            minLength: 1,
            maxLength: 50,
            paramName: 'targetRefID'
        });

        // Validate sourceRefID
        sourceRefID = validateStringParam(sourceRefID, {
            required: true,
            minLength: 1,
            maxLength: 50,
            paramName: 'sourceRefID'
        });

        log(LOG_LEVELS.DEBUG, 'getMirrorTradingSettingsIndependent', 'Executing query', {
            targetRefID,
            sourceRefID
        });

        const query = buildMirrorTradingSettingsQuery();
        const result = await executeQuery(query, [sourceRefID, targetRefID]);
        
        if (!result) {
            throw new DatabaseError('Failed to fetch mirror trading settings', { targetRefID, sourceRefID });
        }

        log(LOG_LEVELS.INFO, 'getMirrorTradingSettingsIndependent', 'Successfully retrieved mirror trading settings', {
            targetRefID,
            sourceRefID,
            resultCount: result?result.length:0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getMirrorTradingSettingsIndependent', 'Failed to fetch mirror trading settings', {
            error: err.message,
            stack: err.stack,
            targetRefID,
            sourceRefID
        });
        throw err;
    } finally {
        logPerformance('getMirrorTradingSettingsIndependent', startTime);
    }
}

/**
 * API-Endpunkt zum Abrufen von Mirror-Trading-Einstellungen
 */
async function getMirrorTradingSettings(req, res) {
    try {
        const result = await withCacheWrapper(
            'GENERAL',
            'getMirrorTradingSettings',
            () => getMirrorTradingSettingsIndependent(req.query.targetRefID, req.query.sourceRefID),
            [req.query.targetRefID, req.query.sourceRefID]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getMirrorTradingSettings');
        res.status(errorResponse.status).json(errorResponse);
    }
}

async function getMirrorTradingLogsIndependent(targetRefID, sourceRefID, limit = 10) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getMirrorTradingLogsIndependent', 'Starting mirror trading logs retrieval', {
            targetRefID,
            sourceRefID,
            limit
        });

        // Validate targetRefID
        targetRefID = validateStringParam(targetRefID, {
            required: true,
            minLength: 1,
            maxLength: 50,
            paramName: 'targetRefID'
        });

        // Validate sourceRefID
        sourceRefID = validateStringParam(sourceRefID, {
            required: true,
            minLength: 1,
            maxLength: 50,
            paramName: 'sourceRefID'
        });

        // Validate limit
        limit = validateNumericParam(limit, {
            defaultValue: 10,
            min: 1,
            max: 1000,
            paramName: 'limit'
        });

        log(LOG_LEVELS.DEBUG, 'getMirrorTradingLogsIndependent', 'Executing query', {
            targetRefID,
            sourceRefID,
            limit
        });

        const query = buildMirrorTradingLogsQuery();
        const result = await executeQuery(query, [sourceRefID, targetRefID, limit]);
        
        log(LOG_LEVELS.INFO, 'getMirrorTradingLogsIndependent', 'Successfully retrieved mirror trading logs', {
            targetRefID,
            sourceRefID,
            limit,
            resultCount: result?result.length:0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getMirrorTradingLogsIndependent', 'Failed to fetch mirror trading logs', {
            error: err.message,
            stack: err.stack,
            targetRefID,
            sourceRefID,
            limit
        });
        throw err;
    } finally {
        logPerformance('getMirrorTradingLogsIndependent', startTime);
    }
}

async function getMirrorTradingLogs(req, res) {
    try {
        const limit = req.query.limit === undefined ? 10 : parseInt(req.query.limit);

        const result = await withCacheWrapper(
            'GENERAL',
            'getMirrorTradingLogs',
            () => getMirrorTradingLogsIndependent(req.query.targetRefID, req.query.sourceRefID, limit),
            [req.query.targetRefID, req.query.sourceRefID, limit]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getMirrorTradingLogs');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getMirrorTradingSettings,
    getMirrorTradingSettingsIndependent,
    getMirrorTradingLogs,
    getMirrorTradingLogsIndependent
};
