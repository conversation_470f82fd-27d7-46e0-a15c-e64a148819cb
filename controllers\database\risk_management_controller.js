
const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON>acheWrapper } = require('../../services/cache_service');
const { log, LOG_LEVELS } = require('../../services/logging_service');
const { executeQuery } = require('../../services/database_service');

const VALID_RISK_MODES = ['Low-Risk', 'Standard', 'High-Risk'];
const VALID_MARKET_SITUATIONS = ['Short', 'Neutral-Wait', 'Long'];

async function getRiskManagementStateIndependent(epic = null) {
    try {
        const baseQuery = `
            SELECT sv.* 
            FROM (
                SELECT timestamp, epic as symbol, risk_on_mode, market_situation, reasoning, 
                       vola_15, vola_30, vola_60,
                       ROW_NUMBER() OVER (PARTITION BY epic ORDER BY timestamp DESC) as rn
                FROM strategy_riskmanagement_with_vola
                ${epic ? 'WHERE epic = ?' : 'WHERE timestamp > DATE_SUB(CURDATE(),INTERVAL 5 day) '}
            ) sv
            WHERE sv.rn = 1`;

        const queryParams = epic ? [epic] : [];

        log(LOG_LEVELS.DEBUG, 'getRiskManagementStateIndependent', 'Executing query', {
            query: baseQuery,
            params: queryParams
        });

        const result = await executeQuery(baseQuery, queryParams);

        log(LOG_LEVELS.INFO, 'getRiskManagementStateIndependent', 'Successfully retrieved data', {
            resultCount: result.length
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getRiskManagementStateIndependent', 'Failed to fetch risk management state', {
            error: err.message,
            stack: err.stack
        });
        throw new DatabaseError('Failed to fetch risk management state', err);
    }
}

async function getRiskManagementState(req, res) {
    try {
        const symbol = req.query.symbol;
        const result = await withCacheWrapper(
            'GENERAL',
            'getRiskManagementState',
            () => getRiskManagementStateIndependent(symbol),
            [symbol],
            1 // 1 second TTL
        );

        res.json(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getRiskManagementState');
        res.status(errorResponse.status).json(errorResponse);
    }
}

async function updateRiskOnMode(req, res) {
    log(LOG_LEVELS.DEBUG, 'updateRiskOnMode', 'Executing update', req.params);
    const { symbol: symbol } = req.params;
    const { mode } = req.query;

    try {
        if (!symbol) {
            throw new ValidationError('symbol is required: ' + req.params.symbol);
        }

        if (!mode || !VALID_RISK_MODES.includes(mode)) {
            throw new ValidationError('Invalid risk_on_mode value', {
                allowedValues: VALID_RISK_MODES,
                receivedValue: mode
            });
        }

        // First get existing values
        const getCurrentValues = `
            SELECT risk_on_mode, market_situation, reasoning 
            FROM strategy_riskmanagement 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 1`;

        const currentValues = await executeQuery(getCurrentValues, [symbol]);

        const query = `
            INSERT INTO strategy_riskmanagement (timestamp, symbol, risk_on_mode, market_situation, reasoning)
            VALUES (NOW(), ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
                risk_on_mode = VALUES(risk_on_mode),
                market_situation = VALUES(market_situation),
                reasoning = VALUES(reasoning),
                timestamp = VALUES(timestamp)`;

        log(LOG_LEVELS.DEBUG, 'updateRiskOnMode', 'Executing update', {
            symbol,
            mode,
            currentValues
        });

        // Use current values for fields that aren't being updated
        const market_situation = currentValues[0]?.market_situation || null;
        const reasoning = currentValues[0]?.reasoning || null;

        await executeQuery(query, [symbol, mode, market_situation, reasoning]);

        res.json({ status: 'success', message: `Risk mode updated to ${mode} for ${symbol}` });
    } catch (err) {
        const errorResponse = errorHandler(err, 'updateRiskOnMode');
        res.status(errorResponse.status).json(errorResponse);
    }
}

async function getRiskManagementStates(req, res) {
    const { symbol: symbol } = req.params;

    try {
        if (!symbol) {
            throw new ValidationError('symbol is required');
        }

        const query = `
            WITH RankedStates AS (
                SELECT 
                    timestamp,
                    symbol,
                    risk_on_mode,
                    market_situation,
                    reasoning,
                    LAG(risk_on_mode) OVER (ORDER BY timestamp) as prev_risk_mode,
                    LAG(market_situation) OVER (ORDER BY timestamp) as prev_market_situation
                FROM strategy_riskmanagement
                WHERE symbol = ?
                AND timestamp >= DATE_SUB(NOW(), INTERVAL 60 DAY)
                ORDER BY timestamp
            )
            SELECT 
                MIN(timestamp) as timestamp,
                symbol,
                risk_on_mode,
                market_situation,
                reasoning
            FROM RankedStates
            WHERE 
                risk_on_mode != prev_risk_mode 
                OR market_situation != prev_market_situation
                OR prev_risk_mode IS NULL
                OR prev_market_situation IS NULL
            GROUP BY symbol, risk_on_mode, market_situation, reasoning
            ORDER BY timestamp;`;

        log(LOG_LEVELS.DEBUG, 'getRiskManagementStates', 'Executing query', {
            symbol: symbol
        });

        const result = await executeQuery(query, [symbol]);

        log(LOG_LEVELS.INFO, 'getRiskManagementStates', 'Successfully retrieved states', {
            resultCount: result.length
        });

        res.json(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getRiskManagementStates');
        res.status(errorResponse.status).json(errorResponse);
    }
}

async function updateMarketSituation(req, res) {
    const { symbol: symbol } = req.params;
    const { situation } = req.query;

    try {
        if (!symbol) {
            throw new ValidationError('symbol is required');
        }

        if (!situation || !VALID_MARKET_SITUATIONS.includes(situation)) {
            throw new ValidationError('Invalid market_situation value', {
                allowedValues: VALID_MARKET_SITUATIONS,
                receivedValue: situation
            });
        }

        // First get existing values
        const getCurrentValues = `
            SELECT risk_on_mode, market_situation, reasoning 
            FROM strategy_riskmanagement 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 1`;

        const currentValues = await executeQuery(getCurrentValues, [symbol]);

        const query = `
            INSERT INTO strategy_riskmanagement (timestamp, symbol, risk_on_mode, market_situation, reasoning)
            VALUES (NOW(), ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
                risk_on_mode = VALUES(risk_on_mode),
                market_situation = VALUES(market_situation),
                reasoning = VALUES(reasoning),
                timestamp = VALUES(timestamp)`;

        log(LOG_LEVELS.DEBUG, 'updateMarketSituation', 'Executing update', {
            epic: symbol,
            situation,
            currentValues
        });

        // Use current values for fields that aren't being updated
        const risk_on_mode = currentValues[0]?.risk_on_mode || null;
        const reasoning = currentValues[0]?.reasoning || null;

        await executeQuery(query, [symbol, risk_on_mode, situation, reasoning]);

        res.json({ status: 'success', message: `Market situation updated to ${situation} for ${symbol}` });
    } catch (err) {
        const errorResponse = errorHandler(err, 'updateMarketSituation');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getRiskManagementState,
    getRiskManagementStates,
    updateRiskOnMode,
    updateMarketSituation
};
