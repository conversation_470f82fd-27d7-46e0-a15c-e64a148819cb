{"paths": {"/api/v1/db/account_settings": {"get": {"summary": "Kontoinformationen abrufen", "description": "Ruft detaillierte Einstellungen und Informationen für ein bestimmtes Konto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Validierung des refID-Parameters\n- Fehlerbehandlung für nicht gefundene Konten\n\nAnwendungsfälle:\n- An<PERSON><PERSON> von Kontoinformationen im Dashboard\n- Überprüfung des Kontostatus\n- Abfrage von Kontoeinstellungen für Trading-Entscheidungen", "tags": ["Account Management"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-<PERSON> des Kontos (z.B. 'IG-D1', 'IG-P1')", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Kontoeinstellungen", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountSettings"}}, "example": [{"account_id": 1, "refId": "IG-D1", "account_name": "Demo Account 1", "account_type": "DEMO", "account_currency": "EUR", "account_balance": 10000.0, "account_leverage": 30, "account_status": "ACTIVE"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/trading_mode": {"get": {"summary": "Trading-Modus abrufen", "description": "Ruft den aktuellen Trading-Modus für ein bestimmtes Konto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Validierung des refID-Parameters\n- Fehlerbehandlung für nicht gefundene Konten\n\nAnwendungsfälle:\n- Überprüfung, ob automatisches Trading aktiviert ist\n- Anzeige des Trading-Status im Dashboard\n- Entscheidungsgrundlage für Trading-Strategien", "tags": ["Account Management"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-<PERSON> des Kontos (z.B. 'IG-D1', 'IG-P1')", "example": "IG-D1"}], "responses": {"200": {"description": "Erfolgreiche Abfrage des Trading-Modus", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradingMode"}}, "example": [{"account_id": 1, "refId": "IG-D1", "trading_mode": "AUTOMATIC", "trading_mode_since": "2023-04-15T10:30:00Z", "trading_mode_reason": "User setting"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}