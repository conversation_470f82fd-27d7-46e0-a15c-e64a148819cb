{"components": {"schemas": {"MicrosoftTodoTaskCreate": {"type": "object", "required": ["title"], "properties": {"listId": {"type": "string", "description": "Microsoft ToDo list ID where the task will be created. Optional if useDefaultList is true.", "example": "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA==", "minLength": 1, "maxLength": 500}, "useDefaultList": {"type": "boolean", "description": "If true, creates the task in the default list (appears in 'My Day'). If false or not provided, listId is required.", "example": true, "default": true}, "title": {"type": "string", "description": "Task title (required)", "example": "Review quarterly trading reports", "minLength": 1, "maxLength": 255}, "body": {"type": "string", "description": "Task description or body content (optional)", "example": "Analyze Q4 trading performance, identify key trends, and prepare recommendations for Q1 strategy", "maxLength": 4000}, "dueDateTime": {"type": "string", "format": "date-time", "description": "Due date and time in ISO 8601 format (optional)", "example": "2025-01-15T14:00:00.000Z"}, "importance": {"type": "string", "enum": ["low", "normal", "high"], "description": "Task importance level", "example": "normal", "default": "normal"}, "categories": {"type": "array", "items": {"type": "string"}, "description": "Array of category tags for the task (optional)", "example": ["Trading", "Reports", "Analysis"], "maxItems": 25}, "timeZone": {"type": "string", "description": "Time zone for the due date (optional, defaults to UTC)", "example": "Europe/Berlin"}}}, "MicrosoftTodoTaskResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates if the operation was successful", "example": true}, "message": {"type": "string", "description": "Human-readable success message", "example": "Task created successfully"}, "data": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique task identifier from Microsoft Graph", "example": "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwBGAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAhHKQZHItDEOVCn8U3xuA2AABmQeVQAAAA=="}, "title": {"type": "string", "description": "Task title", "example": "Review quarterly trading reports"}, "body": {"type": "string", "nullable": true, "description": "Task description or body content", "example": "Analyze Q4 trading performance, identify key trends, and prepare recommendations for Q1 strategy"}, "status": {"type": "string", "enum": ["notStarted", "inProgress", "completed", "<PERSON><PERSON>n<PERSON><PERSON><PERSON>", "deferred"], "description": "Current task status", "example": "notStarted"}, "importance": {"type": "string", "enum": ["low", "normal", "high"], "description": "Task importance level", "example": "normal"}, "createdDateTime": {"type": "string", "format": "date-time", "description": "Task creation timestamp", "example": "2025-01-02T10:30:00.000Z"}, "lastModifiedDateTime": {"type": "string", "format": "date-time", "description": "Last modification timestamp", "example": "2025-01-02T10:30:00.000Z"}, "dueDateTime": {"type": "string", "format": "date-time", "nullable": true, "description": "Task due date and time", "example": "2025-01-15T14:00:00.000Z"}, "categories": {"type": "array", "items": {"type": "string"}, "description": "Task categories", "example": ["Trading", "Reports", "Analysis"]}, "webUrl": {"type": "string", "nullable": true, "description": "URL to view the task in Microsoft ToDo web interface", "example": "https://to-do.office.com/tasks/id/AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwBGAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAhHKQZHItDEOVCn8U3xuA2AABmQeVQAAAA=="}, "listId": {"type": "string", "description": "ID of the ToDo list containing this task", "example": "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA=="}, "listInfo": {"type": "object", "nullable": true, "description": "Information about the list where the task was created (only present when using default list)", "properties": {"id": {"type": "string", "description": "List ID", "example": "AQMkAGVjMzJmMWZjLTgyYjgtNGIwYi1hN2E3LTQxZjQzNTdkNDQ1MwAuAAADKvwNgAMNPE_zFNRJXVrU1wEAhHKQZHItDEOVCn8U3xuA2AABmQeVPwAAAA=="}, "displayName": {"type": "string", "description": "Human-readable list name", "example": "Tasks"}, "wellKnownListName": {"type": "string", "nullable": true, "description": "Microsoft's well-known list identifier", "example": "defaultList"}, "isDefaultList": {"type": "boolean", "description": "Whether this task was created in the default list", "example": true}}}}}, "meta": {"type": "object", "properties": {"requestId": {"type": "string", "description": "Unique request identifier for tracking", "example": "req_1704193800000_abc123"}, "processingTime": {"type": "number", "description": "Request processing time in milliseconds", "example": 1250}, "timestamp": {"type": "string", "format": "date-time", "description": "Response timestamp", "example": "2025-01-02T10:30:00.000Z"}}}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Always false for error responses", "example": false}, "message": {"type": "string", "description": "Human-readable error message", "example": "Invalid input data"}, "error": {"type": "string", "description": "Detailed error description", "example": "title is required and must be a non-empty string"}, "details": {"type": "object", "description": "Additional error details (optional)", "example": {}}, "meta": {"type": "object", "properties": {"requestId": {"type": "string", "description": "Unique request identifier for tracking", "example": "req_1704193800000_abc123"}, "processingTime": {"type": "number", "description": "Request processing time in milliseconds", "example": 45}, "timestamp": {"type": "string", "format": "date-time", "description": "Response timestamp", "example": "2025-01-02T10:30:00.000Z"}}}}}}, "securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key", "description": "API key for authentication. Required for all Microsoft integration endpoints."}}}}