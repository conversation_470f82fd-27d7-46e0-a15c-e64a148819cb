const FMPBaseService = require('./fmp_base_service');
const { LoggingService } = require('./logging_service');

class FMPNewsService extends FMPBaseService {
    constructor(logger) {
        super(logger || LoggingService.getInstance());
    }

    async getNews(size = 50, page = 0) {
        this.logger.info('Fetching FMP news', {
            size,
            page,
            correlationId: this.correlationId
        });

        try {
            const data = await this.makeRequest('v4/general_news', { page, size });
            
            // Handle both English and German response structures
            const newsItems = data || [];

            if (!Array.isArray(newsItems) || newsItems.length === 0) {
                this.logger.warn('No news items received', {
                    correlationId: this.correlationId
                });
                return [];
            }

            this.logger.info('News fetched successfully', {
                itemCount: newsItems.length,
                correlationId: this.correlationId
            });

            return newsItems;
        } catch (error) {
            this.logger.error('Failed to fetch news', {
                error: error.message,
                correlationId: this.correlationId
            });
            throw error;
        }
    }

}

module.exports = FMPNewsService;