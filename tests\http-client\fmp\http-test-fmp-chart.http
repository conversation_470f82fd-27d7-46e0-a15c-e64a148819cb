### @name chart-with-all-params
GET {{API_BASE_URL}}/api/v1/fmp/chart?timeframe=5min&symbol=AAPL&from=2023-08-10&to=2023-09-10&extended=false
Content-Type: application/json

> {%
    client.test("Intraday Chart Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Response status is not success");
        client.assert(response.body.data && Array.isArray(response.body.data), "Data is not an array");
    });

    client.test("Response contains chart data points with required fields", function() {
        if (response.body.data && response.body.data.length > 0) {
            const dataPoint = response.body.data[0];
            client.assert(dataPoint.hasOwnProperty('date'), "Missing date field");
            client.assert(dataPoint.hasOwnProperty('open'), "Missing open field");
            client.assert(dataPoint.hasOwnProperty('high'), "Missing high field");
            client.assert(dataPoint.hasOwnProperty('low'), "Missing low field");
            client.assert(dataPoint.hasOwnProperty('close'), "Missing close field");
            client.assert(dataPoint.hasOwnProperty('volume'), "Missing volume field");
        }
    });

    client.test("Response contains correct parameters", function() {
        client.assert(response.body.params && response.body.params.timeframe === "5min", "Incorrect timeframe parameter");
        client.assert(response.body.params && response.body.params.symbol === "AAPL", "Incorrect symbol parameter");
        client.assert(response.body.params && response.body.params.from === "2023-08-10", "Incorrect from parameter");
        client.assert(response.body.params && response.body.params.to === "2023-09-10", "Incorrect to parameter");
        client.assert(response.body.params && response.body.params.extended === false, "Incorrect extended parameter");
    });
%}

### @name chart-minimum-params
GET {{API_BASE_URL}}/api/v1/fmp/chart?timeframe=15min&symbol=MSFT
Content-Type: application/json

> {%
    client.test("Intraday Chart Request with minimum params executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Response status is not success");
    });
%}

### @name chart-different-timeframes
# Test with 1 hour timeframe
GET {{API_BASE_URL}}/api/v1/fmp/chart?timeframe=1hour&symbol=GOOGL
Content-Type: application/json

> {%
    client.test("Intraday Chart with 1hour timeframe executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

### @name chart-with-extended-hours
GET {{API_BASE_URL}}/api/v1/fmp/chart?timeframe=30min&symbol=AMZN&extended=true
Content-Type: application/json

> {%
    client.test("Intraday Chart with extended hours executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.params && response.body.params.extended === true, "Extended parameter not set to true");
    });
%}

### @name chart-invalid-timeframe
GET {{API_BASE_URL}}/api/v1/fmp/chart?timeframe=invalid&symbol=TSLA
Content-Type: application/json

> {%
    client.test("Intraday Chart with invalid timeframe should return 400", function() {
        client.assert(response.status === 400, "Response status is not 400");
        client.assert(response.body.status === "error", "Response status is not error");
    });
%}

### @name chart-missing-required-params
GET {{API_BASE_URL}}/api/v1/fmp/chart?timeframe=5min
Content-Type: application/json

> {%
    client.test("Intraday Chart without required symbol should return 400", function() {
        client.assert(response.status === 400, "Response status is not 400");
        client.assert(response.body.status === "error", "Response status is not error");
    });
%}

### @name chart-index
GET {{API_BASE_URL}}/api/v1/fmp/chart?timeframe=5min&symbol=^SPX&from=2023-08-10&to=2023-09-10
Content-Type: application/json

> {%
    client.test("Intraday Chart for index executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}