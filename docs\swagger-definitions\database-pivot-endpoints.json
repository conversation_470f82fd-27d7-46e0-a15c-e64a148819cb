{"paths": {"/api/v1/db/pivotpoints": {"get": {"summary": "Pivot-Punkte abrufen", "description": "Ruft Pivot-Punkte für verschiedene Trading-Symbole ab. Pivot-Punkte sind wichtige Preisniveaus, die als potenzielle Unterstützungs- und Widerstandsbereiche dienen.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält Standard-Pivot-Punkte (Pivot, R1-R3, S1-S3)\n- Berechnet aus historischen Hoch-, Tief- und Schlusskursen\n- Filterbar nach Zeitraum in Tagen\n\nAnwendungsfälle:\n- Identifizierung von wichtigen Unterstützungs- und Widerstandsniveaus\n- Integration in Trading-Strategien für Ein- und Ausstiegspunkte\n- Technische Analyse und Preisvorhersage\n- Risikomanagement durch Setzen von Stop-Loss und Take-Profit-Levels an Pivot-Punkten", "tags": ["Technical Analysis"], "parameters": [{"in": "query", "name": "days", "required": false, "schema": {"type": "integer", "default": 3000, "minimum": 1, "maximum": 3650}, "description": "<PERSON>zahl der Tage in der Vergangenheit, für die Pivot-Punkte abgerufen werden sollen", "example": 3000}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Pivot-Punkte", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PivotPoint"}}, "example": [{"pivot_id": 12345, "symbol": "EURUSD", "date": "2023-04-15", "timeframe": "D1", "pivot": 1.0865, "r1": 1.0892, "r2": 1.0918, "r3": 1.0945, "s1": 1.0838, "s2": 1.0812, "s3": 1.0785, "high": 1.0925, "low": 1.0805, "close": 1.0865, "calculation_method": "Standard"}, {"pivot_id": 12346, "symbol": "GBPUSD", "date": "2023-04-15", "timeframe": "D1", "pivot": 1.245, "r1": 1.2475, "r2": 1.25, "r3": 1.2525, "s1": 1.2425, "s2": 1.24, "s3": 1.2375, "high": 1.251, "low": 1.239, "close": 1.245, "calculation_method": "Standard"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}