// @name symbol_setups
GET {{API_BASE_URL}}/api/v1/db/symbol_setups
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}
###
// @name symbol_setup
GET {{API_BASE_URL}}/api/v1/db/symbol_setup/IX.D.NASDAQ.IFE.IP?timeframe=15&cmd=0
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

HTTP/1.1 200  - OK
x-powered-by: Express, PleskLin
date: Sat, 08 Feb 2025 15:16:59 GMT
server: Apache
vary: Origin, Accept-Encoding
access-control-allow-credentials: true
content-type: application/json; charset=utf-8
etag: W/"609-gksS7WMJEGvSznM9u9W6I+lZzmU"
connection: close
content-encoding: gzip
transfer-encoding: chunked
###

// @name symbol_setup
GET {{API_BASE_URL}}/api/v1/db/symbol_setup/IX.D.NASDAQ.IFE.IP
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}