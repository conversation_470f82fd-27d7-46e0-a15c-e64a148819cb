# Microsoft ToDo "Create Task" Feature - Planungsgrundlage

## Übersicht

Dieses Dokument beschreibt die Implementierung eines neuen API-Endpunkts für die Integration mit Microsoft ToDo, um Tasks zu erstellen. Die Implementierung folgt den bestehenden Architekturmustern der Algotrader-API.

## Architektur-Analyse

### Bestehende Patterns
- **Service Layer Pattern**: Externe APIs werden über dedizierte Service-Klassen abstrahiert (z.B. `FMPBaseService`, `IGApiClient`)
- **Controller-Service-Trennung**: Controller handhaben HTTP-Requests, Services die Geschäftslogik
- **Konfigurationsmanagement**: Zentrale Konfiguration in `configs/constants.js`
- **Error Handling**: Standardisierte Fehlerbehandlung über `controllers/database/errors/database_errors.js`
- **Logging**: Strukturiertes Logging über `LoggingService`
- **Caching**: Node-cache für Performance-Optimierung
- **Authentication**: API-Key basierte Authentifizierung

### Microsoft Graph API Integration
- **Endpoint**: `https://graph.microsoft.com/v1.0/me/todo/lists/{listId}/tasks`
- **Authentication**: OAuth 2.0 mit Microsoft Identity Platform
- **HTTP Method**: POST für Task-Erstellung

## Technische Spezifikation

### 1. Service Layer (`services/microsoft_todo_service.js`)

```javascript
class MicrosoftTodoService {
    constructor(logger) {
        this.logger = logger || LoggingService.getInstance();
        this.baseUrl = 'https://graph.microsoft.com/v1.0';
        this.maxRetries = 3;
        this.retryDelay = 1000;
    }

    async createTask(accessToken, listId, taskData) {
        // Implementation für Task-Erstellung
    }

    async getAccessToken(clientId, clientSecret, tenantId) {
        // OAuth 2.0 Token-Beschaffung
    }
}
```

### 2. Controller Layer (`controllers/microsoft/todo_controller.js`)

```javascript
exports.createTask = async (req, res) => {
    try {
        const { listId, title, body, dueDateTime, importance } = req.body;
        
        // Validation
        // Service-Aufruf
        // Response-Handling
        
    } catch (error) {
        // Error-Handling
    }
};
```

### 3. Route Definition (`routes/microsoft.js`)

```javascript
const express = require('express');
const router = express.Router();
const todoController = require('../controllers/microsoft/todo_controller');

router.post('/todo/tasks', todoController.createTask);

module.exports = router;
```

### 4. Konfiguration (`configs/constants.js`)

```javascript
const MICROSOFT_TODO_CONFIG = {
    API_BASE_URL: 'https://graph.microsoft.com/v1.0',
    TENANT_ID: process.env.MICROSOFT_TENANT_ID,
    CLIENT_ID: process.env.MICROSOFT_CLIENT_ID,
    CLIENT_SECRET: process.env.MICROSOFT_CLIENT_SECRET,
    SCOPES: ['https://graph.microsoft.com/Tasks.ReadWrite'],
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,
    CACHE_TTL: {
        token: 3300, // 55 minutes (Token läuft nach 1h ab)
        lists: 300   // 5 minutes
    }
};
```

## API Spezifikation

### Endpoint
```
POST /api/v1/microsoft/todo/tasks
```

### Request Body
```json
{
    "listId": "string (required)",
    "title": "string (required)",
    "body": "string (optional)",
    "dueDateTime": "ISO 8601 string (optional)",
    "importance": "low|normal|high (optional)",
    "categories": ["string"] (optional)
}
```

### Response
```json
{
    "success": true,
    "data": {
        "id": "task-id",
        "title": "Task Title",
        "status": "notStarted",
        "createdDateTime": "2025-01-02T10:00:00Z",
        "webUrl": "https://to-do.office.com/..."
    }
}
```

## Sicherheitsaspekte

### 1. OAuth 2.0 Flow
- Client Credentials Flow für Server-zu-Server Kommunikation
- Token-Caching mit automatischer Erneuerung
- Sichere Speicherung von Client-Credentials

### 2. Input Validation
- Validierung aller Eingabeparameter
- Sanitization von Text-Inhalten
- Rate Limiting für API-Aufrufe

### 3. Error Handling
- Strukturierte Fehlerbehandlung
- Logging von API-Fehlern
- Graceful Degradation bei Service-Ausfällen

## Testing Strategy

### 1. Unit Tests
- Service-Layer Tests mit Mocks
- Controller Tests
- Validation Tests

### 2. Integration Tests
- End-to-End API Tests
- Microsoft Graph API Integration Tests
- Error Scenario Tests

### 3. HTTP Client Tests
```http
### Create Microsoft ToDo Task
POST {{baseUrl}}/api/v1/microsoft/todo/tasks
X-API-Key: {{apiKey}}
Content-Type: application/json

{
    "listId": "test-list-id",
    "title": "Test Task from Algotrader API",
    "body": "This is a test task created via API",
    "importance": "normal"
}
```

## Deployment Considerations

### Environment Variables
```env
MICROSOFT_TENANT_ID=your-tenant-id
MICROSOFT_CLIENT_ID=your-client-id
MICROSOFT_CLIENT_SECRET=your-client-secret
```

### Dependencies
- Keine neuen Dependencies erforderlich (axios bereits vorhanden)
- Optional: `@azure/msal-node` für erweiterte OAuth-Funktionalität

## Swagger Documentation

### Components Definition
```json
{
    "MicrosoftTodoTask": {
        "type": "object",
        "required": ["listId", "title"],
        "properties": {
            "listId": {"type": "string"},
            "title": {"type": "string"},
            "body": {"type": "string"},
            "dueDateTime": {"type": "string", "format": "date-time"},
            "importance": {"type": "string", "enum": ["low", "normal", "high"]}
        }
    }
}
```

## Monitoring & Observability

### Logging Points
- OAuth Token-Beschaffung
- API-Request/Response Zeiten
- Fehlerhafte Requests
- Rate Limiting Events

### Metrics
- Task-Erstellungsrate
- API-Response-Zeiten
- Fehlerquoten
- Token-Erneuerungszyklen

## Erweiterungsmöglichkeiten

### Phase 2 Features
- Task-Updates (PATCH)
- Task-Löschung (DELETE)
- Task-Listen abrufen (GET)
- Bulk-Operations
- Webhook-Integration für Task-Updates

### Integration Points
- Trading-Events → ToDo Tasks
- AI-Predictions → Task-Prioritäten
- Calendar-Events → Task-Deadlines
