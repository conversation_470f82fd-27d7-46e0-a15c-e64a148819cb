# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.

2025-01-02 20:46:00 - Initial Memory Bank creation based on README.md and package.json analysis.

## Project Goal

**ML Algotrader API** - A comprehensive REST API for algorithmic trading with integrated AI/ML capabilities, providing automated trading execution, market analysis, and intelligent decision-making through multiple AI providers.

## Key Features

* **Multi-Provider AI Integration**: OpenAI, Groq, Mistral AI, Google Generative AI, and Anthropic Claude
* **Trading Platform Connectivity**: IG Markets integration with XTB WebSocket support
* **Comprehensive API**: RESTful endpoints for trading operations, market data, statistics, and configuration
* **Advanced Monitoring**: Helicone for cost control and AI usage monitoring, HyperDX for observability
* **Prompt Management**: Langfuse integration for versioned prompt templates
* **Database Operations**: MariaDB with connection pooling and caching (node-cache)
* **Authentication & Security**: API key-based authentication with IP filtering capabilities
* **Documentation**: Swagger/OpenAPI documentation with comprehensive endpoint coverage
* **Testing Framework**: Jest testing with HTTP client tests and mocking capabilities
* **Production Ready**: Multi-environment support (development/production) with deployment scripts

## Overall Architecture

* **Backend Framework**: Node.js 20.x with Express.js
* **Database**: MariaDB with generic connection pooling
* **Caching**: Node-cache for performance optimization, Helicone for AI caching
* **AI/ML Stack**: Multi-provider architecture supporting various LLMs and AI services
* **Trading Infrastructure**: Direct broker integration (IG Markets) with WebSocket support (XTB)
* **Monitoring Stack**: Helicone + HyperDX for comprehensive observability
* **Documentation**: Swagger UI with automated documentation generation
* **Deployment**: WSL-based rsync deployment to production server
* **Development Tools**: Nodemon for hot reloading, Jest for testing, TypeScript support