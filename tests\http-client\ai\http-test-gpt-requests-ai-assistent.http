###

// @name OpenAI-Assistent: assistantID=cvp0 mit claude-3-5-haiku-20241022
POST {{API_BASE_URL}}/api/v1/ai/assistant?assistantID=asst_WsvNWhiY8nMwUW8qjY42cvp0&llmModel=claude-3-5-haiku-20241022
Content-Type: text/plain

"Demobeispiel"

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

