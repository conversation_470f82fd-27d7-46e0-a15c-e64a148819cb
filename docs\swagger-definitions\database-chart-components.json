{"components": {"schemas": {"ChartDataPoint": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time", "description": "Zeitstempel des Datenpunkts", "example": "2023-04-15T14:30:00Z"}, "open": {"type": "number", "format": "float", "description": "Eröffnungskurs der Periode", "example": 1.0865}, "high": {"type": "number", "format": "float", "description": "Höchstkurs der Periode", "example": 1.0878}, "low": {"type": "number", "format": "float", "description": "Tiefstkurs der Periode", "example": 1.086}, "close": {"type": "number", "format": "float", "description": "Schlusskurs der Periode", "example": 1.0872}, "volume": {"type": "number", "format": "float", "description": "Handelsvolumen der Periode", "example": 1250.5}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Parameter 'symbol' is required"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getChartData"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Parameter 'symbol' is required", "function": "getChartData"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch chart data", "function": "getChartData"}}}}}}}