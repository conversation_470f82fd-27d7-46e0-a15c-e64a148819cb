const express = require('express');
const swaggerUi = require('swagger-ui-express');
const swaggerSpec = require('./docs/swagger');
const { apiKeyAuth } = require('./middleware/auth');
const cors = require('cors');
const bodyParser = require('body-parser');
const { IpFilter } = require('express-ipfilter');
const fs = require('fs');
const path = require('path');

// Import routes
const igRoutes = require('./routes/ig');
const databaseRoutes = require('./routes/database');
const aiRoutes = require('./routes/ai');
const fmpRoutes = require('./routes/fmp');
const microsoftRoutes = require('./routes/microsoft');

const server = express();
const port = 8080;

// IP Whitelist configuration based on network analysis
const ipWhitelist = [
    // Lokales Netzwerk - alle 192.168.x.x Adressen erlauben
    '***********/16',
    '::ffff:***********/112',
    // Explicitly add problematic IP
    '************',
    '::ffff:************',
    // Docker/Container Netzwerk
    '**********/16',
    '::ffff:**********/112',
    // Localhost
    '127.0.0.1',
    '::1',
    '::ffff:127.0.0.1',
    // ML-Algotrader Server
    '************',
    '::ffff:************'
];

// Domain Whitelist configuration
const domainWhitelist = [
    'localhost',
    '127.0.0.1',
    'ml-algotrader.com',
    'api.ml-algotrader.com',
    'app.ml-algotrader.com'
];

// For development, allow access without domain check if running in development mode
const isDevelopment = process.env.NODE_ENV !== 'production';

// Custom JSON serializer to handle BigInt values
const originalSend = express.response.json;
express.response.json = function(obj) {
    return originalSend.call(this, JSON.parse(JSON.stringify(obj, (key, value) =>
        typeof value === 'bigint' ? value.toString() : value
    )));
};

// Also override the send method for objects 
const originalSendMethod = express.response.send;
express.response.send = function(body) {
    if (body !== null && typeof body === 'object') {
        body = JSON.parse(JSON.stringify(body, (key, value) =>
            typeof value === 'bigint' ? value.toString() : value
        ));
    }
    return originalSendMethod.call(this, body);
};

// Health check endpoint (no auth required)
server.get('/health', (req, res) => {
    res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});
 
// CORS configuration
const allowedOrigins = ['https://app.ml-algotrader.com', 'http://localhost:8080', 'http://localhost', 'http://localhost:4321'];

// More permissive CORS for development
const isDev = process.env.NODE_ENV !== 'production';
if (isDev) {
    console.log('[CORS] Running in development mode - using more permissive CORS settings');
}

// Add explicit CORS headers middleware before the cors package
server.use((req, res, next) => {
    const origin = req.headers.origin;
    console.log(`[CORS-EXPLICIT] Request from origin: ${origin}`);
    
    // In development mode, allow any origin or use the requesting origin
    if (isDev) {
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Credentials', 'true');
        res.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Origin,X-Requested-With,Content-Type,Accept,Authorization,X-API-KEY');
        res.header('Access-Control-Expose-Headers', 'Content-Length,Content-Type');
        res.header('Access-Control-Max-Age', 3600 * 24 * 14); // 2 weeks
    }
    // In production, only allow specific origins
    else if (origin && allowedOrigins.includes(origin)) {
        res.header('Access-Control-Allow-Origin', origin);
        res.header('Access-Control-Allow-Credentials', 'true');
        res.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Origin,X-Requested-With,Content-Type,Accept,Authorization,X-API-KEY');
        res.header('Access-Control-Expose-Headers', 'Content-Length,Content-Type');
        res.header('Access-Control-Max-Age', 3600 * 24 * 14); // 2 weeks
    }
    
    // Handle preflight OPTIONS requests
    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }
    
    next();
});

// Keep the cors middleware as a fallback with more permissive settings in development
const corsOptions = {
    origin: function (origin, callback) {
        // Log the origin for debugging
        console.log(`[CORS] Request from origin: ${origin}`);
        
        // In development mode, allow any origin
        if (isDev) {
            callback(null, origin || '*');
        }
        // In production, only allow specific origins
        else if (!origin || allowedOrigins.indexOf(origin) !== -1) {
            callback(null, origin);
        } else {
            console.log(`[CORS] Blocked request from unauthorized origin: ${origin}`);
            callback(new Error('Not allowed by CORS: ' + origin));
        }
    },
    credentials: true,
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization', 'X-API-KEY'],
    exposedHeaders: ['Content-Length', 'Content-Type'],
    maxAge: 3600 * 24 * 14 // Cache for 2 weeks
};

server.use((req, res, next) => {
    // Get the real IP address
    let ip = req.headers['x-forwarded-for'] ||
            req.connection.remoteAddress ||
            req.socket.remoteAddress;
    
    console.log(`[IP-FILTER] Request from IP: ${ip}`);
    
    // Skip IP filtering in development mode
    if (isDevelopment) {
        return next();
    }
    
    // Always allow localhost and 192.168.x.x addresses
    if (ip === '127.0.0.1' || ip === '::1' || ip === '::ffff:127.0.0.1' ||
        ip.startsWith('192.168.') || ip.startsWith('::ffff:192.168.')) {
        console.log(`[IP-FILTER] Allowing local IP: ${ip}`);
        return next();
    }
    
    // Use the IP filter for other IPs
    IpFilter(ipWhitelist, {
        mode: 'allow',
        logLevel: 'deny',
    detectIp: (req) => {
        // Get the real IP address, considering proxies and IPv6-mapped IPv4 addresses
        let ip = req.headers['x-forwarded-for'] ||
                req.connection.remoteAddress ||
                req.socket.remoteAddress;
        
        // Log the detected IP for debugging
        console.log(`[IP-FILTER] Detected IP: ${ip}`);
        
        // Special handling for localhost and local network IPs
        if (ip === '127.0.0.1' || ip === '::ffff:127.0.0.1' || ip === '::1') {
            console.log(`[IP-FILTER] Detected localhost IP: ${ip} - should be allowed`);
        }
        
        // Special handling for 192.168.x.x addresses
        if (ip && (ip.startsWith('192.168.') || ip.startsWith('::ffff:192.168.'))) {
            console.log(`[IP-FILTER] Detected local network IP: ${ip} - should be allowed`);
            // For ************ specifically, log extra debug info
            if (ip === '************' || ip === '::ffff:************') {
                console.log(`[IP-FILTER] Special case for ************ detected`);
            }
        }
        
        return ip;
    },
    log: (message) => {
        console.log(`[IP-FILTER] ${message}`);
        fs.appendFileSync(
            path.join(__dirname, 'logs', 'security.log'),
            `${new Date().toISOString()} - ${message}\n`
        );
    }
    })(req, res, next);
});

// Domain filtering middleware
server.use((req, res, next) => {
    // Skip domain check in development mode or if hostname is empty (direct IP access)
    const host = req.hostname;
    
    console.log(`[DOMAIN-FILTER] Request hostname: ${host}`);
    
    if (isDevelopment || !host || domainWhitelist.includes(host) ||
        domainWhitelist.some(domain => host.endsWith(`.${domain}`))) {
        next();
    } else {
        const message = `Zugriff verweigert: Unerlaubte Domain ${host}`;
        console.log(`[DOMAIN-FILTER] ${message}`);
        fs.appendFileSync(
            path.join(__dirname, 'logs', 'security.log'),
            `${new Date().toISOString()} - ${message}\n`
        );
        res.status(403).send('Zugriff verweigert');
    }
});

// Apply the cors middleware after our custom CORS headers middleware
server.use(cors(corsOptions));
server.use(bodyParser.json({ limit: '10mb' }));
server.use(bodyParser.text({ limit: '10mb' }));
server.use(bodyParser.urlencoded({ extended: false, limit: '10mb' }));

// Request ID middleware
server.use((req, res, next) => {
    req.id = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    next();
});

// Swagger Documentation Setup
const swaggerUiOptions = {
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: "ML Algotrader API"
};

// Serve Swagger UI static files without auth
server.use('/api-docs', swaggerUi.serve);

// Protect Swagger UI and JSON with auth 
server.get('/api-docs', apiKeyAuth, (req, res, next) => {
    swaggerUi.setup(swaggerSpec, swaggerUiOptions)(req, res, next);
});
server.get('/api-docs.json', apiKeyAuth, (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
});

// Mount API routes with authentication
//server.use('/api/v1', apiKeyAuth); // Global auth for all API routes
server.use('/api/v1/db', databaseRoutes);
server.use('/api/v1/ig', igRoutes);
server.use('/api/v1/ai', aiRoutes);
server.use('/api/v1/fmp', fmpRoutes);
server.use('/api/v1/microsoft', microsoftRoutes);

// CORS error handling middleware
server.use((err, req, res, next) => {
    if (err.message && err.message.startsWith('Not allowed by CORS:')) {
        console.error(`[CORS-ERROR] ${err.message}`);
        return res.status(403).json({
            status: 'error',
            message: 'CORS Error: Origin not allowed',
            requestId: req.id
        });
    }
    next(err);
});

// Global error handling middleware
server.use((err, req, res, next) => {
    console.error(err.stack);
    
    // Set appropriate CORS headers even for error responses
    const origin = req.headers.origin;
    if (origin && (isDev || allowedOrigins.includes(origin))) {
        res.header('Access-Control-Allow-Origin', origin);
        res.header('Access-Control-Allow-Credentials', 'true');
    }
    
    res.status(500).json({
        status: 'error',
        message: 'Internal Server Error',
        error: process.env.NODE_ENV === 'development' ? err.message : undefined,
        requestId: req.id
    });
});

// Start server 
server.listen(port, () => {
    console.log(`Server started on port: ${port}`);
    console.log('Environment:', process.env.NODE_ENV);
});
