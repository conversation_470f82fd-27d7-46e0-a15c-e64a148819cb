@demo-symbol = CS.D.EURUSD.MINI.IP
@volume = 2.0
@from_date = 2025-04-20
@to_date = 2025-04-21

### GET: Active Trades
# Tests the GET /trades endpoint to retrieve active trades
GET {{API_BASE_URL}}/api/v1/ig/trades?refID=IG-P1
Content-Type: application/json

> {%
    client.test("Active Trades Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Active Trades Response structure valid", function() {
        client.assert(Array.isArray(response.body), "Response should be an array");
    });
    
    client.test("Trade position objects contain required fields", function() {
        if(response.body && response.body.length > 0) {
            const position = response.body[0];
            client.assert(position.hasOwnProperty('cmd'), "Missing cmd");
            client.assert(position.hasOwnProperty('symbol'), "Missing symbol");
            client.assert(position.hasOwnProperty('volume'), "Missing volume");
            client.assert(position.hasOwnProperty('order'), "Missing order");
            client.assert(position.hasOwnProperty('open_price'), "Missing open_price");
            client.assert(position.hasOwnProperty('profit'), "Missing profit");
        }
    });
%}

### GET: Trade History with Date Range
# Tests the GET /trades/history endpoint to retrieve historical trades
GET {{API_BASE_URL}}/api/v1/ig/trades/history?from={{from_date}}&to={{to_date}}&refID=IG-P1
Content-Type: application/json

> {%
    client.test("Trade History Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Trade History Response structure valid", function() {
        client.assert(response.body.hasOwnProperty('activities'), "Missing activities array");
        client.assert(Array.isArray(response.body.activities), "Activities should be an array");
    });
    
    client.test("Trade history entries contain required fields", function() {
        if(response.body.activities && response.body.activities.length > 0) {
            const activity = response.body.activities[0];
            client.assert(activity.hasOwnProperty('date'), "Missing date");
            client.assert(activity.hasOwnProperty('epic'), "Missing epic");
            client.assert(activity.hasOwnProperty('dealId'), "Missing dealId");
            client.assert(activity.hasOwnProperty('type'), "Missing activity type");
        }
    });
    
    client.test("Date filtering is respected", function() {
        if(response.body.activities && response.body.activities.length > 0) {
            const fromDate = new Date(client.global.get("from_date"));
            const toDate = new Date(client.global.get("to_date"));
            
            // Check that all activities are within the requested date range
            const allDatesInRange = response.body.activities.every(activity => {
                const activityDate = new Date(activity.date);
                return activityDate >= fromDate && activityDate <= toDate;
            });
            
            client.assert(allDatesInRange, "Some activities are outside the requested date range");
        }
    });
%}

### GET: Margin Trade Requirements
# Tests the GET /trades/margin-trade endpoint to calculate margin requirements
GET {{API_BASE_URL}}/api/v1/ig/trades/margin-trade?symbol={{demo-symbol}}&direction=BUY&size={{volume}}
Content-Type: application/json

> {%
    client.test("Margin Trade Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Margin Trade Response contains required fields", function() {
        const body = response.body;
        client.assert(body.hasOwnProperty('margin'), "Missing margin requirement");
        client.assert(typeof body.margin === 'number', "Margin should be a numeric value");
    });
%}

### GET: Margin Trade with SELL direction
# Tests the GET /trades/margin-trade endpoint with SELL direction
GET {{API_BASE_URL}}/api/v1/ig/trades/margin-trade?symbol={{demo-symbol}}&direction=SELL&size={{volume}}
Content-Type: application/json

> {%
    client.test("Margin Trade SELL Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Margin Trade SELL Response contains required fields", function() {
        const body = response.body;
        client.assert(body.hasOwnProperty('margin'), "Missing margin requirement");
        client.assert(typeof body.margin === 'number', "Margin should be a numeric value");
    });
%}

### GET: Trade History with Symbol Filter
# Tests the GET /trades/history endpoint with symbol filtering
GET {{API_BASE_URL}}/api/v1/ig/trades/history?symbol={{demo-symbol}}&from={{from_date}}&to={{to_date}}
Content-Type: application/json

> {%
    client.test("Filtered Trade History Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
    
    client.test("Symbol filtering is respected", function() {
        if(response.body.activities && response.body.activities.length > 0) {
            const allMatchSymbol = response.body.activities.every(activity => {
                return activity.epic === client.global.get("demo-symbol");
            });
            
            client.assert(allMatchSymbol, "Some activities don't match the requested symbol");
        }
    });
%}