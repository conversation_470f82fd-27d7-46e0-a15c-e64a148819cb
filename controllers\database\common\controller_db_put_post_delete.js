const db = require('../../../configs/config_db');

exports.getCalendarList = async (req, res) => {
    try {
        const { startDate, endDate, country, importance } = req.query;
        const query = {
            sql: `SELECT * FROM calendar 
                  WHERE (? IS NULL OR date >= ?)
                    AND (? IS NULL OR date <= ?)
                    AND (? IS NULL OR country = ?)
                    AND (? IS NULL OR importance = ?)
                  ORDER BY date DESC`,
            values: [startDate, startDate, endDate, endDate, country, country, importance, importance],
            bigIntAsNumber: true,
            timezone: 'de_de'
        };
        
        const result = await db.pool.query(query);
        res.json(result);
    } catch (err) {
        console.error("Error in getCalendarList:", err);
        res.status(500).json({ error: true, message: "Database error" });
    }
};

exports.putTradingMode = async (req, res) => {
    let p_refID = req.query.refID || 'IG-P1'; 
    let p_value = req.query.value || 'on';
    if (process.env.NODE_ENV !== 'production')
       console.log("[putTradingMode] refID: " + p_refID + " p_value: " + p_value);
    try {
        const p_query = { 
            sql: 'update `accounts` set mode = ? where refId = ?',
            values: [p_value, p_refID],
            types: ['string', 'string'],
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        const result = await db.pool.query(p_query);
        res.send({"state": "sucessfull"});
    } catch (err) {
        throw err;
    }
};

exports.putSymbolSetup = async (req, res) => {
    let p_symbol = req.params.symbolID;
    let p_deadZoneLow = parseFloat(req.query.deadZoneLow);
    if (p_deadZoneLow == undefined) {
        console.error("p_deadZoneLow is undefined");
        p_deadZoneLow = null;
    }
    let p_deadZoneHigh = parseFloat(req.query.deadZoneHigh);
    if (p_deadZoneHigh == undefined) {
        console.error("deadZoneHigh is undefined");
        p_deadZoneHigh = null;
    }
    let p_deadZoneValidUntil = req.query.deadZoneValidUntil
    if (p_deadZoneValidUntil == undefined) {
        console.error("deadZoneValidUntil is undefined");
        p_deadZoneValidUntil = new Date();
        p_deadZoneValidUntil.setDate(p_deadZoneValidUntil.getDate() + 1);
    }
    let p_deadZoneUpdateSource = req.query.deadZoneUpdateSource;
    if (p_deadZoneUpdateSource == undefined) {
        console.error("deadZoneUpdateSource is undefined");
        p_deadZoneUpdateSource = "API";
    }
    console.log("p_symbol: " + p_symbol +
        ", p_deadZoneLow: " + p_deadZoneLow +
        ", p_deadZoneHigh: " + p_deadZoneHigh +
        ", p_deadZoneValidUntil: " + p_deadZoneValidUntil +
        ", p_deadZoneUpdateSource: " + p_deadZoneUpdateSource);

    try {
        const p_query = {
            sql: 'UPDATE `symbol_setups` SET deadZoneLow = ?, deadZoneHigh = ?, deadZoneValidUntil = ?, deadZoneUpdateSource = ?, deadZoneUpdateTime=NOW() '+
                        ' WHERE symbol = ?',
            values: [p_deadZoneLow, p_deadZoneHigh, p_deadZoneValidUntil, p_deadZoneUpdateSource, p_symbol],
            types: ['double', 'double', 'date', 'string', 'string'],
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        const result = await db.pool.query(p_query);
        res.send({"state": "sucessfull"});
    } catch (err) {
        throw err;
    }
};

exports.putCalendarToggles = async (req, res) => {
    let p_calendarID = req.params.calendarID;
    let p_toggle_deactivate_new_trades = req.query.toggle_deactivate_new_trades;
    if (p_toggle_deactivate_new_trades == undefined) {
        console.log("p_toggle_deactivate_new_trades is undefined");
        p_toggle_deactivate_new_trades = 0;
    }
    let p_toggle_deactivate_high_volume_trades = req.query.toggle_deactivate_high_volume_trades;
    if (p_toggle_deactivate_high_volume_trades == undefined) {
        console.log("p_toggle_deactivate_high_volume_trades is undefined");
        p_toggle_deactivate_high_volume_trades = 1;
    }
    let p_toggle_close_all_positions_before = req.query.toggle_close_all_positions_before;
    if (p_toggle_close_all_positions_before == undefined) {
        console.log("p_toggle_close_all_positions_before is undefined");
        p_toggle_close_all_positions_before = 0;
    }
    let p_toggle_actiontime_minutes_window_after = req.query.toggle_actiontime_minutes_window_after;
    if (p_toggle_actiontime_minutes_window_after == undefined) {
        console.log("p_toggle_actiontime_minutes_window_after is undefined");
        p_toggle_actiontime_minutes_window_after = 45;
    }
    let p_toggle_actiontime_minutes_window_before = req.query.toggle_actiontime_minutes_window_before;
    if (p_toggle_actiontime_minutes_window_before == undefined) {
        console.log("p_toggle_actiontime_minutes_window_before is undefined");
        p_toggle_actiontime_minutes_window_before = 5;
    }
    console.log("p_calendarID: " + p_calendarID + " p_toggle_deactivate_new_trades: " + p_toggle_deactivate_new_trades + " p_toggle_deactivate_high_volume_trades: " + p_toggle_deactivate_high_volume_trades + " p_toggle_close_all_positions_before: " + p_toggle_close_all_positions_before + " p_toggle_actiontime_minutes_window_after: " + p_toggle_actiontime_minutes_window_after + " p_toggle_actiontime_minutes_window_before: " + p_toggle_actiontime_minutes_window_before);
    try {
        let query;
        let values;

        if (p_toggle_deactivate_new_trades == 0 && p_toggle_deactivate_high_volume_trades == 0 && p_toggle_close_all_positions_before == 0) {
            // Wenn alle Toggles deaktiviert sind, lösche den Eintrag
            query = {
                sql: 'DELETE ct\n' +
                    'FROM calendar_toggles ct\n' +
                    'JOIN calendar c ON ct.regEx = c.title AND ct.country = c.country\n' +
                    'WHERE c.id = ?',
                values: [p_calendarID],
                bigIntAsNumber: true,
                timezone: 'de_de'
            };
        } else {
            // Ansonsten aktualisiere oder füge den Eintrag ein
            query = {
                sql: 'REPLACE INTO calendar_toggles \
                     (regEx, country, \
                      toggle_deactivate_new_trades, \
                      toggle_deactivate_high_volume_trades, \
                      toggle_close_all_positions_before, \
                      toggle_actiontime_minutes_window_after, \
                      toggle_actiontime_minutes_window_before) \
                    SELECT c.title, c.country, ?, ?, ?, ?, ? \
                      FROM calendar c\
                     WHERE c.id = ?;',
                values: [p_toggle_deactivate_new_trades, p_toggle_deactivate_high_volume_trades, p_toggle_close_all_positions_before, p_toggle_actiontime_minutes_window_after, p_toggle_actiontime_minutes_window_before, p_calendarID],
                bigIntAsNumber: true,
                timezone: 'de_de'
            };
        }

        const result = await db.pool.query(query);
        res.send({"state": "successful"});
    } catch (err) {
        console.error("Error in putCalendarToggles:", err);
        res.status(500).send({"state": "error", "message": err.message});
    }
};

exports.putStrategyToggles = async (req, res) => {
    let p_strategyID = req.params.strategyID;
    let p_activate_ig_p1 = req.query.activate_ig_p1;
    if (p_activate_ig_p1 == undefined) {
        console.log("p_activate_ig_p1 is undefined");
        p_activate_ig_p1 = null;
    }
    let p_activate_ig_d1 = req.query.activate_ig_d1;
    if (p_activate_ig_d1 == undefined) {
        console.log("p_activate_ig_d1 is undefined");
        p_activate_ig_d1 = null;
    }
    let p_activate_high_volume = req.query.activate_high_volume;
    if (p_activate_high_volume == undefined) {
        console.log("p_activate_high_volume is undefined");
        p_activate_high_volume = null;
    }

    let p_activate_bes = req.query.activate_break_even_stop;
    if (p_activate_bes == undefined) {
        console.log("p_activate_bes is undefined");
        p_activate_bes = null;
    }

    let p_activate_ts = req.query.activate_trailing_stops;
    if (p_activate_ts == undefined) {
        console.log("p_activate_ts is undefined");
        p_activate_ts = null;
    }

    let p_inactive = req.query.inactive;
    if (p_inactive == undefined) {
        console.log("p_inactive is undefined");
        p_inactive = null;
    }

    let p_activate_initial_stop_multiple = req.query.activate_initial_stop_multiple;
    if (p_activate_initial_stop_multiple == undefined) {
        console.log("p_activate_initial_stop_multiple is undefined");
        p_activate_initial_stop_multiple = null;
    }

    if (process.env.NODE_ENV !== 'production')
       console.log("p_strategyID: " + p_strategyID + ", p_activate_ig_p1: " + p_activate_ig_p1 + ", p_activate_ig_d1: " + p_activate_ig_d1 + ", p_activate_high_volume: " + p_activate_high_volume+ ", p_activate_bes:"+p_activate_bes+ ", p_activate_ts:"+p_activate_ts);
    try {
        const p_query = {
            sql: 'UPDATE `strategy_toggle` \
                  SET activate_ig_p1=COALESCE(?,activate_ig_p1) , \
                      activate_ig_d1=COALESCE(?,activate_ig_d1), \
                      activate_high_volume=COALESCE(?,activate_high_volume), \
                      activate_break_even_stop=COALESCE(?,activate_break_even_stop), \
                      activate_trailing_stops=COALESCE(?,activate_trailing_stops), \
                      activate_initial_stop_multiple=COALESCE(?,activate_initial_stop_multiple), \
                      inactive=COALESCE(?,inactive) \
                WHERE ID = ?',
            values: [p_activate_ig_p1, p_activate_ig_d1, p_activate_high_volume, p_activate_bes, p_activate_ts, p_activate_initial_stop_multiple, p_inactive, p_strategyID ],
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        console.log(p_query);
        const result = await db.pool.query(p_query);
        res.send({"state": "sucessfull updated strategy with ID: " + p_strategyID});
    } catch (err) {
        throw err;
    }
};

exports.putMirrorTradingSettings = async (req, res) => {
    let p_sourceAccountRefID = req.query.sourceAccountRefID;
    let p_targetAccountRefID = req.query.targetAccountRefID;
    
    if (!p_sourceAccountRefID || !p_targetAccountRefID) {
        return res.status(400).send({"state": "error", "message": "sourceAccountRefID and targetAccountRefID are required"});
    }

    // Define all possible fields that can be updated based on the table structure
    let updateFields = [
        // Basic settings
        'default_minProfitForSourceTrade',
        'default_minProfitForBreakEvenStopp',
        'default_minProfitForActivateTakeProfitTrailingStopp',
        'default_useDeadZones',
        'default_useSLFromOriginTrade',
        'activate_trailingStops_for_targetTrades',
        
        // Time-related settings
        'default_check_last_trading_activitity_inMin',
        'default_check_max_timedistance_inMin',
        
        // Position aggregation settings
        'aggregateSymbolPositionsWithProfitOf',
        'aggregateSymbolPositionsWithMinCount',
        
        // Trailing stop factors
        'trailingStopFactor1',
        'trailingStopFactor2',
        'trailingStopFactor3',
        
        // Profit multipliers
        'profitMultiplier1',
        'profitMultiplier2',
        'profitMultiplier3',
        
        // Risk pyramid settings
        'riskPyramidMinPositionCount',
        'riskPyramidProfitThreshold',
        'pyramidBaseStopFactor',
        'pyramidStopFactorIncrement'
    ];

    let updateValues = [];
    let updateSQL = [];

    // Build the update SQL dynamically based on provided parameters
    updateFields.forEach(field => {
        if (req.query[field] !== undefined) {
            updateValues.push(req.query[field]);
            updateSQL.push(`${field} = ?`);
        }
    });

    if (updateSQL.length === 0) {
        return res.status(400).send({"state": "error", "message": "No fields to update"});
    }

    try {
        // Check if record exists first
        const checkQuery = {
            sql: `SELECT 1 FROM mirror_trading_settings
                  WHERE sourceAccountRefID = ? AND targetAccountRefID = ?`,
            values: [p_sourceAccountRefID, p_targetAccountRefID],
            bigIntAsNumber: true
        };
        
        const existingRecord = await db.pool.query(checkQuery);
        
        let query;
        if (existingRecord.length === 0) {
            // Record doesn't exist, create INSERT query with all fields
            const allFields = ['sourceAccountRefID', 'targetAccountRefID', ...updateFields.filter(field => req.query[field] !== undefined)];
            const allValues = [p_sourceAccountRefID, p_targetAccountRefID, ...updateValues];
            const placeholders = allFields.map(() => '?').join(', ');
            
            query = {
                sql: `INSERT INTO mirror_trading_settings (${allFields.join(', ')})
                      VALUES (${placeholders})`,
                values: allValues,
                bigIntAsNumber: true,
                timezone: 'de_de'
            };
        } else {
            // Record exists, create UPDATE query
            query = {
                sql: `UPDATE mirror_trading_settings
                      SET ${updateSQL.join(', ')}
                      WHERE sourceAccountRefID = ? AND targetAccountRefID = ?`,
                values: [...updateValues, p_sourceAccountRefID, p_targetAccountRefID],
                bigIntAsNumber: true,
                timezone: 'de_de'
            };
        }

        if (process.env.NODE_ENV !== 'production') {
            console.log("[putMirrorTradingSettings] Query:", query);
        }

        const result = await db.pool.query(query);

        res.send({
            "state": "successful",
            "message": `Updated mirror trading settings for sourceAccountRefID: ${p_sourceAccountRefID} and targetAccountRefID: ${p_targetAccountRefID}`,
            "affectedRows": result.affectedRows
        });
    } catch (err) {
        console.error("Error in putMirrorTradingSettings:", err);
        res.status(500).send({"state": "error", "message": err.message});
    }
};

exports.postXtbTrades = async (req, res) => {
    let task = req.body;
    try {
        const result = await db.pool.query("insert into `trades_history` (description) values (?)", [task.description]);
        res.send(result);
    } catch (err) {
        throw err;
    }
};

exports.deleteXtbTrades = async (req, res) => {
    let id = req.query.id;
    try {
        const result = await db.pool.query("delete from `trades_history` where id = ?", [id]);
        res.send(result);
    } catch (err) {
        throw err;
    }
};

exports.postNewsStatus = async (req, res) => {
    let p_newsID = req.body.id;
    let p_isRead = req.body.is_read;
    let p_isBookmarked = req.body.is_bookmarked;
    let p_newsCategory = req.body.news_category;
    
    if (p_newsID === undefined) {
        return res.status(400).json({
            "state": "error",
            "message": "id field is required in the request body"
        });
    }
    
    if (p_isRead === undefined && p_isBookmarked === undefined && p_newsCategory === undefined) {
        return res.status(400).json({
            "state": "error",
            "message": "At least one of is_read, is_bookmarked, or news_category fields is required in the request body"
        });
    }
    
    // Build the SQL query dynamically based on which fields are provided
    let updateFields = [];
    let updateValues = [];
    
    if (p_isRead !== undefined) {
        // Convert to boolean if needed
        if (typeof p_isRead !== 'boolean') {
            p_isRead = p_isRead === true || p_isRead === 'true' || p_isRead === 1;
        }
        updateFields.push('is_read = ?');
        updateValues.push(p_isRead);
    }
    
    if (p_isBookmarked !== undefined) {
        // Convert to boolean if needed
        if (typeof p_isBookmarked !== 'boolean') {
            p_isBookmarked = p_isBookmarked === true || p_isBookmarked === 'true' || p_isBookmarked === 1;
        }
        updateFields.push('is_bookmarked = ?');
        updateValues.push(p_isBookmarked);
    }
    
    if (p_newsCategory !== undefined) {
        updateFields.push('news_category = ?');
        updateValues.push(p_newsCategory);
    }
    
    // Add the news ID to the values array
    updateValues.push(p_newsID);
    
    if (process.env.NODE_ENV !== 'production') {
        console.log(`[postNewsStatus] newsID: ${p_newsID}, is_read: ${p_isRead}, is_bookmarked: ${p_isBookmarked}, news_category: ${p_newsCategory}`);
    }
    
    try {
        const query = {
            sql: `UPDATE \`news\` SET ${updateFields.join(', ')} WHERE uuid = ?`,
            values: updateValues,
            bigIntAsNumber: true,
            timezone: 'de_de'
        };
        
        const result = await db.pool.query(query);
        
        if (result.affectedRows === 0) {
            return res.status(404).json({
                "state": "error",
                "message": `News item with ID ${p_newsID} not found`
            });
        }
        
        res.json({
            "state": "successful",
            "message": `Updated status for news item ${p_newsID}`
        });
    } catch (err) {
        console.error("Error in postNewsStatus:", err);
        res.status(500).json({
            "state": "error",
            "message": err.message
        });
    }
};
