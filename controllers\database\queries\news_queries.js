/**
 * SQL-Queries für News-bezogene Funktionen
 */

const buildNewsQuery = (days, limit, onlyUnread = null, onlyBookmarked = null, searchKeywords = null) => {
    let sql = `SELECT * FROM \`news\`
                WHERE \`time\` >= CURDATE() - INTERVAL ? DAY and uuid NOT LIKE "Factor-Map%" and uuid NOT LIKE "GPTDaySummary%"`;
    
    const values = [days];

    // Add optional filter for unread news
    if (onlyUnread === true) {
        sql += ` AND is_read = 0`;
    }

    // Add optional filter for bookmarked news
    if (onlyBookmarked === true) {
        sql += ` AND is_bookmarked = 1`;
    }

    // Add optional keyword search (AND logic for multiple keywords)
    if (searchKeywords && searchKeywords.trim().length > 0) {
        // Split keywords by comma and trim whitespace
        const keywords = searchKeywords.split(',').map(keyword => keyword.trim()).filter(keyword => keyword.length > 0);
        
        if (keywords.length > 0) {
            // Build AND conditions for each keyword
            const keywordConditions = keywords.map(() =>
                `(LOWER(title) LIKE LOWER(?) OR LOWER(body) LIKE LOWER(?) OR LOWER(gpt_summarize) LIKE LOWER(?) OR LOWER(news_category) LIKE LOWER(?))`
            ).join(' AND ');
            
            sql += ` AND (${keywordConditions})`;
            
            // Add each keyword 4 times (for title, body, gpt_summarize, news_category) with wildcards
            keywords.forEach(keyword => {
                const wildcardKeyword = `%${keyword}%`;
                values.push(wildcardKeyword, wildcardKeyword, wildcardKeyword, wildcardKeyword);
            });
        }
    }

    sql += ` ORDER BY time desc LIMIT ?`;
    values.push(limit);

    return {
        sql: sql,
        values: values,
        bigIntAsNumber: true,
        timezone: 'de_de'
    };
};

module.exports = {
    buildNewsQuery
};
