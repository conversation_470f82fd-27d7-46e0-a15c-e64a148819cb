{"components": {"schemas": {"IGTrade": {"type": "object", "properties": {"dealId": {"type": "string", "description": "Eindeutige ID des Trades", "example": "DIAAABC123"}, "dealReference": {"type": "string", "description": "Referenz für den Trade", "example": "REF123456"}, "cmd": {"type": "number", "description": "Befehlstyp (0=BUY, 1=SELL)", "example": 0}, "symbol": {"type": "string", "description": "Handelssymbol", "example": "CS.D.EURUSD.MINI.IP"}, "volume": {"type": "number", "format": "float", "description": "Handelsvolumen", "example": 2.0}, "order": {"type": "number", "description": "Auftrags-ID", "example": 12345678}, "open_price": {"type": "number", "format": "float", "description": "Eröffnungspreis", "example": 1.08543}, "direction": {"type": "string", "enum": ["BUY", "SELL"], "description": "Handelsrichtung"}, "size": {"type": "number", "format": "float", "description": "Positionsgröße", "example": 1.5}, "level": {"type": "number", "format": "float", "description": "Einstiegspreis", "example": 1.08543}, "limitLevel": {"type": "number", "format": "float", "description": "Take-Profit-Level", "example": 1.09}, "stopLevel": {"type": "number", "format": "float", "description": "Stop-Loss-Level", "example": 1.08}, "createdDate": {"type": "string", "format": "date-time", "description": "Zeitstempel der Positionserstellung"}, "currency": {"type": "string", "description": "Währung der Position", "example": "EUR"}, "pipValue": {"type": "number", "format": "float", "description": "Wert eines Pips für dieses Instrument", "example": 0.0001}, "profit": {"type": "number", "format": "float", "description": "Aktueller Gewinn/Verlust der Position", "example": 25.5}}}, "IGTradeHistory": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time", "description": "Datum und Uhrzeit der Transaktion"}, "dateUtc": {"type": "string", "format": "date-time", "description": "Datum und Uhrzeit der Transaktion in UTC"}, "epic": {"type": "string", "description": "Eindeutiger Instrumentencode", "example": "CS.D.EURUSD.MINI.IP"}, "dealId": {"type": "string", "description": "Eindeutige ID des Trades", "example": "DIAAABC123"}, "type": {"type": "string", "description": "Art der Aktivität", "example": "POSITION"}, "instrumentName": {"type": "string", "description": "Name des gehandelten Instruments", "example": "EUR/USD"}, "period": {"type": "string", "description": "Handelszeitraum", "example": "DFB"}, "profitAndLoss": {"type": "string", "description": "Gewinn oder Verlust mit Währungssymbol", "example": "€25.50"}, "transactionType": {"type": "string", "description": "Art der Transaktion", "example": "DEAL"}, "reference": {"type": "string", "description": "Referenznummer der Transaktion", "example": "DIAAABC123"}, "openLevel": {"type": "string", "description": "Eröffnungskurs", "example": "1.08543"}, "closeLevel": {"type": "string", "description": "Schlusskurs", "example": "1.08643"}, "size": {"type": "string", "description": "Größe der Position", "example": "1.5"}, "currency": {"type": "string", "description": "Währung", "example": "EUR"}, "cashTransaction": {"type": "boolean", "description": "<PERSON><PERSON>t an, ob es sich um eine Bartransaktion handelt"}}}, "IGAccount": {"type": "object", "properties": {"accountId": {"type": "string", "description": "ID des IG-Kontos", "example": "ABCDEF"}, "accountName": {"type": "string", "description": "Name des Kontos", "example": "Demo-Konto"}, "accountType": {"type": "string", "description": "Kontotyp", "example": "CFD"}, "balance": {"type": "object", "properties": {"balance": {"type": "number", "format": "float", "description": "Kontostand", "example": 10000.5}, "deposit": {"type": "number", "format": "float", "description": "Einzahlungsbetrag", "example": 5000.0}, "profitLoss": {"type": "number", "format": "float", "description": "Aktueller Gewinn/Verlust", "example": 250.5}, "available": {"type": "number", "format": "float", "description": "Verfügbarer Betrag", "example": 8500.25}}}, "currency": {"type": "string", "description": "Kontowährung", "example": "EUR"}, "status": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "ENABLED"}}}, "IGMarginCalculation": {"type": "object", "properties": {"margin": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Anford<PERSON>ung", "example": 250.75}, "marginFactor": {"type": "number", "format": "float", "description": "Margin-Faktor in Prozent", "example": 3.33}, "onePipMeans": {"type": "number", "format": "float", "description": "Wert eines Pips", "example": 0.0001}, "symbol": {"type": "string", "description": "Handelssymbol", "example": "CS.D.EURUSD.TODAY.IP"}, "currency": {"type": "string", "description": "Währung", "example": "EUR"}}}, "IGErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "Fehlermeldung", "example": "Server error"}}}}, "responses": {"IGError": {"description": "Fehler bei der Verarbeitung der Anfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IGErrorResponse"}}}}}}}