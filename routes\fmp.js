const express = require('express');
const router = express.Router();
const calendarController = require('../controllers/fmp/calendar_controller');
const newsController = require('../controllers/fmp/news_controller');
const chartController = require('../controllers/fmp/chart_controller');

// Calendar routes
router.get('/calendar', calendarController.fetchCalendar.bind(calendarController));

// News routes
router.get('/news', newsController.fetchNews.bind(newsController));

// Chart routes
router.get('/chart', chartController.fetchIntradayChart.bind(chartController));

module.exports = router;