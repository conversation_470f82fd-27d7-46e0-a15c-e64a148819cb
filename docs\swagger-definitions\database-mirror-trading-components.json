{"components": {"schemas": {"MirrorTradingSettings": {"type": "object", "properties": {"source_account_id": {"type": "integer", "description": "ID des Quellkontos", "example": 1}, "source_refId": {"type": "string", "description": "Referenz-ID des Quellkontos", "example": "IG-D1"}, "target_account_id": {"type": "integer", "description": "ID des Zielkontos", "example": 2}, "target_refId": {"type": "string", "description": "Referenz-ID des Zielkontos", "example": "IG-P1"}, "mirror_active": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> an, ob das Mirror Trading aktiv ist", "example": true}, "volume_factor": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON>, mit dem das Handelsvolumen multipliziert wird", "example": 0.5}, "max_volume": {"type": "number", "format": "float", "description": "Maximales Handelsvolumen pro Trade", "example": 5.0}, "min_volume": {"type": "number", "format": "float", "description": "Minimales Handelsvolumen pro Trade", "example": 0.1}, "mirror_since": {"type": "string", "format": "date-time", "description": "Zeitpunkt, seit dem das Mirror Trading aktiv ist", "example": "2023-04-01T10:00:00Z"}, "mirror_symbols": {"type": "string", "description": "Kommagetrennte Liste von Symbolen, die gespiegelt werden sollen (leer = alle)", "example": "EURUSD,GBPUSD,USDJPY"}, "exclude_symbols": {"type": "string", "description": "Kommagetrennte Liste von Symbolen, die nicht gespiegelt werden sollen", "example": "USDCAD,AUDUSD"}}}, "MirrorTradingLog": {"type": "object", "properties": {"log_id": {"type": "integer", "description": "Eindeutige ID des Log-Eintrags", "example": 12345}, "timestamp": {"type": "string", "format": "date-time", "description": "Zeitpunkt des Log-Eintrags", "example": "2023-04-15T14:30:00Z"}, "source_refId": {"type": "string", "description": "Referenz-ID des Quellkontos", "example": "IG-D1"}, "target_refId": {"type": "string", "description": "Referenz-ID des Zielkontos", "example": "IG-P1"}, "action": {"type": "string", "description": "Durchgeführte Aktion", "example": "OPEN_TRADE"}, "symbol": {"type": "string", "description": "Gehandeltes Symbol", "example": "EURUSD"}, "source_volume": {"type": "number", "format": "float", "description": "Handelsvolumen im Quellkonto", "example": 2.0}, "target_volume": {"type": "number", "format": "float", "description": "Handelsvolumen im Zielkonto", "example": 1.0}, "source_direction": {"type": "string", "description": "Handelsrichtung im Quellkonto", "enum": ["BUY", "SELL"], "example": "BUY"}, "target_direction": {"type": "string", "description": "Handelsrichtung im Zielkonto", "enum": ["BUY", "SELL"], "example": "BUY"}, "source_price": {"type": "number", "format": "float", "description": "Handelspreis im Quellkonto", "example": 1.0865}, "target_price": {"type": "number", "format": "float", "description": "Handelspreis im Zielkonto", "example": 1.0867}, "status": {"type": "string", "description": "Status des Mirror-Trades", "enum": ["SUCCESS", "FAILED", "PENDING"], "example": "SUCCESS"}, "error_message": {"type": "string", "description": "Fehlermeldung bei fehlgeschlagenen Trades", "example": null}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Parameter 'targetRefID' is required"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getMirrorTradingSettings"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Parameter 'targetRefID' is required", "function": "getMirrorTradingSettings"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch mirror trading settings", "function": "getMirrorTradingSettings"}}}}}}}