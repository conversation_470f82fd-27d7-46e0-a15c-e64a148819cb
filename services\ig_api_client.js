const axios = require('axios');
const crypto = require('crypto');
const IGRateLimiter = require('./ig_rate_limiter');
const { LOG_LEVELS, log, logPerformance } = require('./logging_service');

class IGApiClient {
    
    constructor(config) {
        this.identifier = config.identifier;
        this.alias = config.alias;
        this.password = config.password;
        this.apiKey = config.apiKey;
        this.baseUrl = config.isDemo ? process.env.IG_DEMO_URL : process.env.IG_LIVE_URL;
        this.rateLimiter = new IGRateLimiter();
        this.accessToken = '';
        this.tokenExpiry = new Date();
        this.correlationId = this.generateCorrelationId();
        this.sslVerify = config.sslVerify ?? false;
        this.isRefreshing = false;
        this.hasValidToken = false;
        this.loginAttempts = 0;
        this.isInitialized = false;
        this.accountId = '';
        this.MAX_LOGIN_ATTEMPTS = 2;
        this.TOKEN_LIFETIME = 60; // Token lifetime in seconds
    }

    generateCorrelationId() {
        return crypto.randomUUID();
    }

    enrichContext(data, component) {
        return {
            ...data,
            correlation_id: this.correlationId,
            component,
            timestamp: Date.now(),
            environment: this.isDemo() ? 'demo' : 'live'
        };
    }

    isDemo() {
        return this.baseUrl === process.env.IG_DEMO_URL;
    }

    async login() {
        const startTime = process.hrtime.bigint();
        
        // Wait for up to 5 seconds if another login is in progress
        if (this.isRefreshing) {
            log(LOG_LEVELS.DEBUG, 'login', 'Another login in progress, waiting...', this.enrichContext({}, 'auth'));
            
            // Try for up to 5 seconds (50 iterations of 100ms)
            const maxWaitIterations = 50;
            let waitIterations = 0;
            
            while (this.isRefreshing && waitIterations < maxWaitIterations) {
                await new Promise(resolve => setTimeout(resolve, 100));
                waitIterations++;
            }
            
            // If still refreshing after waiting, return error
            if (this.isRefreshing) {
                const waitTime = Number(process.hrtime.bigint() - startTime) / 1e6;
                log(LOG_LEVELS.WARN, 'login', 'Timed out waiting for ongoing login', this.enrichContext({
                    wait_time_ms: waitTime,
                    max_wait_ms: maxWaitIterations * 100
                }, 'auth'));
                
                await this.rateLimiter.logRequest({
                    identifier: this.identifier,
                    method: 'POST',
                    endpoint: 'session',
                    base_url: this.baseUrl,
                    correlation_id: this.correlationId,
                    response_code: 401,
                    response_time: waitTime
                });
                return { code: 401, response: { message: 'Authentication in progress, timed out after waiting 5 seconds' } };
            }
            
            // If we got here, the other login completed and we can proceed
            log(LOG_LEVELS.DEBUG, 'login', 'Previous login completed, proceeding with new login', this.enrichContext({
                wait_iterations: waitIterations,
                wait_time_ms: Number(process.hrtime.bigint() - startTime) / 1e6
            }, 'auth'));
            
            // Reset the start time since we've been waiting
            return this.login(); // Recursively call login() with a fresh start time
        }

        this.isRefreshing = true;

        try {
            // Check rate limit before attempting login
            await this.rateLimiter.checkRateLimit(this.identifier, 'POST', 'session');

            const data = {
                identifier: this.identifier,
                password: this.password,
                encryptedPassword: false
            };

            let url = `${this.baseUrl}session`;
            if (this.isDemo()) {
                url += `?accountId=${process.env.IG_DEMO_ACCOUNT_ID}`;
            }

            log(LOG_LEVELS.DEBUG, 'login', 'Initiating login request', this.enrichContext({
                url,
                is_demo: this.isDemo()
            }, 'auth'));

            const response = await axios({
                method: 'POST',
                url,
                data,
                headers: {
                    'Content-Type': 'application/json',
                    'X-IG-API-KEY': this.apiKey,
                    'Version': '3'
                },
                validateStatus: null,
                httpsAgent: this.sslVerify ? undefined : new (require('https').Agent)({ rejectUnauthorized: false })
            });

            if (response.status === 200 && response.data.oauthToken?.access_token) {
                this.handleAuthResponse(response.data);
                this.accountId = response.data.currentAccountId || response.data.accountId || '';
                this.isInitialized = true;

                log(LOG_LEVELS.INFO, 'login', 'Login successful', this.enrichContext({
                    token_expiry: this.tokenExpiry.toISOString(),
                    base_url: this.baseUrl,
                    has_valid_token: this.hasValidToken,
                    is_initialized: this.isInitialized,
                    expires_in: response.data.oauthToken.expires_in || this.TOKEN_LIFETIME,
                    account_id: this.accountId
                }, 'auth'));
            } else {
                log(LOG_LEVELS.ERROR, 'login', 'Login failed', this.enrichContext({
                    error: 'Invalid auth response',
                    response_data: response.data,
                    status: response.status
                }, 'auth'));
            }

            await this.rateLimiter.logRequest({
                identifier: this.identifier,
                method: 'POST',
                endpoint: 'session',
                base_url: this.baseUrl,
                correlation_id: this.correlationId,
                response_code: response.status,
                response_time: Number(process.hrtime.bigint() - startTime) / 1e6,
                response_raw: response.data
            });

            logPerformance('login', startTime, {
                status: response.status,
                has_token: !!response.data.oauthToken?.access_token
            });

            return {
                code: response.status,
                response: response.data
            };

        } catch (error) {
            const responseTime = Number(process.hrtime.bigint() - startTime) / 1e6;
            
            log(LOG_LEVELS.ERROR, 'login', 'Login error', this.enrichContext({
                message: error.message,
                code: error.code,
                response: error.response?.data
            }, 'auth'));

            await this.rateLimiter.logRequest({
                identifier: this.identifier,
                method: 'POST',
                endpoint: 'session',
                base_url: this.baseUrl,
                correlation_id: this.correlationId,
                response_code: 401,
                response_time: responseTime
            });

            return {
                code: 401,
                response: { error: error.message }
            };
        } finally {
            this.isRefreshing = false;
        }
    }

    async makeRequest(method, endpoint, data = {}, version = 2) {
        const startTime = process.hrtime.bigint();
        
        try {
            if (!this.isInitialized || !this.hasValidToken || this.isTokenExpired()) {
                const loginResult = await this.login();
                if (loginResult.code !== 200) {
                    log(LOG_LEVELS.ERROR, 'makeRequest', 'Authentication failed', this.enrichContext({
                        method,
                        endpoint,
                        login_result: loginResult
                    }, 'api-request'));
                    return loginResult;
                }
                // Add small delay after authentication to prevent connection race conditions
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            await this.rateLimiter.checkRateLimit(this.identifier, method, endpoint);

            const agent = this.sslVerify ? undefined : new (require('https').Agent)({ 
                rejectUnauthorized: false,
                keepAlive: true,
                keepAliveMsecs: 1000,
                maxSockets: 10
            });

            const response = await axios({
                method,
                url: `${this.baseUrl}${endpoint}`,
                data: ['POST', 'PUT', 'DELETE'].includes(method) ? data : undefined,
                params: method === 'GET' ? data : undefined,
                headers: this.getHeaders(version),
                validateStatus: null,
                httpsAgent: agent,
                timeout: 5000, // 5 second timeout
                maxRedirects: 0
            });

            const responseTime = Number(process.hrtime.bigint() - startTime) / 1e6;

            await this.rateLimiter.logRequest({
                identifier: this.identifier,
                method,
                endpoint,
                base_url: this.baseUrl,
                correlation_id: this.correlationId,
                response_code: response.status,
                response_time: responseTime,
                response_raw: response.data
            });

            if (response.status === 401) {
                log(LOG_LEVELS.ERROR, 'makeRequest', 'Authentication error', this.enrichContext({
                    endpoint,
                    method,
                    has_valid_token: this.hasValidToken,
                    is_initialized: this.isInitialized,
                    token_expiry: this.tokenExpiry.toISOString()
                }, 'api-error'));

                if (await this.handleAuthError()) {
                    return this.makeRequest(method, endpoint, data, version);
                }
            }

            logPerformance('makeRequest', startTime, {
                method,
                endpoint,
                response_code: response.status,
                response_time: responseTime
            });

            return {
                code: response.status,
                response: response.data
            };

        } catch (error) {
            // Cleanup any hanging connections
            if (error.code === 'ERR_INTERNAL_ASSERTION') {
                log(LOG_LEVELS.ERROR, 'makeRequest', 'Internal Node.js assertion error - cleaning up connections', this.enrichContext({
                    method,
                    endpoint,
                    error: error.message
                }, 'api-error'));
                
                // Force cleanup of any hanging sockets
                if (this.agent) {
                    this.agent.destroy();
                }
            }

            const responseTime = Number(process.hrtime.bigint() - startTime) / 1e6;

            log(LOG_LEVELS.ERROR, 'makeRequest', 'Request error', this.enrichContext({
                method,
                endpoint,
                error: error.message,
                response_time: responseTime
            }, 'api-error'));

            await this.rateLimiter.logRequest({
                identifier: this.identifier,
                method,
                endpoint,
                base_url: this.baseUrl,
                correlation_id: this.correlationId,
                response_code: 500,
                response_time: responseTime,
                response_raw: error.message
            });

            return {
                code: 500,
                response: { error: error.message }
            };
        }
    }

    async handleAuthError() {
        const startTime = process.hrtime.bigint();
   
        if (this.isRefreshing || this.loginAttempts >= this.MAX_LOGIN_ATTEMPTS) {
            log(LOG_LEVELS.WARN, 'handleAuthError', 'Auth error handling blocked', this.enrichContext({
                reason: this.isRefreshing ? 'Already refreshing' : 'Max login attempts reached',
                login_attempts: this.loginAttempts
            }, 'auth'));
            return false;
        }

        this.isRefreshing = true;
        this.loginAttempts++;

        try {
            const loginResult = await this.login();
            if (loginResult.code === 200) {
                this.loginAttempts = 0;
                return true;
            }
            return false;
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'handleAuthError', 'Auth error handling failed', this.enrichContext({
                error: error.message,
                login_attempts: this.loginAttempts
            }, 'auth'));
            return false;
        } finally {
            this.isRefreshing = false;
        }
    }

    isTokenExpired() {
        if (!this.hasValidToken) {
            return true;
        }
        return this.tokenExpiry <= new Date();
    }

    handleAuthResponse(response) {
        if (!response.oauthToken?.access_token) {
            throw new Error('Invalid auth response - missing access token');
        }

        this.accessToken = response.oauthToken.access_token;
        const expiresIn = this.TOKEN_LIFETIME;
        this.tokenExpiry = new Date(Date.now() + expiresIn * 1000);
        this.hasValidToken = true;
        this.loginAttempts = 0;

        log(LOG_LEVELS.INFO, 'handleAuthResponse', 'Auth token updated', this.enrichContext({
            expires_in: expiresIn,
            expiry_time: this.tokenExpiry.toISOString(),
            token_present: Boolean(this.accessToken)
        }, 'auth'));
    }

    getHeaders(version) {
        const headers = {
            'Content-Type': 'application/json; charset=UTF-8',
            'Accept': 'application/json; charset=UTF-8',
            'X-IG-API-KEY': this.apiKey,
            'Version': version.toString()
        };

        if (this.accessToken) {
            headers['Authorization'] = `Bearer ${this.accessToken}`;
        }

        if (this.accountId) {
            headers['IG-ACCOUNT-ID'] = this.accountId;
        }

        return headers;
    }

    getRateLimiter() {
        return this.rateLimiter;
    }
}

module.exports = IGApiClient;