# Umbau Backend onlyUnread & onlyBookmarked

```
Erweitere die News-Ab<PERSON><PERSON> von getNewsFromDB, so dass weitere Parameter erlaubt sind, die allerdings optional sind d.h. wenn diese NULL sind bzw. nicht gesetzt, keinerlei Auswirkungen haben:
- <PERSON><PERSON><PERSON><PERSON> von <PERSON>, die is_read=0 d.h. "Filter: Nur ungelesene Nachrichten" und
- <PERSON><PERSON><PERSON><PERSON> von News, die is_bookmarked=1 d.h. "Filter: Nur Nachrichten mit Bookmark"

Beide Optionen können auch zusammen verwendet werden und sollten zu Satzfilter in der Datenbank-Abfrage führen!

@/controllers/database/news_controller.js @/controllers/database/queries/news_queries.js 
```

# Such-Funktionalität 

```
Erweitere die News-Abfrage von getNewsFromDB, so dass weitere Parameter erlaubt sind, die allerdings optional sind d.h. wenn diese NULL sind bzw. nicht gesetzt, keinerlei Auswirkungen haben:
- Suche nach News
  - News sollen nach ein oder mehrere Stichworten getrennt durch ein Komma durchsucht werden können (UND-Verknüpfung)  

```


# Umbau Frontend onlyUnread & onlyBookmarked

```
Der API-Endpunkt für News GET {{API_BASE_URL}}/api/v1/db/news?limit=10&days=2 hat nun zwei weitere optionale Parameter zur Vorfilterung:

- onlyBookmarked=true|false
- onlyUnread=true|false

Nutze das Backend zur Filterung ergänzend zur aktuellen Implementierung, damit deutlich weniger Daten an das Frontend gesendet werden!
```



# Umbau Frontend Suche

```
Der API-Endpunkt für News GET "{{API_BASE_URL}}/api/v1/db/news?limit=10&days=7&searchKeywords=Drohne,KI,Schwarm" hat einen weiteren Parameter searchKeywords.

Die News werden dabei mit dem im Parameter searchKeywords übergebenen Einträgen nach ein oder mehrere Stichworten getrennt durch ein Komma durchsucht werden können (UND-Verknüpfung)  

- Suche nach News
  

Nutze das Backend zur Filterung ergänzend zur aktuellen Implementierung, damit deutlich weniger Daten an das Frontend gesendet werden!
```