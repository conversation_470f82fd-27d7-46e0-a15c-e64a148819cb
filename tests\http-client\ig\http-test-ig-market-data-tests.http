@demo-symbol = CS.D.CFEGOLD.CFE.IP
@resolution = MINUTE_15
@from = 2025-03-20
@to = 2025-03-23

### GET Markets/Symbol-Infos
GET {{API_BASE_URL}}/api/v1/ig/markets/info?symbol={{demo-symbol}}
Content-Type: application/json

> {%
    client.test("Market Info Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Market Info contains required fields", function() {
        client.assert(response.body.hasOwnProperty('instrument'), "Missing instrument data");
        client.assert(response.body.hasOwnProperty('snapshot'), "Missing snapshot data");
        client.assert(response.body.hasOwnProperty('dealingRules'), "Missing dealing rules");
    });
%}

### GET Prices with Limit
GET {{API_BASE_URL}}/api/v1/ig/markets/prices?symbol={{demo-symbol}}&resolution={{resolution}}&limit=100
Content-Type: application/json

> {%
    client.test("Current Prices Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Price data contains required fields", function() {
        client.assert(response.body.hasOwnProperty('prices'), "Missing prices array");
        if(response.body.prices && response.body.prices.length > 0) {
            let price = response.body.prices[0];
            client.assert(price.hasOwnProperty('openPrice'), "Missing open price");
            client.assert(price.hasOwnProperty('closePrice'), "Missing close price");
            client.assert(price.hasOwnProperty('highPrice'), "Missing high price");
            client.assert(price.hasOwnProperty('lowPrice'), "Missing low price");
            client.assert(price.hasOwnProperty('lastTradedVolume'), "Missing volume");
        }
    });
%}

### GET Prices with Dates
GET {{API_BASE_URL}}/api/v1/ig/markets/prices/history?symbol={{demo-symbol}}&resolution={{resolution}}&from={{from}}&to={{to}}
Content-Type: application/json

> {%
    client.test("Historical Prices Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Historical data within requested range", function() {
        if(response.body.prices && response.body.prices.length > 0) {
            let firstDate = new Date(response.body.prices[0].snapshotTime);
            let lastDate = new Date(response.body.prices[response.body.prices.length-1].snapshotTime);
            let fromDate = new Date(client.global.get("from"));
            let toDate = new Date(client.global.get("to"));
            
            client.assert(firstDate >= fromDate, "Data starts before requested range");
            client.assert(lastDate <= toDate, "Data ends after requested range");
        }
    });
%}

### GET prices/last
GET {{API_BASE_URL}}/api/v1/ig/markets/prices/last?symbol={{demo-symbol}}&resolution={{resolution}}
Content-Type: application/json

> {%
    client.test("Last Candle Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Last Candle contains OHLC data", function() {
        client.assert(response.body.hasOwnProperty('openPrice'), "Missing open price");
        client.assert(response.body.hasOwnProperty('closePrice'), "Missing close price");
        client.assert(response.body.hasOwnProperty('highPrice'), "Missing high price");
        client.assert(response.body.hasOwnProperty('lowPrice'), "Missing low price");
    });
%}

