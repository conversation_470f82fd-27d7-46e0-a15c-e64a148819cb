const { executeQuery } = require('../../../services/database_service');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../../services/cache_service');
const { LOG_LEVELS, log, logPerformance } = require('../../../services/logging_service');
const { DatabaseError, ValidationError, errorHandler } = require('../errors/database_errors');
const { validateNumericParam, validateStringParam} = require('../../../services/validation_service');

/**
 * @swagger
 * /api/v1/statistics/weekday:
 *   get:
 *     summary: Get weekday statistics
 *     description: Retrieves trading statistics aggregated by weekday
 *     tags: [Statistics]
 *     parameters:
 *       - in: query
 *         name: refID
 *         required: true
 *         schema:
 *           type: string
 *         description: Account reference ID
 *       - in: query
 *         name: weeks
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 52
 *           default: 2
 *         description: Number of weeks to analyze
 *     responses:
 *       200:
 *         description: Weekday trading statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   refID:
 *                     type: string
 *                   week:
 *                     type: integer
 *                   dayOfWeek:
 *                     type: integer
 *                     minimum: 1
 *                     maximum: 7
 *                   cnt_trades:
 *                     type: integer
 *                   profit:
 *                     type: number
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Database error
 * 
 * /api/v1/statistics/dayhour:
 *   get:
 *     summary: Get hour of day statistics
 *     description: Retrieves trading statistics aggregated by hour of day
 *     tags: [Statistics]
 *     parameters:
 *       - in: query
 *         name: refID
 *         required: true
 *         schema:
 *           type: string
 *         description: Account reference ID
 *       - in: query
 *         name: weeks
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 52
 *           default: 2
 *         description: Number of weeks to analyze
 *     responses:
 *       200:
 *         description: Hour of day trading statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   refID:
 *                     type: string
 *                   week:
 *                     type: integer
 *                   hour:
 *                     type: integer
 *                     minimum: 0
 *                     maximum: 23
 *                   cnt_trades:
 *                     type: integer
 *                   profit:
 *                     type: number
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Database error
 * 
 * /api/v1/statistics/strategy_symbols:
 *   get:
 *     summary: Get strategy-symbol statistics
 *     description: Retrieves trading statistics aggregated by strategy and symbol
 *     tags: [Statistics]
 *     parameters:
 *       - in: query
 *         name: refID
 *         required: true
 *         schema:
 *           type: string
 *         description: Account reference ID
 *       - in: query
 *         name: months
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 12
 *           default: 2
 *         description: Number of months to analyze
 *     responses:
 *       200:
 *         description: Strategy-symbol trading statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   symbol:
 *                     type: string
 *                   strategy:
 *                     type: string
 *                   refId:
 *                     type: string
 *                   activate_ig_d1:
 *                     type: boolean
 *                   cnt_trades:
 *                     type: integer
 *                   profit:
 *                     type: number
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Database error
 * 
 * /api/v1/statistics/symbols_strategy:
 *   get:
 *     summary: Get symbol-strategy statistics
 *     description: Retrieves trading statistics aggregated by symbol and strategy
 *     tags: [Statistics]
 *     parameters:
 *       - in: query
 *         name: refID
 *         required: true
 *         schema:
 *           type: string
 *         description: Account reference ID
 *       - in: query
 *         name: months
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 12
 *           default: 2
 *         description: Number of months to analyze
 *     responses:
 *       200:
 *         description: Symbol-strategy trading statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   strategy:
 *                     type: string
 *                   symbol:
 *                     type: string
 *                   refId:
 *                     type: string
 *                   activate_ig_d1:
 *                     type: boolean
 *                   cnt_trades:
 *                     type: integer
 *                   profit:
 *                     type: number
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Database error
 */
class AnalysisController {
    /**
     * Lädt Wochentag-Statistiken
     */
    async getWeekDayStats(req, res) {
        const startTime = process.hrtime.bigint();
        try {
            const { refID, weeks = 2 } = req.query;
            
            if (!refID) {
                throw new ValidationError('refID is required');
            }

            // Validate parameters
            const validatedWeeks = validateNumericParam(weeks, {
                defaultValue: 2,
                min: 1,
                max: 52,
                paramName: 'weeks'
            });

            const validatedRefID = validateStringParam(refID, {
                required: true,
                minLength: 2,
                maxLength: 50,
                paramName: 'refID'
            });


            log(LOG_LEVELS.INFO, 'getWeekDayStats', 'Starting weekday statistics retrieval', {
                refID,
                weeks: validatedWeeks
            });
            return await withCacheWrapper(
                'TRADE',
                'getWeekDayStats',
                async () => {
                    const query = {
                        sql: 'WITH weekdays AS (' +
                             'SELECT 1 AS dayOfWeek ' +
                             'UNION ALL SELECT 2 ' +
                             'UNION ALL SELECT 3 ' +
                             'UNION ALL SELECT 4 ' +
                             'UNION ALL SELECT 5 ' +
                             'UNION ALL SELECT 6 ' +
                             'UNION ALL SELECT 7' +
                             ') ' +
                             'SELECT a.refID, WEEKOFYEAR(x.exit_time) as week, ' +
                             'w.dayOfWeek, IFNULL(COUNT(x.profit),0) as cnt_trades, ' +
                             'IFNULL(round(SUM(x.profit),0),0) as profit ' +
                             'FROM weekdays as w ' +
                             'LEFT JOIN `trades_history` x ON date(x.exit_time)>DATE_SUB(CURDATE(),INTERVAL ? week) ' +
                             'AND DAYOFWEEK(x.exit_time)=w.dayofWeek ' +
                             'LEFT JOIN `accounts` a ON a.refID=? AND x.account=a.account_id ' +
                             'WHERE a.refID=? ' +
                             'GROUP BY a.refID, WEEKOFYEAR(x.exit_time), w.dayOfWeek',
                        bigIntAsNumber: true,
                        timezone: 'de_de'
                    };

                    const result = await executeQuery(query, [weeks, refID, refID]);
                    res.send(result);
                },
                [refID, weeks]
            );
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getWeekDayStats', 'Failed to get weekday statistics', { error: err });
            return errorHandler(err, 'getWeekDayStats');
        }
    }

    /**
     * Lädt Tagesstunden-Statistiken
     */
    async getDayHourStats(req, res) {
        const startTime = process.hrtime.bigint();
        try {
            const { refID, weeks = 2 } = req.query;
            
            if (!refID) {
                throw new ValidationError('refID is required');
            }

            // Validate parameters
            const validatedWeeks = validateNumericParam(weeks, {
                defaultValue: 2,
                min: 1,
                max: 52,
                paramName: 'weeks'
            });

            const validatedRefID = validateStringParam(refID, {
                required: true,
                minLength: 2,
                maxLength: 50,
                paramName: 'refID'
            });


            log(LOG_LEVELS.INFO, 'getDayHourStats', 'Starting day hour statistics retrieval', {
                refID: validatedRefID,
                weeks: validatedWeeks
            });
            return await withCacheWrapper(
                'TRADE',
                'getDayHourStats',
                async () => {
                    const query = {
                        sql: 'WITH hours AS (' +
                             Array.from({length: 24}, (_, i) => 
                                 i === 0 ? 'SELECT 0 AS hour' : 'UNION ALL SELECT ' + i
                             ).join(' ') +
                             ') ' +
                             'SELECT a.refID, WEEKOFYEAR(x.entry_time) as week, hours.hour, ' +
                             'IFNULL(COUNT(x.profit),0) as cnt_trades, ' +
                             'IFNULL(round(SUM(x.profit),0),0) as profit ' +
                             'FROM hours ' +
                             'LEFT JOIN `trades_history` x ON date(x.entry_time)>DATE_SUB(CURDATE(),INTERVAL ? week) ' +
                             'AND HOUR(x.entry_time)=hours.hour ' +
                             'LEFT JOIN `accounts` a ON a.refID=? AND x.account=a.account_id ' +
                             'WHERE a.refID=? ' +
                             'GROUP BY a.refID, WEEKOFYEAR(x.entry_time), hours.hour',
                        bigIntAsNumber: true,
                        timezone: 'de_de'
                    };

                    const result =  await executeQuery(query, [weeks, refID, refID]);
                    res.send(result);
                },
                [refID, weeks]
            );
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getDayHourStats', 'Failed to get day hour statistics', { error: err });
            return errorHandler(err, 'getDayHourStats');
        }
    }

    /**
     * Lädt Symbol-Strategie-Statistiken
     */
    async getSymbolStrategyStats(req, res) {
        const startTime = process.hrtime.bigint();
        try {
            const { refID, months = 2 } = req.query;
            
            if (!refID) {
                throw new ValidationError('refID is required');
            }

            // Validate parameters
            const validatedMonths = validateNumericParam(months, {
                defaultValue: 2,
                min: 1,
                max: 12,
                paramName: 'months'
            });

            const validatedRefID = validateStringParam(refID, {
                required: true,
                minLength: 2,
                maxLength: 50,
                paramName: 'refID'
            });


            log(LOG_LEVELS.INFO, 'getSymbolStrategyStats', 'Starting symbol strategy statistics retrieval', {
                refID: validatedRefID,
                months: validatedMonths
            });
            return await withCacheWrapper(
                'TRADE',
                'getSymbolStrategyStats',
                async () => {
                    const query = {
                        sql: `SELECT s.symbol, s.strategy, IFNULL(acc.refId,?) as refId, 
                                     IFNULL(t.activate_ig_d1,0) AS activate_ig_d1, 
                                     IFNULL(t.activate_ig_p1,0) AS activate_ig_p1, 
                                     IFNULL(COUNT(xtb.profit),0) AS cnt_trades, 
                                     IFNULL(ROUND(SUM(xtb.profit),0),0) AS profit
                                      FROM \`strategy_symbols\` AS s
                                 LEFT JOIN \`strategy_toggle\` AS t ON t.strategy = s.strategy AND t.symbol = s.symbol 
                                 LEFT JOIN \`accounts\` AS acc ON refID=? 
                                 LEFT JOIN \`trades_history\` AS xtb ON xtb.strategy=s.strategy 
                                                                       AND xtb.symbol=s.symbol 
                                                                       AND xtb.timeframe=t.timeframe 
                                                                       AND xtb.refID=acc.refId 
                                                                       AND DATE(xtb.exit_time)> DATE_SUB(CURDATE(), INTERVAL ? month) 
                                   GROUP BY s.symbol, s.strategy, acc.refId HAVING cnt_trades>0`,
                        bigIntAsNumber: true,
                        timezone: 'de_de'
                    };

                    const result = await executeQuery(query, [refID, refID, months]);
                    res.send(result);
                },
                [refID, months]
            );
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getSymbolStrategyStats', 'Failed to get symbol strategy statistics', { error: err });
            return errorHandler(err, 'getSymbolStrategyStats');
        }
    }

    /**
     * Lädt Strategie-Symbol-Statistiken
     */
    async getStrategySymbolStats(req, res) {
        const startTime = process.hrtime.bigint();
        try {
            const { refID, months = 2 } = req.query;
            
            if (!refID) {
                throw new ValidationError('refID is required');
            }

            // Validate parameters
            const validatedMonths = validateNumericParam(months, {
                defaultValue: 2,
                min: 1,
                max: 12,
                paramName: 'months'
            });

            const validatedRefID = validateStringParam(refID, {
                required: true,
                minLength: 2,
                maxLength: 50,
                paramName: 'refID'
            });


            log(LOG_LEVELS.INFO, 'getStrategySymbolStats', 'Starting strategy symbol statistics retrieval', {
                refID: validatedRefID,
                months: validatedMonths
            });
            return await withCacheWrapper(
                'TRADE',
                'getStrategySymbolStats',
                async () => {
                    const query = {
                        sql: `SELECT s.strategy,
                                     s.symbol,
                                     IFNULL(acc.refId, ?)                 as refId,
                                     IFNULL(t.activate_ig_d1, 0)             AS activate_ig_d1,
                                     IFNULL(COUNT(xtb.profit), 0)         AS cnt_trades,
                                     IFNULL(ROUND(SUM(xtb.profit), 0), 0) AS profit
                              FROM \`strategy_symbols\` AS s
                                       LEFT JOIN \`strategy_toggle\` AS t
                                                 ON t.strategy = s.strategy AND t.symbol = s.symbol
                                       LEFT JOIN \`accounts\` AS acc ON refID = ?
                                       LEFT JOIN \`trades_history\` AS xtb
                                                 ON xtb.strategy = s.strategy AND xtb.symbol = s.symbol  AND xtb.timeframe=t.timeframe
                                                     AND DATE(xtb.exit_time) > DATE_SUB(CURDATE(), INTERVAL ? MONTH)
                                                     AND acc.account_id = xtb.account
                              GROUP BY s.strategy, s.symbol, acc.refId
                              HAVING cnt_trades > 0`,
                        bigIntAsNumber: true,
                        timezone: 'de_de'
                    };

                    const result = await executeQuery(query, [refID, refID, months]);
                    res.send(result);

                },
                [refID, months]
            );
        } catch (err) {
            log(LOG_LEVELS.ERROR, 'getStrategySymbolStats', 'Failed to get strategy symbol statistics', { error: err });
            return errorHandler(err, 'getStrategySymbolStats');
        }
    }
}

module.exports = new AnalysisController();
