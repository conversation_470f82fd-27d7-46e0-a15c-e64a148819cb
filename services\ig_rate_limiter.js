const NodeCache = require('node-cache');
const moment = require('moment-timezone');
const { pool } = require('../configs/config_db');
const { LOG_LEVELS, log, logPerformance } = require('./logging_service');

class IGRateLimiter {
    constructor() {
        this.cachedLimits = new NodeCache({ stdTTL: 300 }); // Cache for 5 minutes
        this.lastLimitCheck = moment().tz('Europe/Berlin').format('YYYY-MM-DD HH:mm:ss');
    }

    async loadRateLimits() {
        const startTime = process.hrtime.bigint();
        try {
            const connection = await pool.getConnection();
            const query = 'SELECT * FROM rate_limits WHERE updated_at > ?';
            const rows = await connection.query(query, [this.lastLimitCheck]);

            rows.forEach(row => {
                const key = this.getLimitKey(row.limit_type, row.identifier, row.endpoint);
                this.cachedLimits.set(key, row);
            });

            this.lastLimitCheck = moment().tz('Europe/Berlin').format('YYYY-MM-DD HH:mm:ss');
            connection.release();

            logPerformance('loadRateLimits', startTime, {
                limits_loaded: rows.length,
                cache_size: this.cachedLimits.keys().length
            });
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'loadRateLimits', 'Failed to load rate limits', {
                error: error.message,
                component: 'rate-limiter'
            });
        }
    }

    getLimitKey(type, identifier = '', endpoint = '') {
        return `${type}:${identifier}:${endpoint}`;
    }

    async calculateWaitTime(limit, conditions = {}) {
        const startTime = process.hrtime.bigint();
        if (!limit) return 0;

        try {
            const connection = await pool.getConnection();
            let query = `SELECT
                        MAX(timestamp) as latest_request,
                        MIN(timestamp) as earliest_request,
                        COUNT(*) as request_count
                        FROM api_requests
                        WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? SECOND)`;

            const params = [limit.time_window];

            Object.entries(conditions).forEach(([field, value]) => {
                query += ` AND ${field} = ?`;
                params.push(value);
            });

            const [result] = await connection.query(query, params);
            connection.release();

            // If we haven't reached the limit yet, no need to wait
            if (result.request_count < limit.max_requests) {
                return 0;
            }

            // Calculate the ideal time between requests
            const timePerRequest = limit.time_window / limit.max_requests;
            
            // Get current time in Berlin timezone
            const now = moment().tz('Europe/Berlin');
            
            // Calculate when the next request should be allowed based on the rate limit
            // This distributes requests evenly across the time window
            if (result.earliest_request && result.latest_request) {
                // Calculate time elapsed since the earliest request in this window
                const earliestTime = moment(result.earliest_request);
                const latestTime = moment(result.latest_request);
                
                // Calculate how many requests we should have been able to make by now
                const timeElapsed = now.diff(earliestTime, 'seconds');
                const allowedRequestsByNow = Math.floor(timeElapsed / timePerRequest) + 1; // +1 for the initial request
                
                if (result.request_count > allowedRequestsByNow) {
                    // We've made more requests than we should have by now
                    // Calculate when the next request should be allowed
                    const nextAllowedTime = earliestTime.clone().add((result.request_count) * timePerRequest, 'seconds');
                    const waitTime = Math.max(0.1, nextAllowedTime.diff(now, 'seconds', true));
                    
                    logPerformance('calculateWaitTime', startTime, {
                        request_count: result.request_count,
                        limit_max: limit.max_requests,
                        time_per_request: timePerRequest,
                        allowed_by_now: allowedRequestsByNow,
                        wait_time: waitTime,
                        conditions: JSON.stringify(conditions)
                    });
                    
                    return waitTime;
                }
            }
            
            // Fallback calculation if we can't determine a precise wait time
            // This is more conservative but still better than the fixed 3-5 seconds
            const waitTime = Math.max(0.1, timePerRequest);
            
            logPerformance('calculateWaitTime', startTime, {
                request_count: result.request_count,
                limit_max: limit.max_requests,
                time_per_request: timePerRequest,
                fallback: true,
                wait_time: waitTime,
                conditions: JSON.stringify(conditions)
            });
            
            return waitTime;
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'calculateWaitTime', 'Error calculating wait time', {
                error: error.message,
                component: 'rate-limiter'
            });
            return 1; // Default to minimum wait time on error
        }
    }

    async checkRateLimit(identifier, method, endpoint) {
        const startTime = process.hrtime.bigint();
        try {
            await this.loadRateLimits();
            let waitTime = 0;
            let appliedLimitType = null;
            let appliedLimit = null;

            // Hierarchical rate limiting: Endpoint-specific > Identifier-specific > Global
            // Check endpoint-specific limit first (highest priority)
            const endpointLimit = await this.getRateLimit('per_endpoint', null, endpoint);
            if (endpointLimit) {
                waitTime = await this.calculateWaitTime(endpointLimit, { endpoint });
                appliedLimitType = 'endpoint-specific';
                appliedLimit = endpointLimit;
                log(LOG_LEVELS.DEBUG, 'checkRateLimit', 'Applied endpoint-specific rate limit', {
                    identifier,
                    endpoint,
                    max_requests: endpointLimit.max_requests,
                    time_window: endpointLimit.time_window,
                    wait_time: waitTime,
                    component: 'rate-limiter'
                });
            }
            // Check identifier-specific limit if no endpoint-specific limit found
            else {
                const identifierLimit = await this.getRateLimit('per_identifier', identifier);
                if (identifierLimit) {
                    waitTime = await this.calculateWaitTime(identifierLimit, { identifier });
                    appliedLimitType = 'identifier-specific';
                    appliedLimit = identifierLimit;
                    log(LOG_LEVELS.DEBUG, 'checkRateLimit', 'Applied identifier-specific rate limit', {
                        identifier,
                        endpoint,
                        max_requests: identifierLimit.max_requests,
                        time_window: identifierLimit.time_window,
                        wait_time: waitTime,
                        component: 'rate-limiter'
                    });
                }
                // Check global limit if no identifier-specific limit found
                else {
                    const globalLimit = await this.getRateLimit('global');
                    if (globalLimit) {
                        waitTime = await this.calculateWaitTime(globalLimit);
                        appliedLimitType = 'global';
                        appliedLimit = globalLimit;
                        log(LOG_LEVELS.DEBUG, 'checkRateLimit', 'Applied global rate limit', {
                            identifier,
                            endpoint,
                            max_requests: globalLimit.max_requests,
                            time_window: globalLimit.time_window,
                            wait_time: waitTime,
                            component: 'rate-limiter'
                        });
                    }
                }
            }

            if (waitTime > 0) {
                log(LOG_LEVELS.INFO, 'checkRateLimit', 'Rate limit exceeded, waiting before next request', {
                    identifier,
                    endpoint, 
                    wait_time: waitTime,
                    applied_limit_type: appliedLimitType,
                    max_requests: appliedLimit ? appliedLimit.max_requests : null,
                    time_window: appliedLimit ? appliedLimit.time_window : null,
                    component: 'rate-limiter'
                });

                return new Promise(resolve => setTimeout(resolve, waitTime * 1000));
            }

            logPerformance('checkRateLimit', startTime, {
                identifier,
                endpoint,
                method,
                wait_time: waitTime,
                applied_limit_type: appliedLimitType || 'none'
            });
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'checkRateLimit', 'Error checking rate limit', {
                error: error.message,
                identifier,
                endpoint,
                component: 'rate-limiter'
            });
        }
    }
 
    async logRequest(requestData) {
        const startTime = process.hrtime.bigint();
        try {
            const connection = await pool.getConnection();
            const berlinTimestamp = moment().tz('Europe/Berlin').format('YYYY-MM-DD HH:mm:ss');
            
            const query = `INSERT INTO api_requests (
                identifier,
                method,
                endpoint,
                base_url,
                correlation_id,
                response_code,
                response_time,
                response_raw,
                source,
                timestamp
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

            await connection.query(query, [
                requestData.identifier,
                requestData.method,
                requestData.endpoint,
                requestData.base_url,
                requestData.correlation_id,
                requestData.response_code,
                requestData.response_time,
                requestData.response_raw,
                requestData.source || 'mlalgotrader-api',
                berlinTimestamp
            ]);

            connection.release();

            logPerformance('logRequest', startTime, {
                identifier: requestData.identifier,
                endpoint: requestData.endpoint,
                response_code: requestData.response_code,
                response_time: requestData.response_time,
                timestamp: berlinTimestamp
            });
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'logRequest', 'Error logging API request', {
                error: error.message,
                requestData,
                component: 'rate-limiter'
            });
        }
    }

    async getRateLimit(type, identifier = null, endpoint = null) {
        const startTime = process.hrtime.bigint();
        try {
            const connection = await pool.getConnection();
            let query = 'SELECT * FROM rate_limits WHERE limit_type = ?';
            const params = [type];

            if (identifier !== null) {
                query += ' AND (identifier = ? OR identifier IS NULL)';
                params.push(identifier);
            }

            if (endpoint !== null) {
                query += ' AND (endpoint = ? OR endpoint IS NULL)';
                params.push(endpoint);
            }

            query += ' ORDER BY identifier IS NULL, endpoint IS NULL LIMIT 1';

            const [limit] = await connection.query(query, params);
            connection.release();

            logPerformance('getRateLimit', startTime, {
                type,
                identifier,
                endpoint,
                found: !!limit
            });

            return limit || null;
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'getRateLimit', 'Error getting rate limit', {
                error: error.message,
                type,
                identifier,
                endpoint,
                component: 'rate-limiter'
            });
            return null;
        }
    }

    async isWithinLimit(limit, conditions = {}) {
        const startTime = process.hrtime.bigint();
        if (!limit) return true;

        try {
            const connection = await pool.getConnection();
            let query = `SELECT COUNT(*) as request_count 
                        FROM api_requests 
                        WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? SECOND)`;

            const params = [limit.time_window];

            Object.entries(conditions).forEach(([field, value]) => {
                query += ` AND ${field} = ?`;
                params.push(value);
            });

            const [result] = await connection.query(query, params);
            connection.release();

            const isWithin = result.request_count < limit.max_requests;

            logPerformance('isWithinLimit', startTime, {
                request_count: result.request_count,
                max_requests: limit.max_requests,
                is_within: isWithin,
                conditions: JSON.stringify(conditions)
            });

            return isWithin;
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'isWithinLimit', 'Error checking if within limit', {
                error: error.message,
                component: 'rate-limiter'
            });
            return true; // Allow request on error
        }
    }

    getActiveRateLimits() {
        return Object.values(this.cachedLimits.mget(this.cachedLimits.keys()));
    }
}

module.exports = IGRateLimiter;