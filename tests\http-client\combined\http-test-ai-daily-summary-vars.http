###

// @name Get AI Daily Summary Variables
GET {{API_BASE_URL}}/api/v1/ai_daily_summary_vars?refID=P1&days=0
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("AI Daily Summary Variables Test");
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Response has correct structure", function() {
        var jsonData = JSON.parse(response.body);
        client.assert(jsonData.hasOwnProperty('trade_history'), "Response does not have trade_history");
        client.assert(jsonData.hasOwnProperty('latest_factor_map'), "Response does not have latest_factor_map");
        client.assert(jsonData.hasOwnProperty('calendar_list'), "Response does not have calendar_list");
    });

    client.test("Trade history has correct properties", function() {
        var jsonData = JSON.parse(response.body);
        if (jsonData.trade_history.length > 0) {
            var firstTrade = jsonData.trade_history[0];
            client.assert(firstTrade.hasOwnProperty('entry_time'), "Trade does not have entry_time");
            client.assert(firstTrade.hasOwnProperty('symbol'), "Trade does not have symbol");
            client.assert(firstTrade.hasOwnProperty('strategy'), "Trade does not have strategy");
            client.assert(firstTrade.hasOwnProperty('trade_volume'), "Trade does not have trade_volume");
            client.assert(firstTrade.hasOwnProperty('profit'), "Trade does not have profit");
        }
    });

    client.test("Latest factor map has correct properties", function() {
        var jsonData = JSON.parse(response.body);
        var factorMap = jsonData.latest_factor_map;
        client.assert(factorMap.hasOwnProperty('uuid'), "Factor map does not have uuid");
        client.assert(factorMap.hasOwnProperty('toVerify'), "Factor map does not have toVerify");
        client.assert(factorMap.hasOwnProperty('time'), "Factor map does not have time");
        client.assert(factorMap.hasOwnProperty('title'), "Factor map does not have title");
        client.assert(factorMap.hasOwnProperty('body'), "Factor map does not have body");
        client.assert(factorMap.hasOwnProperty('score'), "Factor map does not have score");
    });

    client.test("Calendar list has correct properties", function() {
        var jsonData = JSON.parse(response.body);
        if (jsonData.calendar_list.length > 0) {
            var firstEvent = jsonData.calendar_list[0];
            client.assert(firstEvent.hasOwnProperty('country'), "Event does not have country");
            client.assert(firstEvent.hasOwnProperty('current'), "Event does not have current");
            client.assert(firstEvent.hasOwnProperty('forecast'), "Event does not have forecast");
            client.assert(firstEvent.hasOwnProperty('impact'), "Event does not have impact");
            client.assert(firstEvent.hasOwnProperty('period'), "Event does not have period");
            client.assert(firstEvent.hasOwnProperty('previous'), "Event does not have previous");
            client.assert(firstEvent.hasOwnProperty('timestamp'), "Event does not have timestamp");
            client.assert(firstEvent.hasOwnProperty('title'), "Event does not have title");
        }
    });
%}

###
