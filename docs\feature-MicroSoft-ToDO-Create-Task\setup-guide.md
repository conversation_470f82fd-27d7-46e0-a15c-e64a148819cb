# Microsoft ToDo Integration - Setup Guide

## Übersicht

Diese Anleitung beschreibt die Einrichtung und Konfiguration der Microsoft ToDo Integration für die Algotrader-API.

## Voraussetzungen

### 1. Microsoft Azure App Registration

1. **Azure Portal öffnen**: <PERSON><PERSON><PERSON> zu [portal.azure.com](https://portal.azure.com)
2. **App Registration erstellen**:
   - Navigieren Sie zu "Azure Active Directory" > "App registrations"
   - Klicken Sie auf "New registration"
   - Name: `Algotrader-API-Microsoft-ToDo`
   - Supported account types: "Accounts in this organizational directory only"
   - Redirect URI: Nicht erforderlich für Client Credentials Flow

3. **API Permissions konfigurieren**:
   - Geh<PERSON> Sie zu "API permissions"
   - Klicken Sie auf "Add a permission"
   - Wählen Sie "Microsoft Graph"
   - Wählen Sie "Application permissions"
   - Fügen Sie folgende Permissions hinzu:
     - `Tasks.ReadWrite` - Zum Lesen und Schreiben von Tasks
     - `Tasks.ReadWrite.All` - Für alle Benutzer (falls erforderlich)

   **Wichtig**: Die Anwendung verwendet OAuth 2.0 Client Credentials Flow mit dem Scope `https://graph.microsoft.com/.default`, welcher automatisch alle konfigurierten Application Permissions einschließt.

4. **Client Secret erstellen**:
   - Gehen Sie zu "Certificates & secrets"
   - Klicken Sie auf "New client secret"
   - Description: `Algotrader-API-Secret`
   - Expires: 24 months (empfohlen)
   - **Wichtig**: Kopieren Sie den Secret-Wert sofort!

5. **Admin Consent erteilen**:
   - Gehen Sie zurück zu "API permissions"
   - Klicken Sie auf "Grant admin consent for [Your Organization]"

### 2. Erforderliche Informationen sammeln

Nach der App Registration benötigen Sie:

- **Tenant ID**: Zu finden unter "Azure Active Directory" > "Properties" > "Tenant ID"
- **Client ID**: Zu finden in Ihrer App Registration unter "Overview" > "Application (client) ID"
- **Client Secret**: Der Wert, den Sie in Schritt 4 kopiert haben

## Environment Variables

Fügen Sie folgende Umgebungsvariablen zu Ihrer `.env` Datei hinzu:

```env
# Microsoft ToDo Integration
MICROSOFT_TENANT_ID=your-tenant-id-here
MICROSOFT_CLIENT_ID=your-client-id-here
MICROSOFT_CLIENT_SECRET=your-client-secret-here
```

### Beispiel-Werte

```env
# Beispiel (verwenden Sie Ihre echten Werte!)
MICROSOFT_TENANT_ID=12345678-1234-1234-1234-123456789012
MICROSOFT_CLIENT_ID=87654321-4321-4321-4321-210987654321
MICROSOFT_CLIENT_SECRET=abcdef123456789~abcdef123456789
```

## API Endpunkte

### 1. Task erstellen

**Endpoint**: `POST /api/v1/microsoft/todo/tasks`

#### "Mein Tag" Integration

Tasks, die mit `useDefaultList: true` erstellt werden, erscheinen automatisch in der Standard-Liste von Microsoft ToDo. Diese Standard-Liste ist mit der "Mein Tag"-Ansicht verknüpft, wodurch Tasks automatisch in der täglichen Übersicht sichtbar werden.

**Vorteile der Standard-Liste:**
- Tasks erscheinen automatisch in "Mein Tag"
- Keine manuelle Liste-ID erforderlich
- Vereinfachte API-Nutzung
- Konsistente Erfahrung über alle Microsoft ToDo-Clients

**Headers**:
```
Content-Type: application/json
Authorization: Bearer YOUR_API_KEY
```

**Request Body**:

**Option 1: Task in Standard-Liste erstellen (erscheint in "Mein Tag")**:
```json
{
  "title": "Neue Aufgabe",
  "body": "Detaillierte Beschreibung der Aufgabe",
  "dueDateTime": "2024-12-31T23:59:59.000Z",
  "importance": "high",
  "categories": ["Arbeit", "Wichtig"],
  "timeZone": "Europe/Berlin",
  "useDefaultList": true
}
```

**Option 2: Task in spezifischer Liste erstellen**:
```json
{
  "listId": "AQMkADAwATM0MDAAMS1iNTcwLWI2NTEtMDACLTAwCgAuAAADiQ8RE6fqaEOyolDoO-lHwwEA",
  "title": "Neue Aufgabe",
  "body": "Detaillierte Beschreibung der Aufgabe",
  "dueDateTime": "2024-12-31T23:59:59.000Z",
  "importance": "high",
  "categories": ["Arbeit", "Wichtig"],
  "timeZone": "Europe/Berlin",
  "useDefaultList": false
}
```

**Parameter**:
- `title` (string, required): Aufgaben-Titel
- `listId` (string, optional): Microsoft ToDo Liste-ID (nur erforderlich wenn `useDefaultList` = false)
- `useDefaultList` (boolean, optional):
  - `true`: Erstellt Task in Standard-Liste (erscheint in "Mein Tag")
  - `false`: Erfordert explizite `listId`
  - Standard: `true` wenn `listId` nicht angegeben
- `body` (string, optional): Aufgaben-Beschreibung
- `dueDateTime` (string, optional): Fälligkeitsdatum im ISO 8601 Format
- `importance` (string, optional): Wichtigkeit (`low`, `normal`, `high`)
- `categories` (array, optional): Kategorien für die Aufgabe
- `timeZone` (string, optional): Zeitzone für Fälligkeitsdatum (Standard: UTC)

**Response**:
```json
{
  "success": true,
  "message": "Task created successfully",
  "data": {
    "id": "AQMkADAwATM0MDAAMS1iNTcwLWI2NTEtMDACLTAwCgAuAAADiQ8RE6fqaEOyolDoO-lHwwEA",
    "title": "Neue Aufgabe",
    "status": "notStarted",
    "importance": "high",
    "createdDateTime": "2024-01-15T10:30:00.000Z",
    "lastModifiedDateTime": "2024-01-15T10:30:00.000Z",
    "dueDateTime": "2024-12-31T23:59:59.000Z",
    "categories": ["Arbeit", "Wichtig"],
    "webUrl": "https://to-do.office.com/tasks/id/...",
    "listId": "AQMkADAwATM0MDAAMS1iNTcwLWI2NTEtMDACLTAwCgAuAAADiQ8RE6fqaEOyolDoO-lHwwEA",
    "listInfo": {
      "id": "AQMkADAwATM0MDAAMS1iNTcwLWI2NTEtMDACLTAwCgAuAAADiQ8RE6fqaEOyolDoO-lHwwEA",
      "displayName": "Tasks",
      "wellKnownListName": "defaultList",
      "isDefaultList": true
    }
  },
  "meta": {
    "requestId": "req_1705312200000_abc123",
    "processingTime": 245,
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### 2. Performance Metrics abrufen

**Endpoint**: `GET /api/v1/microsoft/todo/metrics`

**Response**:
```json
{
  "success": true,
  "message": "Performance metrics retrieved successfully",
  "data": {
    "totalRequests": 150,
    "successfulRequests": 147,
    "failedRequests": 3,
    "averageResponseTime": 234,
    "cacheHits": 45,
    "cacheMisses": 105,
    "cacheHitRate": "30.00%",
    "successRate": "98.00%",
    "activeRequests": 2,
    "queuedRequests": 0
  },
  "meta": {
    "requestId": "req_1705312200000_def456",
    "processingTime": 12,
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

## Validierung und Limits

### Request Validierung

- **listId**: Erforderlich, muss eine gültige Microsoft ToDo List ID sein
- **title**: Erforderlich, maximal 255 Zeichen
- **body**: Optional, maximal 4000 Zeichen
- **dueDateTime**: Optional, muss ISO 8601 Format haben
- **importance**: Optional, Werte: `low`, `normal`, `high`
- **categories**: Optional, Array von Strings, maximal 25 Einträge
- **timeZone**: Optional, gültige Zeitzone (z.B. "Europe/Berlin")

### Rate Limits

- **Microsoft Graph API**: 10.000 Requests pro 10 Minuten pro App
- **Algotrader-API**: 50 Requests pro 15 Minuten pro IP/API-Key Kombination
- **Token Refresh**: Maximal 3 Versuche alle 30 Sekunden

## Sicherheit

### Credential Management

- Client Secrets werden sicher validiert und maskiert in Logs
- Automatische Rotation-Empfehlungen bei schwachen Credentials
- Audit-Logging für alle Credential-Zugriffe

### Request Security

- SSL/TLS 1.2+ Enforcement
- Input Sanitization gegen XSS und Injection Attacks
- Security Headers (X-Content-Type-Options, X-Frame-Options, etc.)
- Rate Limiting auf mehreren Ebenen

### Caching Security

- Token-Cache mit automatischer Expiration
- Sichere Cache-Schlüssel ohne sensitive Daten
- Maximale Cache-Größe zur Speicher-Begrenzung

## Monitoring und Debugging

### Logging

Alle Requests werden strukturiert geloggt:

```json
{
  "level": "info",
  "message": "Microsoft ToDo task created successfully",
  "requestId": "req_1705312200000_abc123",
  "listId": "AQMkADAwATM0MDAAMS1iNTcwLWI2NTEtMDACLTAwCgAuAAADiQ8RE6fqaEOyolDoO-lHwwEA",
  "taskId": "AQMkADAwATM0MDAAMS1iNTcwLWI2NTEtMDACLTAwCgAuAAADiQ8RE6fqaEOyolDoO-lHwwEA",
  "processingTime": 245,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Performance Monitoring

- Response-Zeit Tracking
- Cache Hit/Miss Ratios
- Success/Failure Rates
- Aktive und wartende Requests
- Automatische Performance-Metriken

### Error Tracking

Alle Fehler werden kategorisiert und geloggt:

- **MicrosoftGraphError**: Allgemeine Microsoft Graph API Fehler
- **MicrosoftOAuthError**: OAuth/Authentication Fehler
- **MicrosoftRateLimitError**: Rate Limiting Fehler
- **MicrosoftResourceNotFoundError**: Ressource nicht gefunden
- **ValidationError**: Input-Validierung Fehler

## Troubleshooting

### Häufige Probleme

1. **401 Unauthorized**
   - Prüfen Sie Tenant ID, Client ID und Client Secret
   - Stellen Sie sicher, dass Admin Consent erteilt wurde
   - Überprüfen Sie die API Permissions

2. **403 Forbidden**
   - Überprüfen Sie die API Permissions in Azure
   - Stellen Sie sicher, dass `Tasks.ReadWrite` Permission vorhanden ist
   - Prüfen Sie, ob Admin Consent erteilt wurde

3. **404 Not Found**
   - Überprüfen Sie die List ID
   - Stellen Sie sicher, dass die Liste existiert und zugänglich ist

4. **429 Too Many Requests**
   - Rate Limit erreicht, warten Sie und versuchen Sie es erneut
   - Überprüfen Sie die `Retry-After` Header

5. **500 Internal Server Error**
   - Überprüfen Sie die Server-Logs
   - Stellen Sie sicher, dass alle Environment Variables gesetzt sind
   - Prüfen Sie die Netzwerkverbindung zu Microsoft Graph API

### Debug-Modus

Für detailliertes Debugging setzen Sie:

```env
LOG_LEVEL=debug
```

Dies aktiviert zusätzliche Debug-Logs für:
- OAuth Token Requests
- Microsoft Graph API Calls
- Cache Operations
- Performance Metrics
- Security Events

## Deployment

### Produktionsumgebung

1. **Environment Variables sicher setzen**
2. **SSL/TLS Zertifikate konfigurieren**
3. **Monitoring und Alerting einrichten**
4. **Backup-Strategien für Credentials implementieren**
5. **Rate Limiting Konfiguration anpassen**

### Health Checks

Der Service bietet Health Check Endpunkte:

- **Service Status**: Über Performance Metrics Endpoint
- **Token Validity**: Automatische Token-Validierung
- **API Connectivity**: Regelmäßige Connectivity-Tests

## Support

Bei Problemen oder Fragen:

1. Überprüfen Sie die Logs auf Fehlermeldungen
2. Verwenden Sie die Performance Metrics für Diagnose
3. Konsultieren Sie die Microsoft Graph API Dokumentation
4. Kontaktieren Sie das Entwicklungsteam mit detaillierten Logs
