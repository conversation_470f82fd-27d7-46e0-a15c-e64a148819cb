{"paths": {"/api/v1/db/risk_management/state": {"get": {"summary": "Aktuellen Risikomanagement-Status abrufen", "description": "Ruft den aktuellen Risikomanagement-Status für ein bestimmtes Symbol oder alle Symbole ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 1 Sekunde)\n- Enthält Risikomodus, Marktsituation und Volatilitätsdaten\n- Optionale Filterung nach Symbol\n\nAnwendungsfälle:\n- Überwachung des aktuellen Risikomanagement-Status im Trading-Dashboard\n- Entscheidungsunterstützung für Trading-Strategien\n- Automatisierte Anpassung von Trading-Parametern basierend auf dem Risikomodus\n- Überwachung der Marktvolatilität", "tags": ["Risk Management"], "parameters": [{"in": "query", "name": "symbol", "required": false, "schema": {"type": "string"}, "description": "Trading-Symbol, für das der Risikomanagement-Status abgerufen werden soll. Wenn nicht angegeben, werden Daten für alle Symbole zurückgegeben.", "example": "EURUSD"}], "responses": {"200": {"description": "Erfolgreiche Abfrage des Risikomanagement-Status", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RiskManagementState"}}, "example": [{"timestamp": "2023-04-15T14:30:00Z", "symbol": "EURUSD", "risk_on_mode": "Standard", "market_situation": "Neutral-Wait", "reasoning": "Market volatility has decreased, switching to neutral stance", "vola_15": 0.0012, "vola_30": 0.0018, "vola_60": 0.0025}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/risk_management/states/{symbol}": {"get": {"summary": "Risikomanagement-Statushistorie abrufen", "description": "Ruft die Historie der Risikomanagement-Statusänderungen für ein bestimmtes Symbol ab.\n\nTechnische Details:\n- Enthält nur Einträge, bei denen sich der Risikomodus oder die Marktsituation geändert hat\n- Sortiert nach Zeitstempel (älteste zu<PERSON>t)\n- Begrenzt auf die letzten 60 Tage\n\nAnwendungsfälle:\n- Analyse der Risikomanagement-Strategie über Zeit\n- Überprüfung der Häufigkeit von Statusänderungen\n- Korrelation von Statusänderungen mit Marktbewegungen\n- Historische Analyse für die Optimierung der Risikomanagement-Strategie", "tags": ["Risk Management"], "parameters": [{"in": "path", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das die Risikomanagement-Statushistorie abgerufen werden soll", "example": "EURUSD"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Risikomanagement-Statushistorie", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RiskManagementStateHistory"}}, "example": [{"timestamp": "2023-04-10T09:15:00Z", "symbol": "EURUSD", "risk_on_mode": "Low-Risk", "market_situation": "Neutral-Wait", "reasoning": "High market uncertainty due to economic data release"}, {"timestamp": "2023-04-12T14:30:00Z", "symbol": "EURUSD", "risk_on_mode": "Standard", "market_situation": "Neutral-Wait", "reasoning": "Market volatility has normalized"}, {"timestamp": "2023-04-15T10:45:00Z", "symbol": "EURUSD", "risk_on_mode": "Standard", "market_situation": "<PERSON>", "reasoning": "Strong upward trend confirmed by multiple indicators"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/risk_management/mode/{symbol}": {"put": {"summary": "Risikomodus aktualisieren", "description": "Aktualisiert den Risikomodus für ein bestimmtes Symbol.\n\nTechnische Details:\n- Beh<PERSON>lt die aktuelle Marktsituation bei\n- Speichert den Zeitpunkt der Änderung\n- Unterstützt drei Risikomodi: Low-Risk, Standard, High-Risk\n\nAnwendungsfälle:\n- Manuelle Anpassung des Risikomodus basierend auf Marktbedingungen\n- Implementierung von Risikomanagement-Strategien\n- Reaktion auf unerwartete Marktereignisse\n- Anpassung der Trading-Parameter für verschiedene Risikoniveaus", "tags": ["Risk Management"], "parameters": [{"in": "path", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das der Risikomodus aktualisiert werden soll", "example": "EURUSD"}, {"in": "query", "name": "mode", "required": true, "schema": {"type": "string", "enum": ["Low-Risk", "Standard", "High-Risk"]}, "description": "<PERSON><PERSON>er Risiko<PERSON>dus", "example": "Standard"}], "responses": {"200": {"description": "Erfolgreiche Aktualisierung des Risikomodus", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RiskModeUpdateResponse"}, "example": {"status": "success", "message": "Risk mode updated to Standard for EURUSD"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/risk_management/situation/{symbol}": {"put": {"summary": "Marktsituation aktualisieren", "description": "Aktualisiert die Marktsituation für ein bestimmtes Symbol.\n\nTechnische Details:\n- Behält den aktuellen Risikomodus bei\n- Speichert den Zeitpunkt der Änderung\n- Unterstützt drei Marktsituationen: Short, Neutral-Wait, Long\n\nAnwendungsfälle:\n- Manuelle Anpassung der Marktsituation basierend auf technischer Analyse\n- Implementierung von direktionalen Trading-Strategien\n- Reaktion auf Trendänderungen\n- Anpassung der Trading-Richtung basierend auf der Marktanalyse", "tags": ["Risk Management"], "parameters": [{"in": "path", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das die Marktsituation aktualisiert werden soll", "example": "EURUSD"}, {"in": "query", "name": "situation", "required": true, "schema": {"type": "string", "enum": ["Short", "Neutral-Wait", "<PERSON>"]}, "description": "Neue Marktsituation", "example": "Neutral-Wait"}], "responses": {"200": {"description": "Erfolgreiche Aktualisierung der Marktsituation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarketSituationUpdateResponse"}, "example": {"status": "success", "message": "Market situation updated to Neutral-Wait for EURUSD"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}