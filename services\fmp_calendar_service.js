const FMPBaseService = require('./fmp_base_service');
const { LoggingService } = require('./logging_service');

class FMPCalendarService extends FMPBaseService {
    constructor(logger) {
        super(logger || LoggingService.getInstance());
    }

    async getCalendar(fromDate, toDate) {
        const dates = this._calculateDateRange(fromDate, toDate);
        
        this.logger.info('Fetching FMP calendar data', {
            fromDate: dates.from,
            toDate: dates.to,
            correlationId: this.correlationId
        });

        try {
            const data = await this.makeRequest('v3/economic_calendar', {
                from: dates.from,
                to: dates.to
            });

            const transformedData = this._transformCalendarData(data);
            
            this.logger.info('Calendar data fetched successfully', {
                itemCount: transformedData.length,
                correlationId: this.correlationId
            });

            return transformedData;
        } catch (error) {
            this.logger.error('Failed to fetch calendar data', {
                error: error.message,
                correlationId: this.correlationId
            });
            throw error;
        }
    }

    _calculateDateRange(fromDate, toDate) {
        // If both dates are provided, use them
        if (fromDate && toDate) {
            return { from: fromDate, to: toDate };
        }

        // Initialize dates
        const today = new Date();
        let from = fromDate;
        let to = toDate;

        // If only from date is provided, set to date to 30 days after from
        if (fromDate && !toDate) {
            const fromDateObj = new Date(fromDate);
            const toDateObj = new Date(fromDateObj);
            toDateObj.setDate(toDateObj.getDate() + 30);
            to = this._formatDate(toDateObj);
        }

        // If only to date is provided, set from date to 30 days before to
        if (!fromDate && toDate) {
            const toDateObj = new Date(toDate);
            const fromDateObj = new Date(toDateObj);
            fromDateObj.setDate(fromDateObj.getDate() - 30);
            from = this._formatDate(fromDateObj);
        }

        // If no dates are provided, use the current month
        if (!fromDate && !toDate) {
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            from = this._formatDate(firstDay);
            to = this._formatDate(lastDay);
        }

        return { from, to };
    }

    _formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }


    _transformCalendarData(data) {
        if (!Array.isArray(data)) {
            this.logger.warn('Calendar data is not an array', {
                correlationId: this.correlationId
            });
            return [];
        }

        return data.map(item => ({
            date: item.date,
            event: item.event,
            symbol: item.symbol || null,
            time: item.time || null,
            estimate: item.estimate || null,
            actual: item.actual || null
        }));
    }
}

module.exports = FMPCalendarService;