const axios = require('axios');
const { LoggingService } = require('./logging_service');
const config = require('../configs/constants').FMP_CONFIG;

class FMPBaseService {
    constructor(logger) {
        this.logger = logger || LoggingService.getInstance();
        this.baseUrl = config.API_BASE_URL;
        this.maxRetries = config.MAX_RETRIES;
        this.retryDelay = config.RETRY_DELAY;
        this.correlationId = `fmp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }


    async makeRequest(endpoint, params = {}) {
        let retries = 0;
        let lastError = null;

        while (retries <= this.maxRetries) {
            try {
                // Moving this log statement outside of logger to ensure visibility
                if (process.stdout) {
                    process.stdout.write(`FMP API request to ${endpoint} (correlation: ${this.correlationId})\n`);
                }

                const response = await this._executeRequest(endpoint, params);
                this.logger.info('FMP API request completed', {
                    endpoint,
                    statusCode: response.status,
                    correlationId: this.correlationId
                });
                return response;
            } catch (error) {
                lastError = error;
                if (error.response?.status === 429) {
                    this.logger.warn('FMP API rate limit exceeded', {
                        endpoint,
                        retryCount: retries,
                        correlationId: this.correlationId
                    });
                    if (retries < this.maxRetries) {
                        await this._sleep(this.retryDelay * 1000);
                        retries++;
                        continue;
                    }
                }
                if (error.response?.status >= 500 && retries < this.maxRetries) {
                    this.logger.warn('FMP API server error, retrying', {
                        endpoint,
                        retryCount: retries,
                        correlationId: this.correlationId
                    });
                    await this._sleep(this.retryDelay * 1000);
                    retries++;
                    continue;
                }
                throw this._handleError(error);
            }
        }

        throw lastError;
    }

    /**
     * Execute a single API request
     * @private
     */
    async _executeRequest(endpoint, params) {
        const url = `${this.baseUrl}/${endpoint}`;
        const startTime = Date.now();

        try {
            const response = await axios.get(url, {
                params: {
                    ...params,
                    apikey: config.API_KEY
                },
                headers: {
                    'User-Agent': 'AlgoTrader-API/1.0'
                },
                timeout: 30000
            });

            const duration = Date.now() - startTime;
            this.logger.info('FMP API request successful', {
                endpoint,
                duration,
                statusCode: response.status,
                correlationId: this.correlationId
            });

            return response.data;
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('FMP API request failed', {
                endpoint,
                duration,
                error: error.message,
                correlationId: this.correlationId
            });
            throw error;
        }
    }

    /**
     * Handle API errors
     * @private
     */
    _handleError(error) {
        if (error.response) {
            const { status, data } = error.response;
            return new Error(`FMP API error: ${status} - ${data.message || JSON.stringify(data)}`);
        }
        if (error.request) {
            return new Error(`FMP API request failed: ${error.message}`);
        }
        return new Error(`FMP API error: ${error.message}`);
    }

    /**
     * Sleep helper
     * @private
     */
    _sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = FMPBaseService;