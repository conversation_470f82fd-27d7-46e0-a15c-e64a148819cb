{"name": "algotrader-api", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "jest", "start": "node server.js", "dev": "nodemon --exec \"npm start\"", "upload": "wsl -d Ubuntu rsync -avz -e 'ssh -i ~/.ssh/id_rsa_leon' --exclude-from='./.rsyncignore' --delete ./ <EMAIL>:/var/www/vhosts/ml-algotrader.com/api.ml-algotrader.com/", "sync-local": "robocopy \".\" \"Z:\\www\\vhosts\\ml-algotrader.com\\api.ml-algotrader.com\" /MIR /W:1 /R:1 /MT:16 /XD node_modules .git .github .vscode coverage dist tests __tests__ /XF *.log .env* *.test.* *.spec.* .DS_Store /NFL /NDL /NP /LOG:sync-log.txt"}, "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.25.0", "@google/generative-ai": "^0.24.0", "@helicone/helicone": "^2.1.6", "@hyperdx/node-opentelemetry": "^0.8.1", "@mistralai/mistralai": "^1.5.2", "body-parser": "^1.20.1", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-ipfilter": "^1.3.2", "generic-pool": "^3.9.0", "groq-sdk": "^0.17.0", "html-to-text": "^9.0.5", "ip-range-check": "^0.2.0", "langfuse": "^2.2.0", "mariadb": "^3.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "node-cache": "^5.1.2", "openai": "^4.89.0", "prompt-wrangler": "^0.7.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "xapi-node": "^2.6.2"}, "devDependencies": {"axios": "^1.8.4", "jest": "^29.7.0", "nock": "^14.0.0", "nodemon": "^3.0.2", "typescript": "^4.9.5"}}