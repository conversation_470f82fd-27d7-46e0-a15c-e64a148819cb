### Get IG Trades
// @name get-ig-trades
GET {{API_BASE_URL}}/api/v1/ig/trades?refID=IG-D1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: IG Trades Retrieval");
        client.assert(response.status === 200, "Response status is not 200");
    });
    
    client.test("Response is an array", function() {
        client.assert(Array.isArray(response.body), "Response is not an array");
    });
    
    if (response.body.length > 0) {
        client.test("Trade items have correct properties", function() {
            const firstItem = response.body[0];
            client.assert(firstItem.hasOwnProperty('symbol'), "Item does not have 'symbol' property");
            client.assert(firstItem.hasOwnProperty('pipValue'), "Item does not have 'pipValue' property");
            // Even if symbol_details is undefined, the code should not crash
            client.log("PipValue: " + firstItem.pipValue);
        });
    }
%}

### Get IG Account
// @name get-ig-account
GET {{API_BASE_URL}}/api/v1/ig/account?refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: IG Account Retrieval");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

### Get IG Margin Trade
// @name get-ig-margin-trade
GET {{API_BASE_URL}}/api/v1/ig/margin?refID=IG-P1&symbol=CS.D.EURUSD.TODAY.IP&volume=1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: IG Margin Trade Calculation");
        client.assert(response.status === 200, "Response status is not 200");
    });
    
    client.test("Response has correct structure", function() {
        var jsonData = response.body;
        client.assert(jsonData.hasOwnProperty('margin'), "Response does not have 'margin' property");
        client.assert(jsonData.hasOwnProperty('onePipMeans'), "Response does not have 'onePipMeans' property");
        client.assert(jsonData.hasOwnProperty('currency'), "Response does not have 'currency' property");
    });
%}