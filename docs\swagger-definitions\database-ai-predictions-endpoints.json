{"paths": {"/api/v1/db/ai_predictions": {"get": {"summary": "KI-Vorhersagen für Handelssymbole abrufen", "description": "Ruft KI-generierte Vorhersagen für ein bestimmtes Symbol oder alle verfügbaren Symbole ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Optionale Filterung nach Symbol\n- Enthält Konfidenzwerte und Richtungsvorhersagen\n\nAnwendungsfälle:\n- <PERSON><PERSON><PERSON> von KI-Vorhersagen im Trading-Dashboard\n- Integration in automatisierte Trading-Strategien\n- Entscheidungsunterstützung für manuelle Trades\n- Vergleich mit tatsächlichen Marktbewegungen", "tags": ["AI Integration"], "parameters": [{"in": "query", "name": "symbol", "required": false, "schema": {"type": "string"}, "description": "Trading-Symbol, für das Vorhersagen abgerufen werden sollen. Wenn nicht angegeben, werden Vorhersagen für alle verfügbaren Symbole zurückgegeben.", "example": "EURUSD"}], "responses": {"200": {"description": "Erfolgreiche Abfrage der KI-Vorhersagen", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIPrediction"}}, "example": [{"symbol": "EURUSD", "prediction_date": "2023-04-15T10:30:00Z", "prediction_value": 1.0865, "prediction_direction": "<PERSON>", "confidence": 0.85, "model_name": "LSTM-v2.3", "timeframe": "1D"}, {"symbol": "EURUSD", "prediction_date": "2023-04-15T10:30:00Z", "prediction_value": 1.0842, "prediction_direction": "Short", "confidence": 0.67, "model_name": "Transformer-v1.2", "timeframe": "4H"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}