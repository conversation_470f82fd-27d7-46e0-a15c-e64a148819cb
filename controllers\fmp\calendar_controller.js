const { LoggingService } = require('../../services/logging_service');
const FMPCalendarService = require('../../services/fmp_calendar_service');

class FMPCalendarController {
    constructor() {
        this.logger = LoggingService.getInstance();
        this.calendarService = new FMPCalendarService(this.logger);
    }


    async fetchCalendar(req, res) {
        try {
            const { from, to } = req.query;
            
            this.logger.info('Calendar fetch request received', {
                from,
                to,
                requestId: req.id
            });

            const calendarData = await this.calendarService.getCalendar(from, to);
            
            return res.json({
                status: 'success',
                data: calendarData,
                count: calendarData.length
            });
        } catch (error) {
            this.logger.error('Calendar fetch failed', {
                error: error.message,
                requestId: req.id
            });

            return res.status(500).json({
                status: 'error',
                message: 'Failed to fetch calendar data',
                error: error.message
            });
        }
    }

  
}

module.exports = new FMPCalendarController();