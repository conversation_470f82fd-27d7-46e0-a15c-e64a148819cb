const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam, validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');

async function getWeekStatisticsIndependent(refID) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getWeekStatisticsIndependent', 'Starting week statistics retrieval', {
            refID
        });

        refID = validateStringParam(refID, {
            required: true,
            paramName: 'refID'
        });

        const query = {
            sql: 'SELECT YEAR(t.exit_time) as year, WEEKOFYEAR(t.exit_time) as week, round(min(profit),0) AS min_profit, round(max(profit),0) AS max_profit, COUNT(*) AS cnt_trades, round(SUM(profit),0) AS sum_profit \
             FROM `trades_history` t, `accounts` a \
             WHERE t.account=a.account_id \
              AND t.exit_time>=DATE_SUB(NOW(), INTERVAL 3 Month) \
              AND a.refId=? \
            GROUP BY WEEKOFYEAR(t.exit_time) \
             ORDER BY year,week',
            bigIntAsNumber: true
        };

        const result = await executeQuery(query, [refID]);

        log(LOG_LEVELS.INFO, 'getWeekStatisticsIndependent', 'Successfully retrieved week statistics', {
            refID,
            resultCount: result?.length || 0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getWeekStatisticsIndependent', 'Failed to fetch week statistics', {
            error: err.message,
            stack: err.stack,
            refID
        });
        throw err;
    } finally {
        logPerformance('getWeekStatisticsIndependent', startTime);
    }
}

async function getWeekStatistics(req, res) {
    try {
        const result = await withCacheWrapper(
            'GENERAL',
            'getWeekStatistics',
            () => getWeekStatisticsIndependent(req.query.refID),
            [req.query.refID]
        );
        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getWeekStatistics');
        res.status(errorResponse.status).json(errorResponse);
    }
}

async function getCurrentWeekStatisticsIndependent(refID) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getCurrentWeekStatisticsIndependent', 'Starting current week statistics retrieval', {
            refID
        });

        refID = validateStringParam(refID, {
            required: true,
            paramName: 'refID'
        });

        const query = {
            sql: 'SELECT YEAR(t.exit_time) AS year, WEEKOFYEAR(t.exit_time) AS week, ROUND(MIN(profit),0) AS min_profit, ROUND(MAX(profit),0) AS max_profit, COUNT(*) AS cnt_trades, ROUND(SUM(profit),0) AS sum_profit\
                FROM `trades_history` t \
                WHERE t.refId=? \
                  AND YEAR(t.exit_time)=YEAR(NOW()) \
                  AND WEEKOFYEAR(t.exit_time)=WEEKOFYEAR(NOW()) ',
            bigIntAsNumber: true
        };

        const result = await executeQuery(query, [refID]);

        log(LOG_LEVELS.INFO, 'getCurrentWeekStatisticsIndependent', 'Successfully retrieved current week statistics', {
            refID,
            resultCount: result?.length || 0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getCurrentWeekStatisticsIndependent', 'Failed to fetch current week statistics', {
            error: err.message,
            stack: err.stack,
            refID
        });
        throw err;
    } finally {
        logPerformance('getCurrentWeekStatisticsIndependent', startTime);
    }
}


async function getCurrentWeekStatistics(req, res) {
    try {
        const result = await withCacheWrapper(
            'GENERAL',
            'getCurrentWeekStatistics',
            () => getCurrentWeekStatisticsIndependent(req.query.refID),
            [req.query.refID]
        );
        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getCurrentWeekStatistics');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getWeekStatistics,
    getWeekStatisticsIndependent,
    getCurrentWeekStatistics,
    getCurrentWeekStatisticsIndependent
};
