const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { TIME_CONSTANTS, FILTERS } = require('../../configs/constants');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam, validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');
const { buildCalendarListQuery } = require('./queries/calendar_queries');
const { buildDeltaReportQuery } = require('./queries/delta_report_queries');

async function getCalendarListIndependent(minus_days = TIME_CONSTANTS.DEFAULT_DAYS.CALENDAR_PAST, plus_days = TIME_CONSTANTS.DEFAULT_DAYS.CALENDAR_FUTURE) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getCalendarListIndependent', 'Starting calendar list retrieval', {
            minus_days,
            plus_days
        });

        minus_days = validateNumericParam(minus_days, {
            defaultValue: TIME_CONSTANTS.DEFAULT_DAYS.CALENDAR_PAST,
            min: 0,
            max: 365,
            paramName: 'minus_days'
        });

        plus_days = validateNumericParam(plus_days, {
            defaultValue: TIME_CONSTANTS.DEFAULT_DAYS.CALENDAR_FUTURE,
            min: 0,
            max: 365,
            paramName: 'plus_days'
        });

        log(LOG_LEVELS.DEBUG, 'getCalendarListIndependent', 'Executing query', {
            minus_days,
            plus_days,
            min_impact: FILTERS.MIN_IMPACT,
            allowed_countries: FILTERS.ALLOWED_COUNTRIES
        });

        const query = buildCalendarListQuery();
        const result = await executeQuery(query,
            [minus_days, plus_days, FILTERS.MIN_IMPACT, FILTERS.ALLOWED_COUNTRIES.join(',')],
            {
                bigIntAsNumber: true,
                timezone: 'de_de'
            }
        );
        
        log(LOG_LEVELS.INFO, 'getCalendarListIndependent', 'Successfully retrieved calendar list', {
            minus_days,
            plus_days,
            resultCount: result?result.length:0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getCalendarListIndependent', 'Failed to retrieve calendar list', {
            error: err.message,
            stack: err.stack,
            minus_days,
            plus_days
        });
        throw err;
    } finally {
        logPerformance('getCalendarListIndependent', startTime);
    }
}

async function getCalendarList(req, res) {
    try {
        const p_minus_days = parseInt(req.query.minus_days) || TIME_CONSTANTS.DEFAULT_DAYS.CALENDAR_PAST;
        const p_plus_days = parseInt(req.query.plus_days) || TIME_CONSTANTS.DEFAULT_DAYS.CALENDAR_FUTURE;

        if (isNaN(p_minus_days) || isNaN(p_plus_days)) {
            throw new ValidationError('Invalid days parameters', {
                minus_days: req.query.minus_days,
                plus_days: req.query.plus_days
            });
        }
        
        const result = await withCacheWrapper(
            'CALENDAR',
            'getCalendarList',
            () => getCalendarListIndependent(p_minus_days, p_plus_days),
            [p_minus_days, p_plus_days]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getCalendarList');
        res.status(errorResponse.status).json(errorResponse);
    }
}

async function getDeltaReportIndependent(calendar_id, symbol) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getDeltaReportIndependent', 'Starting delta report retrieval', {
            calendar_id,
            symbol
        });

        calendar_id = validateStringParam(calendar_id, {
            required: true,
            paramName: 'calendar_id'
        });

        symbol = validateStringParam(symbol, {
            required: true,
            minLength: 1,
            maxLength: 50,
            paramName: 'symbol'
        });

        log(LOG_LEVELS.DEBUG, 'getDeltaReportIndependent', 'Executing query', {
            calendar_id,
            symbol
        });

        const query = buildDeltaReportQuery();
        const result = await executeQuery(query, [calendar_id, symbol]);
        
        log(LOG_LEVELS.INFO, 'getDeltaReportIndependent', 'Successfully retrieved delta report', {
            calendar_id,
            symbol,
            query,
            resultCount: result?result.length:0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getDeltaReportIndependent', 'Failed to retrieve delta report', {
            error: err.message,
            stack: err.stack,
            calendar_id,
            symbol
        });
        throw err;
    } finally {
        logPerformance('getDeltaReportIndependent', startTime);
    }
}

async function getDeltaReport(req, res) {
    try {
        const result = await withCacheWrapper(
            'CALENDAR',
            'getDeltaReport',
            () => getDeltaReportIndependent(req.query.calendar_id, req.query.symbol),
            [req.query.calendar_id, req.query.symbol]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getDeltaReport');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getCalendarList,
    getCalendarListIndependent,
    getDeltaReport,
    getDeltaReportIndependent
};
