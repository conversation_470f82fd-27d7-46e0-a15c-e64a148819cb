const ipRangeCheck = require('ip-range-check');

/**
 * Prüft, ob eine IP-Adresse in einem CIDR-Bereich liegt
 * @param {string} ip - Die zu prüfende IP-Adresse
 * @param {string} cidr - Der CIDR-Bereich (z.B. '***********/24')
 * @returns {boolean} - True, wenn die IP im Bereich liegt
 */
function isIpInCidrRange(ip, cidr) {
  return ipRangeCheck(ip, cidr);
}

/**
 * Prüft, ob eine IP-Adresse in einer Liste von CIDR-Bereichen liegt
 * @param {string} ip - Die zu prüfende IP-Adresse
 * @param {Array<string>} cidrList - Liste von CIDR-Bereichen
 * @returns {boolean} - True, wenn die IP in mindestens einem Bereich liegt
 */
function isIpAllowed(ip, cidrList) {
  return cidrList.some(cidr => isIpInCidrRange(ip, cidr));
}

module.exports = {
  isIpInCidrRange,
  isIpAllowed
};