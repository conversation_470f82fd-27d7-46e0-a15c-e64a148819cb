
###

// @name chartdata
// account=1357150&symbol=US500&period=15&days=2
GET {{API_BASE_URL}}/api/v1/db/chartdata?symbol=CC.D.CL.UME.IP&period=15&days=200
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name all_used_symbols_with_pip_values
// account=1357150&symbol=US500&period=15&days=2
GET {{API_BASE_URL}}/api/v1/db/all_used_symbols_with_pip_values
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name pivotpoint-list
GET {{API_BASE_URL}}/api/v1/db/pivotpoints?days=3000
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: pivotpoints");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name trading-mode
GET {{API_BASE_URL}}/api/v1/db/tradingmode?refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name tradeLogs
GET {{API_BASE_URL}}/api/v1/db/tradeLogs?limit=250&refID=IG-D1&days=1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name getSymbolAIPredictions
GET {{API_BASE_URL}}/api/v1/db/symbol_ai_predictions?symbol=IX.D.NASDAQ.IFE.IP
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name getSymbolAIPredictions
GET {{API_BASE_URL}}/api/v1/db/symbol_ai_predictions?symbol=IX.D.NASDAQ.IFE.IP
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


