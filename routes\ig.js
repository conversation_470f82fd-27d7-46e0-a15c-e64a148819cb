const express = require('express');
const router = express.Router();

const igGetController = require('../controllers/ig/controller_ig_get');
const igPostController = require('../controllers/ig/controller_ig_post');
const igMarketDataController = require('../controllers/ig/ig_market_data_controller');

// IG Trading Routes
router.get('/trades', igGetController.getIGTrades);
router.get('/trades/history', igGetController.getIGTradesHistory);
router.get('/accounts', igGetController.getIGAccount);
router.get('/trades/margin-trade', igGetController.getIGMarginTrade);
router.post('/trades/bridge', igPostController.ig_trade_bridgeformat);
router.post('/trades/execute', igPostController.executeTrade);
router.post('/trades/close', igPostController.closePosition);
router.post('/trades/modify', igPostController.modifyPosition);
router.post('/trades/status', igPostController.getTradeStatus);

// IG Market Data Routes
router.get('/markets/info', igMarketDataController.getMarketInfo);
router.get('/markets/prices', igMarketDataController.getPrices);
router.get('/markets/prices/history', igMarketDataController.getHistoricalPrices);
router.get('/markets/prices/last', igMarketDataController.getLastCandle);

module.exports = router;