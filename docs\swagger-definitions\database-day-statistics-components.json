{"components": {"schemas": {"DayStatistics": {"type": "object", "properties": {"date": {"type": "string", "format": "date", "description": "Datum der Statistik im Format YYYY-MM-DD", "example": "2023-04-15"}, "min_profit": {"type": "number", "format": "float", "description": "Minimaler Gewinn/Verlust an diesem Tag", "example": -120}, "max_profit": {"type": "number", "format": "float", "description": "Maximaler Gewinn/Verlust an diesem Tag", "example": 350}, "cnt_trades": {"type": "integer", "description": "<PERSON><PERSON><PERSON> der Trades an diesem Tag", "example": 12}, "sum_profit": {"type": "number", "format": "float", "description": "Gesamtgewinn/-verlust an diesem Tag", "example": 230}}}, "CurrentDayStatistics": {"type": "object", "properties": {"YEAR": {"type": "integer", "description": "Jahr der Statistik", "example": 2023}, "MONTH": {"type": "integer", "description": "Monat der Statistik (1-12)", "example": 4}, "DATE": {"type": "string", "format": "date", "description": "Datum der Statistik im Format YYYY-MM-DD", "example": "2023-04-15"}, "min_profit": {"type": "number", "format": "float", "description": "Minimaler Gewinn/Verlust am aktuellen Tag", "example": -120}, "max_profit": {"type": "number", "format": "float", "description": "Maximaler Gewinn/Verlust am aktuellen Tag", "example": 350}, "cnt_trades": {"type": "integer", "description": "Anzahl der Trades am aktuellen Tag", "example": 12}, "sum_profit": {"type": "number", "format": "float", "description": "Gesamtgewinn/-verlust am aktuellen Tag", "example": 230}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Parameter 'refID' is required"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getDayStatistics"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Parameter 'refID' is required", "function": "getDayStatistics"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch day statistics", "function": "getDayStatistics"}}}}}}}