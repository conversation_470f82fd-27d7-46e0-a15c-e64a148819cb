const MicrosoftTodoService = require('../../services/microsoft_todo_service');
const { LoggingService } = require('../../services/logging_service');
const nock = require('nock');
const NodeCache = require('node-cache');

// Mock the config
jest.mock('../../configs/constants', () => ({
  MICROSOFT_TODO_CONFIG: {
    API_BASE_URL: 'https://graph.microsoft.com/v1.0',
    AUTH_URL: 'https://login.microsoftonline.com',
    TENANT_ID: 'test-tenant-id',
    CLIENT_ID: 'test-client-id',
    CLIENT_SECRET: 'test-client-secret',
    SCOPES: ['https://graph.microsoft.com/.default'],
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,
    TIMEOUT: 30000,
    CACHE_TTL: {
      token: 3600
    },
    ENDPOINTS: {
      token: '/oauth2/v2.0/token',
      tasks: '/me/todo/lists/{listId}/tasks'
    },
    TASK_IMPORTANCE: {
      LOW: 'low',
      NORMAL: 'normal',
      HIGH: 'high'
    }
  }
}));

// Mock LoggingService
jest.mock('../../services/logging_service', () => ({
  LoggingService: {
    getInstance: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  }
}));

describe('MicrosoftTodoService', () => {
  let service;
  let mockLogger;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    nock.cleanAll();

    // Set up environment variables FIRST
    process.env.MICROSOFT_TENANT_ID = '12345678-1234-1234-1234-123456789012';
    process.env.MICROSOFT_CLIENT_ID = '87654321-4321-4321-4321-210987654321';
    process.env.MICROSOFT_CLIENT_SECRET = 'test-secret-key';

    // Create mock logger
    mockLogger = {
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    };

    // Create service instance
    service = new MicrosoftTodoService(mockLogger);

    // Clear cache
    service.cache.flushAll();
  });

  afterEach(() => {
    nock.cleanAll();
    // Clean up environment variables
    delete process.env.MICROSOFT_TENANT_ID;
    delete process.env.MICROSOFT_CLIENT_ID;
    delete process.env.MICROSOFT_CLIENT_SECRET;
  });

  describe('Constructor', () => {
    it('should initialize with default logger if none provided', () => {
      const serviceWithoutLogger = new MicrosoftTodoService();
      expect(LoggingService.getInstance).toHaveBeenCalled();
    });

    it('should throw error for missing configuration', () => {
      // Temporarily remove environment variables
      const originalTenantId = process.env.MICROSOFT_TENANT_ID;
      const originalClientId = process.env.MICROSOFT_CLIENT_ID;
      const originalClientSecret = process.env.MICROSOFT_CLIENT_SECRET;

      delete process.env.MICROSOFT_TENANT_ID;
      delete process.env.MICROSOFT_CLIENT_ID;
      delete process.env.MICROSOFT_CLIENT_SECRET;

      expect(() => {
        new MicrosoftTodoService(mockLogger);
      }).toThrow('Microsoft Graph API configuration validation failed');

      // Restore environment variables
      process.env.MICROSOFT_TENANT_ID = originalTenantId;
      process.env.MICROSOFT_CLIENT_ID = originalClientId;
      process.env.MICROSOFT_CLIENT_SECRET = originalClientSecret;
    });

    it('should set correlation ID', () => {
      expect(service.correlationId).toMatch(/^mstodo_\d+_[a-z0-9]+$/);
    });
  });

  describe('getAccessToken', () => {
    const mockTokenResponse = {
      access_token: 'mock-access-token',
      expires_in: 3600,
      token_type: 'Bearer'
    };

    it('should return cached token if available', async () => {
      // Set cached token
      service.cache.set('microsoft_access_token', 'cached-token');
      
      const token = await service.getAccessToken();
      
      expect(token).toBe('cached-token');
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Using cached Microsoft access token',
        expect.objectContaining({ correlationId: service.correlationId })
      );
    });

    it('should request new token when cache is empty', async () => {
      // Mock OAuth token endpoint
      nock('https://login.microsoftonline.com')
        .post('/test-tenant-id/oauth2/v2.0/token')
        .reply(200, mockTokenResponse);

      const token = await service.getAccessToken();

      expect(token).toBe('mock-access-token');
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Requesting new Microsoft access token',
        expect.objectContaining({
          tenantId: 'test-tenant-id',
          correlationId: service.correlationId
        })
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Microsoft access token obtained successfully',
        expect.objectContaining({
          duration: expect.any(Number),
          expiresIn: 3600,
          correlationId: service.correlationId
        })
      );
    });

    it('should handle OAuth authentication errors', async () => {
      // Mock OAuth error response
      nock('https://login.microsoftonline.com')
        .post('/test-tenant-id/oauth2/v2.0/token')
        .reply(401, {
          error: 'invalid_client',
          error_description: 'Invalid client credentials'
        });

      await expect(service.getAccessToken()).rejects.toThrow('Microsoft OAuth error: 401 - Invalid client credentials');
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to obtain Microsoft access token',
        expect.objectContaining({
          duration: expect.any(Number),
          error: expect.any(String),
          status: 401,
          correlationId: service.correlationId
        })
      );
    });

    it('should handle network errors', async () => {
      // Mock network error
      nock('https://login.microsoftonline.com')
        .post('/test-tenant-id/oauth2/v2.0/token')
        .replyWithError('Network error');

      await expect(service.getAccessToken()).rejects.toThrow('Microsoft OAuth request failed: Network error');
    });

    it('should cache token with proper TTL', async () => {
      // Mock OAuth token endpoint
      nock('https://login.microsoftonline.com')
        .post('/test-tenant-id/oauth2/v2.0/token')
        .reply(200, mockTokenResponse);

      const cacheSpy = jest.spyOn(service.cache, 'set');
      
      await service.getAccessToken();

      expect(cacheSpy).toHaveBeenCalledWith(
        'microsoft_access_token',
        'mock-access-token',
        3300 // 3600 - 300 (5 minute buffer)
      );
    });
  });

  describe('createTask', () => {
    const mockTaskData = {
      title: 'Test Task',
      body: 'Test Description',
      dueDateTime: '2025-01-15T14:00:00.000Z',
      importance: 'high',
      categories: ['Test', 'Category']
    };

    const mockCreatedTask = {
      id: 'task-id-123',
      title: 'Test Task',
      body: { content: 'Test Description', contentType: 'text' },
      status: 'notStarted',
      importance: 'high',
      createdDateTime: '2025-01-02T10:30:00.000Z',
      lastModifiedDateTime: '2025-01-02T10:30:00.000Z',
      dueDateTime: { dateTime: '2025-01-15T14:00:00.000Z', timeZone: 'UTC' },
      categories: ['Test', 'Category'],
      webUrl: 'https://to-do.office.com/tasks/id/task-id-123'
    };

    beforeEach(() => {
      // Mock successful OAuth token request
      nock('https://login.microsoftonline.com')
        .post('/test-tenant-id/oauth2/v2.0/token')
        .reply(200, {
          access_token: 'mock-access-token',
          expires_in: 3600
        });
    });

    it('should create task successfully', async () => {
      // Mock Graph API task creation
      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(201, mockCreatedTask);

      const result = await service.createTask('test-list-id', mockTaskData);

      expect(result).toEqual(mockCreatedTask);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Creating Microsoft ToDo task',
        expect.objectContaining({
          listId: 'test-list-id',
          title: 'Test Task',
          retryCount: 0,
          correlationId: service.correlationId
        })
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Microsoft ToDo task created successfully',
        expect.objectContaining({
          taskId: 'task-id-123',
          title: 'Test Task',
          correlationId: service.correlationId
        })
      );
    });

    it('should validate task data', async () => {
      await expect(service.createTask('test-list-id', {})).rejects.toThrow('Task title is required and must be a non-empty string');
      await expect(service.createTask('test-list-id', { title: '' })).rejects.toThrow('Task title is required and must be a non-empty string');
      await expect(service.createTask('test-list-id', { title: 'Test', importance: 'invalid' })).rejects.toThrow('Invalid task importance');
      await expect(service.createTask('test-list-id', { title: 'Test', dueDateTime: 'invalid-date' })).rejects.toThrow('Due date must be in valid ISO 8601 format');
    });

    it('should handle rate limiting with retry', async () => {
      // Mock rate limit response followed by success
      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(429, { error: { message: 'Rate limit exceeded' } }, { 'retry-after': '1' })
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(201, mockCreatedTask);

      const result = await service.createTask('test-list-id', { title: 'Test Task' });

      expect(result).toEqual(mockCreatedTask);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Microsoft Graph API rate limit exceeded',
        expect.objectContaining({
          listId: 'test-list-id',
          retryCount: 0,
          retryAfter: 1,
          correlationId: service.correlationId
        })
      );
    });

    it('should handle server errors with retry', async () => {
      // Mock server error followed by success
      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(500, { error: { message: 'Internal server error' } })
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(201, mockCreatedTask);

      const result = await service.createTask('test-list-id', { title: 'Test Task' });

      expect(result).toEqual(mockCreatedTask);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Microsoft Graph API server error, retrying',
        expect.objectContaining({
          listId: 'test-list-id',
          retryCount: 0,
          status: 500,
          correlationId: service.correlationId
        })
      );
    });

    it('should handle authentication errors with token refresh', async () => {
      // Mock 401 error followed by new token and success
      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(401, { error: { message: 'Unauthorized' } })
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(201, mockCreatedTask);

      // Mock second token request
      nock('https://login.microsoftonline.com')
        .post('/test-tenant-id/oauth2/v2.0/token')
        .reply(200, {
          access_token: 'new-access-token',
          expires_in: 3600
        });

      const result = await service.createTask('test-list-id', { title: 'Test Task' });

      expect(result).toEqual(mockCreatedTask);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Microsoft Graph API authentication failed, clearing token cache',
        expect.objectContaining({
          correlationId: service.correlationId
        })
      );
    });

    it('should fail after max retries', async () => {
      // Mock repeated failures
      for (let i = 0; i <= 3; i++) {
        nock('https://graph.microsoft.com')
          .post('/v1.0/me/todo/lists/test-list-id/tasks')
          .reply(500, { error: { message: 'Internal server error' } });
      }

      await expect(service.createTask('test-list-id', { title: 'Test Task' })).rejects.toThrow('Microsoft Graph API error: 500');
    });

    it('should handle client errors without retry', async () => {
      // Mock 400 error (client error - should not retry)
      nock('https://graph.microsoft.com')
        .post('/v1.0/me/todo/lists/test-list-id/tasks')
        .reply(400, { error: { message: 'Bad request' } });

      await expect(service.createTask('test-list-id', { title: 'Test Task' })).rejects.toThrow('Microsoft Graph API error: 400');
    });
  });

  describe('_validateTaskData', () => {
    it('should validate required fields', () => {
      expect(() => service._validateTaskData(null)).toThrow('Task data must be an object');
      expect(() => service._validateTaskData({})).toThrow('Task title is required and must be a non-empty string');
      expect(() => service._validateTaskData({ title: '' })).toThrow('Task title is required and must be a non-empty string');
    });

    it('should validate importance values', () => {
      expect(() => service._validateTaskData({ title: 'Test', importance: 'invalid' })).toThrow('Invalid task importance');
      expect(() => service._validateTaskData({ title: 'Test', importance: 'low' })).not.toThrow();
      expect(() => service._validateTaskData({ title: 'Test', importance: 'normal' })).not.toThrow();
      expect(() => service._validateTaskData({ title: 'Test', importance: 'high' })).not.toThrow();
    });

    it('should validate date format', () => {
      expect(() => service._validateTaskData({ title: 'Test', dueDateTime: 'invalid-date' })).toThrow('Due date must be in valid ISO 8601 format');
      expect(() => service._validateTaskData({ title: 'Test', dueDateTime: '2025-01-15T14:00:00.000Z' })).not.toThrow();
    });
  });

  describe('_isValidISODate', () => {
    it('should validate ISO date strings', () => {
      expect(service._isValidISODate('2025-01-15T14:00:00.000Z')).toBe(true);
      expect(service._isValidISODate('2025-01-15T14:00:00Z')).toBe(true);
      expect(service._isValidISODate('invalid-date')).toBe(false);
      expect(service._isValidISODate('2025-01-15')).toBe(false);
      expect(service._isValidISODate('')).toBe(false);
    });
  });
});
