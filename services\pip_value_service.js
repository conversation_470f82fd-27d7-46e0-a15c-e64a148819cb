/**
 * Globaler Speicher für Pip-Werte aller Handelssymbole
 * @type {Map<string, number>}
 */
let PipValuePerSymbol = new Map();

/**
 * Aktualisiert die Pip-Werte für alle Handelssymbole
 * 
 * Diese Funktion überschreibt die bestehende Map mit neuen Pip-Werten.
 * Sie wird typischerweise beim Systemstart oder nach Datenbank-Updates aufgerufen.
 * 
 * @param {Map<string, number>} values - Map mit Symbol-Keys und zugehörigen Pip-Werten
 * @throws {TypeError} Wenn values kein Map-Objekt ist
 * 
 * @example
 * // Pip-Werte setzen
 * const pipValues = new Map([
 *   ['EURUSD', 0.0001],
 *   ['GBPUSD', 0.0001]
 * ]);
 * setPipValues(pipValues);
 */
function setPipValues(values) {
    if (!(values instanceof Map)) {
        throw new TypeError('values muss eine Map-Instanz sein');
    }
    PipValuePerSymbol = values;
}

/**
 * Liefert die aktuelle Map aller Pip-Werte zurück
 * 
 * @returns {Map<string, number>} Map mit Symbol-Keys und zugehörigen Pip-Werten
 * 
 * @example
 * // Pip-Werte abrufen
 * const pipValues = getPipValues();
 * const eurUsdPip = pipValues.get('EURUSD'); // Returns: 0.0001
 */
function getPipValues() {
    return PipValuePerSymbol;
}

module.exports = {
    setPipValues,
    getPipValues
};
