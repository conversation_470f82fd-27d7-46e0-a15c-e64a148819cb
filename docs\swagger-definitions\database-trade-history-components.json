{"components": {"schemas": {"TradeHistoryItem": {"type": "object", "properties": {"trade_id": {"type": "integer", "description": "Eindeutige ID des Trades", "example": 12345}, "account": {"type": "integer", "description": "Konto-ID", "example": 1}, "refId": {"type": "string", "description": "Referenz-ID des Kontos", "example": "IG-D1"}, "symbol": {"type": "string", "description": "Gehandeltes Symbol", "example": "EURUSD"}, "cmd": {"type": "integer", "description": "Handelsrichtung (0 = <PERSON>, 1 = Short)", "enum": [0, 1], "example": 0}, "volume": {"type": "number", "format": "float", "description": "Handelsvolumen", "example": 1.5}, "open_price": {"type": "number", "format": "float", "description": "Eröffnungspreis", "example": 1.0865}, "close_price": {"type": "number", "format": "float", "description": "Schlusspreis", "example": 1.0885}, "sl": {"type": "number", "format": "float", "description": "Stop-Loss-Preis", "example": 1.084}, "tp": {"type": "number", "format": "float", "description": "Take-Profit-Preis", "example": 1.09}, "profit": {"type": "number", "format": "float", "description": "Gewinn/Verlust des Trades", "example": 30.0}, "commission": {"type": "number", "format": "float", "description": "Kommission", "example": 1.5}, "swap": {"type": "number", "format": "float", "description": "Swap-Gebühren", "example": 0.5}, "entry_time": {"type": "string", "format": "date-time", "description": "Zeitpunkt der Eröffnung", "example": "2023-04-15T10:30:00Z"}, "exit_time": {"type": "string", "format": "date-time", "description": "Zeitpunkt der Schließung", "example": "2023-04-15T14:30:00Z"}, "comment": {"type": "string", "description": "Kommentar zum Trade", "example": "Manual close"}, "magic_number": {"type": "integer", "description": "Magic Number (zur Identifizierung des Trading-Systems)", "example": 12345}, "duration_minutes": {"type": "integer", "description": "Dauer des Trades in Minuten", "example": 240}}}, "TradeHistoryStopLevel": {"type": "object", "properties": {"trade_id": {"type": "integer", "description": "Eindeutige ID des Trades", "example": 12345}, "symbol": {"type": "string", "description": "Gehandeltes Symbol", "example": "EURUSD"}, "cmd": {"type": "integer", "description": "Handelsrichtung (0 = <PERSON>, 1 = Short)", "enum": [0, 1], "example": 0}, "open_price": {"type": "number", "format": "float", "description": "Eröffnungspreis", "example": 1.0865}, "sl": {"type": "number", "format": "float", "description": "Stop-Loss-Preis", "example": 1.084}, "entry_time": {"type": "string", "format": "date-time", "description": "Zeitpunkt der Eröffnung", "example": "2023-04-15T10:30:00Z"}}}, "TradeHistoryEquity": {"type": "object", "properties": {"exit_time": {"type": "string", "format": "date-time", "description": "Zeitpunkt des Equity-Punkts", "example": "2023-04-15T14:30:00Z"}, "equity": {"type": "number", "format": "float", "description": "Gesamtequity zu diesem Zeitpunkt", "example": 10250.5}, "day_profit": {"type": "number", "format": "float", "description": "Gewinn/Verlust für den Tag", "example": 150.25}, "balance": {"type": "number", "format": "float", "description": "Kontostand ohne schwebende Gewinne/Verluste", "example": 10200.0}, "floating_pl": {"type": "number", "format": "float", "description": "Schwebender Gewinn/Verlust", "example": 50.5}, "margin_used": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "example": 500.0}, "free_margin": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON>", "example": 9750.5}, "margin_level": {"type": "number", "format": "float", "description": "Margin-Level in Prozent", "example": 2050.1}, "drawdown": {"type": "number", "format": "float", "description": "Aktueller Drawdown vom Höchststand", "example": 150.0}, "high_watermark": {"type": "number", "format": "float", "description": "Höchster aufgezeichneter Equity-Stand", "example": 10400.5}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Parameter 'refID' is required"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getTradeHistory"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Parameter 'refID' is required", "function": "getTradeHistory"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to retrieve trade history", "function": "getTradeHistory"}}}}}}}