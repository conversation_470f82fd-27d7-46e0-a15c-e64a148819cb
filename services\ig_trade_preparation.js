const IGMarketDataProvider = require('./ig_market_data_provider');
const { LOG_LEVELS, log } = require('./logging_service');

class IGTradePreparationService {
    constructor(marketDataProvider, logger) {
        this.marketDataProvider = marketDataProvider;
        this.logger = logger;
    }

    /**
     * Prepares a trade by running all necessary preparation steps
     * @param {Object} trade - The trade to prepare
     */
    async prepare(trade) {
        try {
            log(LOG_LEVELS.INFO, 'prepare', 'Starting trade preparation', this.enrichContext({
                trade_info: trade
            }));

            const symbol = trade.tradeTransInfo.symbol;
            await this.fixPrecision(trade, symbol);
            await this.fixPriceIfNotPresent(trade, symbol);
            this.recalcIfNecessarySL(trade);
            this.fixTrailStopOffset(trade, trade.tradeTransInfo.offset || 0, symbol);

            // Handle chart price offset if price is available
            if (trade.tradeTransInfo.price) {
                this.fixChartPriceOffset(
                    trade,
                    trade.tradeTransInfo.price,
                    trade.tradeTransInfo.original_price || null
                );
            }

            this.fixCommentWithStrategy(trade);

            log(LOG_LEVELS.INFO, 'prepare', 'Trade preparation completed', this.enrichContext({
                prepared_trade: trade
            }));

        } catch (error) {
            log(LOG_LEVELS.ERROR, 'prepare', 'Trade preparation failed', this.enrichContext({
                error: error.message,
                trade_info: trade
            }));
            throw error;
        }
    }

    /**
     * Enriches logging context with trade-specific information
     */
    enrichContext(data) {
        return {
            ...data,
            correlation_id: this.marketDataProvider.igApiClient.correlationId,
            component: 'trade-preparation',
            timestamp: Date.now(),
            environment: this.marketDataProvider.igApiClient.isDemo() ? 'demo' : 'live'
        };
    }

    /**
     * Fixes precision for all price-related values in the trade
     */
    async fixPrecision(trade, symbol) {
        try {
            const marketInfo = await this.marketDataProvider.getMarketInfo(symbol);
            if (!marketInfo) {
                throw new Error(`Failed to get market info for ${symbol}, ${JSON.stringify(marketInfo)}`);
            }

            const scalingFactor = marketInfo?.instrument?.scalingFactor ?? 2;
            trade.precision = scalingFactor;

            // Fix SL precision
            if (trade.tradeTransInfo?.sl && !isNaN(trade.tradeTransInfo.sl)) {
                trade.tradeTransInfo.sl = this.truncate(trade.tradeTransInfo.sl, scalingFactor);
            }

            // Fix offset precision
            if (trade.tradeTransInfo?.offset && !isNaN(trade.tradeTransInfo.offset)) {
                trade.tradeTransInfo.offset = this.truncate(trade.tradeTransInfo.offset, scalingFactor);
            }

            // Fix price precision
            if (trade.tradeTransInfo?.price && !isNaN(trade.tradeTransInfo.price)) {
                trade.tradeTransInfo.price = this.truncate(trade.tradeTransInfo.price, scalingFactor);
            }

            log(LOG_LEVELS.DEBUG, 'fixPrecision', 'Precision fixed', this.enrichContext({
                symbol,
                scaling_factor: scalingFactor,
                trade_info: trade.tradeTransInfo
            }));

        } catch (error) {
            log(LOG_LEVELS.ERROR, 'fixPrecision', 'Error fixing precision', this.enrichContext({
                symbol,
                error: error.message
            }));
            throw error;
        }
    }

    /**
     * Handles trailing stop configuration
     */
    fixTrailStopOffset(trade, trailStopOffset, symbol) {
        if (trailStopOffset && trailStopOffset > 0) {
            log(LOG_LEVELS.DEBUG, 'fixTrailStopOffset', 'Setting trailing stop', this.enrichContext({
                trail_stop_offset: trailStopOffset,
                symbol: symbol.epic || trade.tradeTransInfo.symbol
            }));

            trade.tradeTransInfo.trailingStop = true;
            trade.tradeTransInfo.trailingStopDistance = trailStopOffset;
        }
    }

    /**
     * Sets market price if not provided in trade info
     */
    async fixPriceIfNotPresent(trade, symbol) {
        if (!trade?.tradeTransInfo) {
            throw new Error('Trade info object is missing');
        }

        // Check if price is undefined, null, or 0
        if (trade.tradeTransInfo.price === undefined ||
            trade.tradeTransInfo.price === null ||
            isNaN(trade.tradeTransInfo.price)) {
            try {
                const marketData = await this.marketDataProvider.getMarketInfo(symbol);

                if (!marketData?.snapshot) {
                    throw new Error(`Market data snapshot for symbol ${symbol} is missing marketdata: ${JSON.stringify(marketData)} and trade: ${JSON.stringify(trade)}`);
                }

                const { bid, offer } = marketData.snapshot;

                if (typeof bid !== 'number' || typeof offer !== 'number') {
                    throw new Error('Invalid bid/offer prices in market data');
                }

                // Ein Preis von 0 ist jetzt erlaubt, solange er explizit gesetzt wurde
                if (trade.tradeTransInfo.cmd === 0) { // BUY
                    log(LOG_LEVELS.DEBUG, 'fixPriceIfNotPresent', 'Setting buy price', this.enrichContext({
                        price: offer
                    }));
                    trade.tradeTransInfo.price = offer;
                } else if (trade.tradeTransInfo.cmd === 1) { // SELL
                    log(LOG_LEVELS.DEBUG, 'fixPriceIfNotPresent', 'Setting sell price', this.enrichContext({
                        price: bid
                    }));
                    trade.tradeTransInfo.price = bid;
                } else {
                    throw new Error(`Invalid trade command: ${trade.tradeTransInfo.cmd}`);
                }

                if (trade.tradeTransInfo.price === undefined || trade.tradeTransInfo.price === null || isNaN(trade.tradeTransInfo.price)) {
                    throw new Error('Could not set valid trade price from market data');
                }
            } catch (error) {
                log(LOG_LEVELS.ERROR, 'fixPriceIfNotPresent', 'Failed to get market price', this.enrichContext({
                    symbol: symbol || trade.tradeTransInfo.symbol,
                    error: error.message,
                    trade_info: trade.tradeTransInfo
                }));
                throw error;
            }
        }
    }

    /**
     * Recalculates stop loss if necessary
     */
    recalcIfNecessarySL(trade) {
        if (!trade.tradeTransInfo.sl || trade.tradeTransInfo.sl === 0) {
            log(LOG_LEVELS.DEBUG, 'recalcIfNecessarySL', 'Stop loss missing', this.enrichContext({
                trade_info: trade.tradeTransInfo
            }));

            const stopOffset = trade.stopOffset || 0;

            if (stopOffset !== 0) {
                const adjustedOffset = this.fixStopOffset(trade, stopOffset);
                log(LOG_LEVELS.DEBUG, 'recalcIfNecessarySL', 'Calculating new stop', this.enrichContext({
                    stop_offset: adjustedOffset
                }));

                const sl = this.truncate(trade.tradeTransInfo.price + adjustedOffset, trade.precision || 2);
                log(LOG_LEVELS.DEBUG, 'recalcIfNecessarySL', 'New stop calculated', this.enrichContext({
                    new_stop: sl
                }));

                trade.tradeTransInfo.sl = sl;
            }
        }
    }

    /**
     * Adjusts stop offset based on trade direction
     */
    fixStopOffset(trade, stopOffset) {
        if (trade.tradeTransInfo.cmd === 0 && stopOffset > 0) {
            log(LOG_LEVELS.DEBUG, 'fixStopOffset', 'Inverting long stop', this.enrichContext({
                original: stopOffset,
                inverted: -stopOffset
            }));
            return -stopOffset;
        } else if (trade.tradeTransInfo.cmd === 1 && stopOffset < 0) {
            log(LOG_LEVELS.DEBUG, 'fixStopOffset', 'Inverting short stop', this.enrichContext({
                original: stopOffset,
                inverted: -stopOffset
            }));
            return -stopOffset;
        }
        return stopOffset;
    }

    /**
     * Handles chart price offset adjustments
     */
    fixChartPriceOffset(trade, symbolPrice, chartPrice) {
        log(LOG_LEVELS.DEBUG, 'fixChartPriceOffset', 'Processing price offset', this.enrichContext({
            symbol_price: symbolPrice,
            chart_price: chartPrice
        }));

        if (symbolPrice != null && chartPrice != null) {
            const offset = this.truncate(symbolPrice - chartPrice, trade.precision);
            trade.chartPriceOffsetXTBandTradeView = offset;

            if (trade.tradeTransInfo.sl && !isNaN(trade.tradeTransInfo.sl)) {
                trade.tradeTransInfo.original_sl = trade.tradeTransInfo.sl;
                trade.tradeTransInfo.sl = this.truncate(trade.tradeTransInfo.sl + offset, trade.precision);

                log(LOG_LEVELS.DEBUG, 'fixChartPriceOffset', 'Stop loss adjusted', this.enrichContext({
                    offset,
                    original_sl: trade.tradeTransInfo.original_sl,
                    adjusted_sl: trade.tradeTransInfo.sl
                }));
            }
        }
    }

    /**
     * Formats trade comment with strategy information
     */
    fixCommentWithStrategy(trade) {
        if (trade.strategy) {
            const comment = `${trade.strategy}_${trade.timeframe}`;
            log(LOG_LEVELS.DEBUG, 'fixCommentWithStrategy', 'Setting trade comment', this.enrichContext({
                comment
            }));
            trade.tradeTransInfo.comment = comment;
        }
    }

    /**
     * Truncates a number to specified decimal places
     */
    truncate(number, precision) {
        const scale = Math.pow(10, precision);
        return Math.floor(number * scale) / scale;
    }
}

module.exports = IGTradePreparationService;