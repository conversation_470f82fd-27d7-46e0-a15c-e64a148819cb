# Usage and Start

## Development
## siehe https://www.digitalocean.com/community/tutorials/workflow-nodemon


```
nodeman .\server.js
```
 
## Production
```
node .\server.js
```

# XTB-Websocket-Setup
siehe https://github.com/peterszombati/xapi-node

# Setup

Es wird die node 20.x verwendet.
Um NVM (Node Version Manager) unter Windows zu installieren, benutze `nvm-windows`, eine angepasste Version für Windows-Systeme. Hier sind die Schritte:

1. **Download NVM für Windows:**
    - Gehe zur [nvm-windows GitHub-Seite](https://github.com/coreybutler/nvm-windows/releases).
    - Lade die neueste Installationsdatei (`nvm-setup.zip` oder `.exe`) herunter.

2. **Installation:**
    - Entpacke die Datei (falls nötig) und führe die `.exe`-Datei aus.
    - Folge den Anweisungen des Installationsprogramms. Dies wird NVM standardmäßig in `C:\Program Files\nvm` installieren.

3. **Konfiguration:**
    - Nach der Installation sollte sich im Verzeichnis `C:\Program Files\nvm` eine Datei `settings.txt` befinden.
    - Diese Datei konfiguriert die Standardpfade für die Node-Installation und den NPM-Cache. Standardmäßig zeigt sie auf `C:\Program Files\nodejs`. Anpassungen können je nach deinen Bedürfnissen vorgenommen werden.

4. **Überprüfung der Installation:**
    - Öffne ein neues Terminal oder eine Eingabeaufforderung.
    - Gib `nvm version` ein, um zu überprüfen, ob nvm korrekt installiert wurde.

5. **Node.js-Versionen verwalten:**
    - Installiere die gewünschte Node-Version mit: `nvm install <version>`, z.B. `nvm install 20`.
    - Wechsle zu einer installierten Version mit: `nvm use <version>`, z.B. `nvm use 20`.
    - Liste alle installierten Node-Versionen auf mit: `nvm list`.

Stelle sicher, dass du die Eingabeaufforderung oder das Terminal nach der Installation neu startest, damit die Umgebungsänderungen wirksam werden.

```
yarn add moment
yarn add openai
yarn add html-to-text
yarn add groq-sdk
yarn add @mistralai/mistralai
yarn add @google/generative-ai
yarn add @hyperdx/node-opentelemetry 
yarn add swagger-ui-express swagger-jsdoc --save
```

Zusätzlich muss einmalig die .env-Datei angelegt werden. Die Daten können vom Produktionsserver kopiert werden.

# Monitoring

Die Daten werden für die Kostenkontrolle und das Monitoring in die Helicone-Cloud geschickt. Die Daten können unter folgendem Link eingesehen werden:

![img.png](docs/dashboard-helicone.png)

- UI: https://www.helicone.ai/dashboard
- Docs: https://docs.helicone.ai/getting-started/integration-method/openai-proxy


# Caching

Da Helicon-Caching anbietet, werden die Aufrufe über die Plattform geführt.

# Prompting

Um die Prompts versioniert nutzen zu können, wird Langfuse als Plattform benutzt.
Die Prompts finden sich unter:

- UI: https://cloud.langfuse.com/project/clnbnz99y0000if08xnid4g3k/prompts
- Docs: https://langfuse.com/docs/prompts

# Emfohlene Modelle

- llama-3.3-70b-versatile
- claude-3-5-sonnet-20240620
- claude-3-5-haiku-20241022
- gpt-4o-mini
- mistral-large-latest
- gemini-2.0-flash-exp

# API-Key Authentication

Die API ist durch einen API-Key geschützt. Dieser muss in der `.env`-Datei konfiguriert werden:

```
API_KEY=your-api-key-here
```

Der Default-API-Key ist: `ml!algotrader`

Der API-Key kann auf zwei Arten übergeben werden:

1. Als HTTP-Header:
```
X-API-Key: your-api-key-here
```

2. Als Query-Parameter:
```
http://localhost:8080/api-docs?api_key=ml!algotrader
```

Ohne gültigen API-Key wird ein 401 Unauthorized Error zurückgegeben.
