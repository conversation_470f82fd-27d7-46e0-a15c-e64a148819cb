const db = require("../../configs/config_db");

async function database_init(p_newsItem) {
    try {
        const p_query = {
            sql: 'REPLACE INTO news(uuid,time,title,bodylen,body, gpt_summarize, logo, gpt_tldr, gpt_topic) values (?,?,?,?,?,?,?,?,?)',
            values: [
                p_newsItem.key, 
                p_newsItem.time!=null?new Date(p_newsItem.time):null, 
                p_newsItem.title, 
                p_newsItem.body.length, 
                p_newsItem.body, 
                p_newsItem.gpt_summarize?p_newsItem.gpt_summarize:null,
                p_newsItem.logo?p_newsItem.logo:"default-logo.png",
                p_newsItem.gpt_tldr?p_newsItem.gpt_tldr:"",
                p_newsItem.gpt_topic?p_newsItem.gpt_topic:""                
            ],
            types: ['string', 'date', 'string', 'int', 'string', 'string'],
            bigIntAsNumber: true,
            decimalAsNumber: true,
            insertIdAsNumber: true,
            timezone: 'de_de'
        }
        let l_result = await db.pool.query(p_query);
        if (process.env.NODE_ENV !== 'production')
           console.log("[gpt_databaseHelper][database_init] Database update result:", l_result);
        p_newsItem.uuid=p_newsItem.key;
        return p_newsItem;
    } catch (err) {
        console.error("db-update: ",err);
        throw err;
    }
}

async function database_update(p_newsItem) {
    try {
        if (process.env.NODE_ENV !== 'production')
           console.log("[gpt_databaseHelper][database_update] Updating news item:", p_newsItem.tldr);
        const p_query = {
            sql: 'UPDATE news ' +
                '    set gpt_summarize=?,' +
                '        gpt_summarize_date=?,' +
                '        optimized_textbody=?, ' +
                '        bodylen=?, ' +
                '        gpt_prompt_version=?,' +
                '        gpt_refresh_reason=?, ' +
                '        gpt_prompt=?, ' +
                '        logo=?, ' +
                '        gpt_tldr=?, ' +
                '        gpt_topic=?' +
                '  where uuid=?',
            values: [
                p_newsItem.gpt_summarize?p_newsItem.gpt_summarize:"",
                p_newsItem.gpt_summarize_date?p_newsItem.gpt_summarize_date:new Date(),
                p_newsItem.optimized_textbody?p_newsItem.optimized_textbody:"",
                p_newsItem.body.length?p_newsItem.body.length:0,
                p_newsItem.gpt_prompt_version?p_newsItem.gpt_prompt_version:-1,
                p_newsItem.gpt_refresh_reason?p_newsItem.gpt_refresh_reason:"",
                p_newsItem.gpt_prompt?p_newsItem.gpt_prompt:"",
                p_newsItem.logo?p_newsItem.logo:"xtb-logo.png",
                p_newsItem.gpt_tldr?p_newsItem.gpt_tldr:"",
                p_newsItem.gpt_topic?p_newsItem.gpt_topic:"",   
                p_newsItem.uuid],
            types: ['string', 'date', 'string', 'int','string', 'string', 'string', 'string', 'string', 'string', 'string'],
            bigIntAsNumber: true,
            decimalAsNumber: true,
            insertIdAsNumber: true,
            timezone: 'de_de'
        }
        //console.log("db-update: ",p_query);
        let l_result = await db.pool.query(p_query);
        if (process.env.NODE_ENV !== 'production')
           console.log("[gpt_databaseHelper][database_update] Database final update result:", l_result);
        return l_result;
    } catch (err) {
        console.error("try to update with:", p_newsItem);
        console.error("db-final-update-failed: ",err);
        throw err;
    }
}

async function database_read(p_newsItem_key) {
    try {
        const p_query = {
            sql: 'SELECT * FROM news where uuid=?',
            values: [p_newsItem_key],
            types: ['string'],
            bigIntAsNumber: true,
            decimalAsNumber: true,
            insertIdAsNumber: true,
            timezone: 'de_de'
        }
        let l_result = await db.pool.query(p_query);
        if (process.env.NODE_ENV !== 'production')
           console.log("[gpt_databaseHelper][database_read] Database read result:", { itemCount: l_result.length, key: p_newsItem_key });
        if (l_result.length > 0)
            return l_result[0]; else
            return {
                uuid: null,
                gpt_summarize: null,
            };
    } catch (err) {
        console.error("db-read: ",err);
        throw err;
    }
}

async function getConcatenatedNewsSummaries(p_limit){
    const l_charLimit = 5500;
    try {
        const p_query = {
            sql: `CALL get_concatenated_summaries(${l_charLimit},?)`,
            values: [p_limit],
            types: ['int'],
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        if (process.env.NODE_ENV !== 'production')
          console.log("[gpt_databaseHelper][getConcatenatedNewsSummaries] Query:", p_query);
        let l_result= await db.pool.query(p_query);
        let l_return = l_result[0][0].concatenated_summaries;
        if (process.env.NODE_ENV !== 'production')
          console.log("[gpt_databaseHelper][getConcatenatedNewsSummaries] Concatenated summaries (Limit, String-Len):", l_charLimit, l_return.length);
        return l_return;
    } catch (err) {
        throw err;
    }
};

async function getNewsSummariesFromDay(p_limit){
    try {
        const p_query = {
            sql:`SELECT * FROM news 
                   WHERE DATE(time) >= (select date_sub(max(DATE(TIME)), INTERVAL 1 DAY) FROM news) 
                     and gpt_summarize!="" 
                     and (uuid not like 'GPTDaySummary_%')  
                     and (uuid not like 'Factor-Map_%') 
                     and gpt_topic not in ('KI','Werbung', 'Auto' )
                   ORDER BY time DESC LIMIT ?`,
            values: [p_limit],
            types: ['int'],
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        if (process.env.NODE_ENV !== 'production')
          console.log("[gpt_databaseHelper][getNewsSummariesFromDay] Query:", p_query);
        let l_result= await db.pool.query(p_query);
        if (process.env.NODE_ENV !== 'production')
          console.log("[gpt_databaseHelper][getNewsSummariesFromDay] Day news count:", l_result.length);
        return l_result;
    } catch (err) {
        throw err;
    }
};

async function getLatestGPTDaySummaries(p_limit) {
    try {
        const p_query = {
            sql: `SELECT * FROM news WHERE uuid LIKE 'GPTDaySummary_%' ORDER BY gpt_summarize_date DESC LIMIT ?`,
            values: [p_limit],
            types: ['int'],
            bigIntAsNumber: true,
            timezone: 'de_de'
        }
        if (process.env.NODE_ENV !== 'production')
          console.log("[gpt_databaseHelper][getLatestGPTDaySummaries] Executing query:", p_query);
        let l_result = await db.pool.query(p_query);
        if (process.env.NODE_ENV !== 'production')
           console.log(`[gpt_databaseHelper][getLatestGPTDaySummaries] Retrieved ${l_result.length} GPTDaySummary entries`);
        return l_result;
    } catch (err) {
        console.error("Error in getLatestGPTDaySummaries:", err);
        throw err;
    }
}

async function createFactorMapEntry(gptSummarize, gptPromptVersion, scoreReasoning) {
    if (!gptSummarize || typeof gptPromptVersion !== 'number' || !scoreReasoning) {
        throw new Error('Invalid input parameters');
    }

    try {
        const currentDate = new Date();
        const formattedDate = currentDate.toISOString().split('T')[0]; // Format: YYYY-MM-DD
        const uuid = `Factor-Map_${formattedDate}`;
        const title = `Factor Map für ${formattedDate}`;

        // Überprüfen und ggf. kürzen des scoreReasoning
        const maxBodyLength = 65535; // Maximale Länge für TEXT-Feld in MySQL
        let scoreData;
        try {
            scoreData = JSON.parse(scoreReasoning).score;
            if (process.env.NODE_ENV !== 'production')
              console.log("[gpt_databaseHelper][createFactorMapEntry] Score extracted from scoreReasoning:", scoreData);
        } catch (error) {
            console.error("Error parsing scoreReasoning JSON:", error);
            scoreData = {};
        }
        const score = Number(scoreData) || 0;
        const truncatedScoreReasoning = scoreReasoning.length > maxBodyLength
            ? scoreReasoning.substring(0, maxBodyLength)
            : scoreReasoning;

        if (process.env.NODE_ENV !== 'production')
           console.log("[gpt_databaseHelper][createFactorMapEntry] FactorMap entry now created or updated:", uuid);
        const query = {
            sql: 'REPLACE INTO news (uuid, time, gpt_summarize, gpt_summarize_date, gpt_prompt_version, body, title, score) VALUES (?, ?, ?, ?, ?, ?, ?,?)',
            values: [uuid, currentDate, gptSummarize, currentDate, gptPromptVersion, truncatedScoreReasoning, title, score],
            types: ['string', 'date', 'string', 'date', 'int', 'string', 'string', 'int'],
            bigIntAsNumber: true,
            decimalAsNumber: true,
            insertIdAsNumber: true,
            timezone: 'de_de'
        };

        let result = await db.pool.query(query);
        if (process.env.NODE_ENV !== 'production')
           console.log("[gpt_databaseHelper][createFactorMapEntry] FactorMap entry created or updated:", result);
        return { 
            uuid, 
            time: currentDate, 
            gpt_summarize: gptSummarize, 
            gpt_summarize_date: currentDate, 
            gpt_prompt_version: gptPromptVersion,
            body: truncatedScoreReasoning,
            score: score,
            title
        };
    } catch (err) {
        console.error("Error creating or updating FactorMap entry:", err);
        throw err;
    }
}


async function getLatestGPTDaySummariesForAI(limit = 5) {
    try {
        const summaries = await getLatestGPTDaySummaries(limit);
        
        let processedSummaries = '';
        summaries.forEach(summary => {
            const date = new Date(summary.time).toLocaleDateString('de-DE');
            const bodyContent = summary.gpt_summarize;
            processedSummaries += `<gptSummary_${date}>${bodyContent}</gptSummary_${date}>`;
            if (process.env.NODE_ENV !== 'production')
                console.log(`[gpt_databaseHelper][getLatestGPTDaySummariesForAI] <gptSummary_${date}>`);
        });

        return processedSummaries;
    } catch (error) {
        console.error("Error in getLatestGPTDaySummariesForAI:", error);
        throw error;
    }
}

/**
 * Log an AI request to the database
 * @param {Object} requestData - Data about the request
 * @param {string} requestData.request_id - Unique identifier for the request
 * @param {string} requestData.llm_model - The LLM model used
 * @param {Array|Object} requestData.prompt - The input messages/prompt
 * @param {string} requestData.response - The response from the LLM
 * @param {number} requestData.processing_time_ms - Time taken to process the request
 * @param {string} requestData.status - 'success' or 'error'
 * @param {string} [requestData.error_message] - Error message if applicable
 * @param {string} [requestData.assistant_id] - Assistant ID if applicable
 * @param {string} [requestData.prompt_id] - Prompt ID if applicable
 * @param {number} [requestData.prompt_version] - Prompt version if applicable
 * @param {number} [requestData.token_count] - Total token count if available
 * @param {number} [requestData.token_input_count] - Input token count if available
 * @param {number} [requestData.token_output_count] - Output token count if available
 * @returns {Promise<Object>} - Result of the database operation
 */
async function logAIRequest(requestData) {
    try {
        if (process.env.NODE_ENV !== 'production')
            console.log('[gpt_databaseHelper][logAIRequest] Logging AI request:', {
                request_id: requestData.request_id,
                llm_model: requestData.llm_model,
                status: requestData.status
            });

        // Convert prompt to JSON string if it's an object or array
        let promptText = requestData.prompt;
        if (typeof promptText !== 'string') {
            try {
                promptText = JSON.stringify(promptText);
            } catch (err) {
                console.error('[gpt_databaseHelper][logAIRequest] Error stringifying prompt:', err);
                promptText = 'Error converting prompt to string';
            }
        }

        const query = {
            sql: `INSERT INTO ai_requests (
                request_id,
                timestamp,
                llm_model,
                prompt_text,
                response_text,
                processing_time_ms,
                status,
                error_message,
                assistant_id,
                prompt_id,
                prompt_version,
                token_count,
                token_input_count,
                token_output_count
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            values: [
                requestData.request_id,
                new Date(),
                requestData.llm_model,
                promptText,
                requestData.response,
                requestData.processing_time_ms,
                requestData.status,
                requestData.error_message || null,
                requestData.assistant_id || null,
                requestData.prompt_id || null,
                requestData.prompt_version || null,
                requestData.token_count || null,
                requestData.token_input_count || null,
                requestData.token_output_count || null
            ],
            types: [
                'string', 'date', 'string', 'string', 'string',
                'int', 'string', 'string', 'string', 'string',
                'int', 'int', 'int', 'int'
            ],
            bigIntAsNumber: true,
            decimalAsNumber: true,
            insertIdAsNumber: true,
            timezone: 'de_de'
        };

        const result = await db.pool.query(query);
        
        if (process.env.NODE_ENV !== 'production')
            console.log('[gpt_databaseHelper][logAIRequest] AI request logged successfully:', {
                request_id: requestData.request_id,
                result
            });
            
        return { success: true, result };
    } catch (err) {
        // Log the error but don't throw it
        console.error('[gpt_databaseHelper][logAIRequest] Error logging AI request:', err);
        console.error('[gpt_databaseHelper][logAIRequest] Request data:', {
            request_id: requestData.request_id,
            llm_model: requestData.llm_model,
            status: requestData.status
        });
        
        // Return failure but don't throw
        return {
            success: false,
            error: err.message
        };
    }
}

module.exports = {
    database_init,
    database_update,
    database_read,
    getConcatenatedNewsSummaries,
    getNewsSummariesFromDay,
    getLatestGPTDaySummaries,
    createFactorMapEntry,
    getLatestDaySummariesForAI: getLatestGPTDaySummariesForAI,
    logAIRequest
}


