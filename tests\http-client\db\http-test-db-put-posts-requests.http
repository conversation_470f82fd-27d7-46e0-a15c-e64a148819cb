### Trading-Mode: Set: IG-D1: On
PUT {{API_BASE_URL}}/api/v1/db/tradingmode?refID=IG-D1&value=on
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: put tradingmode");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}
### Update Symbol-Setup: DeadZones with Source
PUT {{API_BASE_URL}}/api/v1/db/symbol_setup/US500?deadZoneLow=4134&deadZoneHigh=4163&deadZoneValidUntil=2023-04-24&deadZoneUpdateSource=Postman
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: put symbol-setup-updates");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}
### Update Symbol-Setup: DeadZones-Only
PUT {{API_BASE_URL}}/api/v1/db/symbol_setup/US500?deadZoneLow=4134&deadZoneHigh=4163
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: put symbol-setup-updates");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}
### Update Calendar-toggles: High Volume Trades
PUT {{API_BASE_URL}}/api/v1/db/calendar/toggles/1436?toggle_deactivate_high_volume_trades=1&toggle_close_all_positions_before=1&toggle_actiontime_minutes_window_after=42&toggle_actiontime_minutes_window_before=5
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}
### Update Strategy-Activation: 
PUT {{API_BASE_URL}}/api/v1/db/strategies/1236?activate_ig_p1=0&activate_ig_d1=0&activate_high_volume=1&activate_trailing_stops=0
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}
### Update Mirror-trading-settings: IG-D1-->IG-P1
PUT {{API_BASE_URL}}/api/v1/db/mirror_trading_settings?sourceAccountRefID=IG-D1&targetAccountRefID=IG-P1&default_minProfitForSourceTrade=5&default_minProfitForBreakEvenStopp=80&default_minProfitForActivateTakeProfitTrailingStopp=500&default_useDeadZones=1&default_useSLFromOriginTrade=1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: put-mirror-trading-settings");
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.state === "successful", "State is not successful");
    });
%}