{"paths": {"/api/v1/ai/predictions": {"get": {"summary": "Get AI price predictions", "description": "Retrieves AI-generated price predictions for specified trading symbols", "tags": ["AI Integration"], "parameters": [{"in": "query", "name": "symbol", "schema": {"type": "string"}, "description": "Trading symbol (e.g. EURUSD, USDJPY)", "required": true}, {"in": "query", "name": "timeframe", "schema": {"type": "integer", "enum": [15, 30, 60, 240, 1440], "default": 60}, "description": "Prediction timeframe in minutes"}], "responses": {"200": {"description": "AI predictions retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"symbol": {"type": "string", "example": "EURUSD"}, "timeframe": {"type": "integer", "example": 60}, "predictions": {"type": "array", "items": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "direction": {"type": "string", "enum": ["UP", "DOWN", "NEUTRAL"]}, "confidence": {"type": "number", "format": "float", "minimum": 0, "maximum": 1}, "target_price": {"type": "number", "format": "float"}}}}}}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/ai/prompt": {"post": {"summary": "Execute an AI prompt", "description": "Executes a predefined AI prompt with the specified LLM model and variables. This endpoint allows for flexible interaction with various LLM models using structured prompts.", "tags": ["AI Integration"], "parameters": [{"in": "query", "name": "promptID", "schema": {"type": "string"}, "description": "ID of the predefined prompt to execute (e.g., n8n.mail.summary, n8n.mail.relevancescore)", "required": true, "example": "n8n.mail.summary"}, {"in": "query", "name": "llmModel", "schema": {"type": "string", "enum": ["llama-3.3-70b-versatile", "claude-3-5-haiku-20241022", "gpt-4o-mini", "mistral-large-latest", "gemini-2.5-pro-exp-03-25", "gemini-2.0-flash"]}, "description": "LLM model to use for processing the prompt", "example": "gpt-4o-mini"}, {"in": "query", "name": "promptVersion", "schema": {"type": "integer", "default": 1}, "description": "Version of the prompt to use"}, {"in": "query", "name": "promptTitle", "schema": {"type": "string"}, "description": "Alternative to promptID, specifies the title of the prompt to execute", "example": "ki-algobot.llm-news-aggregation"}], "requestBody": {"description": "Variables to be used in the prompt template", "required": true, "content": {"text/plain": {"schema": {"type": "string", "description": "JSON string containing variables for the prompt", "example": "{\n  \"TEXT_TO_ANALYZE\": \"Nvidia's AI system ACE will power in-game character interactions in Mecha Break, a mech battle game launching in 2025.\"\n}"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PromptVariables"}}}}, "responses": {"200": {"description": "Prompt executed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"result": {"type": "string", "description": "The LLM-generated response to the prompt"}, "model": {"type": "string", "description": "The LLM model used for processing"}, "promptID": {"type": "string", "description": "The ID of the prompt that was executed"}, "promptVersion": {"type": "integer", "description": "The version of the prompt that was used"}}}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}