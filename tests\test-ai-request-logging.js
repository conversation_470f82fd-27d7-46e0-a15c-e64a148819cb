const { requestLLM } = require('../services/llm-service');
const db = require('../configs/config_db');

async function testLogging() {
    try {
        console.log('Testing AI request logging...');
        
        // Test a successful request
        const result = await requestLLM(
            [{ role: 'user', content: 'Hello, how are you?' }],
            'gpt-3.5-turbo'
        );
        
        console.log('Request completed successfully');
        console.log('Response:', result.substring(0, 100) + '...');
        
        // Query the database to verify logging
        const query = {
            sql: 'SELECT * FROM ai_requests ORDER BY id DESC LIMIT 1',
            bigIntAsNumber: true,
            decimalAsNumber: true
        };
        
        const logEntries = await db.pool.query(query);
        console.log('Latest log entry:', logEntries[0]);
        
        console.log('Test completed successfully');
    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        // Close the database connection
        await db.pool.end();
    }
}

testLogging();