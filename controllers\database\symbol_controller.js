const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const pipValueService = require('../../services/pip_value_service');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { executeQuery } = require('../../services/database_service');

async function getAllUsedSymbolsWithPipValuesIndependent() {
    try {
        const query = {
            sql: `SELECT s.symbol, s.pipValuePerLotInProfitEUR 
                   from symbols s  
                  where s.symbol in (select symbol from \`symbol_setups\`)
                    and s.pipValuePerLotInProfitEUR is not null`,
            bigIntAsNumber: true,
            timezone: 'de_de'
        };

        log(LOG_LEVELS.DEBUG, 'getAllUsedSymbolsWithPipValuesIndependent', 'Executing query');
        const queryResult = await executeQuery(query, []);

        log(LOG_LEVELS.INFO, 'getAllUsedSymbolsWithPipValuesIndependent', 'Successfully retrieved data', {
            result: queryResult
        });


        return queryResult;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getAllUsedSymbolsWithPipValuesIndependent', 'Failed to fetch pip values', {
            error: err.message,
            stack: err.stack
        });
        return []; // Return an empty array in case of error
    }
}

async function getAllUsedSymbolsWithPipValues(req, res) {
    try {
        const result = await withCacheWrapper(
            'GENERAL',
            'getAllUsedSymbolsWithPipValues',
            getAllUsedSymbolsWithPipValuesIndependent
        );
        
        log(LOG_LEVELS.INFO, 'getAllUsedSymbolsWithPipValues', 'Sending response', {
            resultCount: Array.isArray(result) ? result.length : 0
        });
        
        res.json(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getAllUsedSymbolsWithPipValues');
        res.status(errorResponse.status).json(errorResponse);
    }
}

/**
 * Lädt die Pip-Werte aller Symbole in eine Map und aktualisiert den PipValue-Service
 */
async function loadPipValuesMap() {
    try {
        const result = await getAllUsedSymbolsWithPipValuesIndependent();
        const pipValuesMap = new Map();
        
        if (Array.isArray(result)) {
            result.forEach(row => {
                pipValuesMap.set(row.symbol, row.pipValuePerLotInProfitEUR);
            });
        } else {
            log(LOG_LEVELS.WARN, 'loadPipValuesMap', 'Unexpected result format', {
                result: result
            });
        }
        
        log(LOG_LEVELS.INFO, 'loadPipValuesMap', 'PipValues loaded', {
            size: pipValuesMap.size
        });
        
        pipValueService.setPipValues(pipValuesMap);
        return pipValuesMap;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'loadPipValuesMap', 'Failed to load pip values', {
            error: err.message,
            stack: err.stack
        });
        return new Map();
    }
}

module.exports = {
    getAllUsedSymbolsWithPipValues,
    getAllUsedSymbolsWithPipValuesIndependent,
    loadPipValuesMap
};
