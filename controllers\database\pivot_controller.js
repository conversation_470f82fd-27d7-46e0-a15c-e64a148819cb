const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { TIME_CONSTANTS } = require('../../configs/constants');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');
const { buildPivotPointsQuery } = require('./queries/pivot_point_queries');

async function getPivotpointsIndependent(days = TIME_CONSTANTS.DEFAULT_DAYS.PIVOT_POINTS) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getPivotpointsIndependent', 'Starting pivot points retrieval', {
            days
        });

        // Validate days parameter
        days = validateNumericParam(days, {
            defaultValue: TIME_CONSTANTS.DEFAULT_DAYS.PIVOT_POINTS,
            min: 1,
            max: 3650,
            paramName: 'days'
        });

        log(LOG_LEVELS.DEBUG, 'getPivotpointsIndependent', 'Executing query', {
            days
        });

        const query = buildPivotPointsQuery(days);
        const result = await executeQuery(query);
        
        if (!result) {
            throw new DatabaseError('Failed to fetch pivot points', { days });
        }

        log(LOG_LEVELS.INFO, 'getPivotpointsIndependent', 'Successfully retrieved pivot points', {
            days,
            resultCount: result?result.length:0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getPivotpointsIndependent', 'Failed to fetch pivot points', {
            error: err.message,
            stack: err.stack,
            days
        });
        throw err;
    } finally {
        logPerformance('getPivotpointsIndependent', startTime);
    }
}


async function getPivotpoints(req, res) {
    try {
        const days = req.query.days ? parseInt(req.query.days) : 3000;

        const result = await withCacheWrapper(
            'GENERAL',
            'getPivotpoints',
            () => getPivotpointsIndependent(days),
            [days]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getPivotpoints');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getPivotpoints,
    getPivotpointsIndependent
};
