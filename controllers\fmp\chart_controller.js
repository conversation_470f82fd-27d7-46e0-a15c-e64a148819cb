const { LoggingService } = require('../../services/logging_service');
const FMPChartService = require('../../services/fmp_chart_service');

class FMPChartController {
    constructor() {
        this.logger = LoggingService.getInstance();
        this.chartService = new FMPChartService(this.logger);
    }


    async fetchIntradayChart(req, res) {
        try {
            const { timeframe, symbol, from, to } = req.query;
            let extended = req.query.extended === 'true';
            
            if (!timeframe || !symbol) {
                return res.status(400).json({
                    status: 'error',
                    message: 'Missing required parameters: timeframe and symbol are required'
                });
            }
            
            this.logger.info('Intraday chart fetch request received', {
                timeframe,
                symbol,
                from,
                to,
                extended,
                requestId: req.id
            });

            const chartData = await this.chartService.getIntradayChart(
                timeframe, 
                symbol, 
                from, 
                to, 
                extended
            );
            
            return res.json({
                status: 'success',
                data: chartData,
                count: chartData.length,
                params: {
                    timeframe,
                    symbol,
                    from: from || 'not specified',
                    to: to || 'not specified',
                    extended
                }
            });
        } catch (error) {
            this.logger.error('Intraday chart fetch failed', {
                error: error.message,
                requestId: req.id
            });

            const statusCode = error.message.includes('Invalid timeframe') ? 400 : 500;

            return res.status(statusCode).json({
                status: 'error',
                message: 'Failed to fetch intraday chart data',
                error: error.message
            });
        }
    }
}

module.exports = new FMPChartController();