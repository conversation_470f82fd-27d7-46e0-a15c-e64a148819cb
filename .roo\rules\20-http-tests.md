# HTTP Tests Best Practices

This document outlines the best practices for creating and maintaining HTTP tests for the Algotrader API, based on the current implementation in the `tests/http-client/db` directory.

## Table of Contents

1. [File Organization](#file-organization)
2. [Naming Conventions](#naming-conventions)
3. [Request Structure](#request-structure)
4. [Test Scripts](#test-scripts)
5. [Assertions](#assertions)
6. [Variables and Parameters](#variables-and-parameters)
7. [Documentation](#documentation)
8. [Common Test Scenarios](#common-test-scenarios)

## File Organization

- **Group by Functionality**: Organize test files by API functionality or resource type (e.g., `http-test-db-requests-calendar.http`, `http-test-db-requests-news.http`).
- **Separate PUT/POST Requests**: Keep modification operations (PUT/POST) in dedicated files (e.g., `http-test-db-put-posts-requests.http`).
- **Logical Sequence**: Arrange requests in a logical sequence within each file, typically following the flow of operations a user might perform.

## Naming Conventions

- **File Names**: Use the pattern `http-test-[api-area]-[resource]-[optional-specifics].http`
  - Example: `http-test-db-requests-mirrorTradingLogs.http`
- **Request Names**: Use descriptive names with the `@name` annotation
  - Example: `// @name calendar-toggles`
- **Consistent Casing**: Use camelCase for request names and resource identifiers

## Request Structure

Each request should follow this structure:

```
### [Optional Description]

// @name request-name
[HTTP_METHOD] {{API_BASE_URL}}/api/v1/[path]?[query-parameters]
Content-Type: application/json
[Other Headers]

[Request Body if applicable]

> {%
    [Test Script]
%}
```

Example:
```
### Update Symbol-Setup: DeadZones with Source

PUT {{API_BASE_URL}}/api/v1/db/symbol_setup/US500?deadZoneLow=4134&deadZoneHigh=4163&deadZoneValidUntil=2023-04-24&deadZoneUpdateSource=Postman
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: put symbol-setup-updates");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}
```

## Test Scripts

Test scripts should be enclosed in `> {% %}` tags and include:

1. **Basic Success Test**: Always verify that the request executed successfully
   ```javascript
   client.test("Request executed successfully", function() {
       client.assert(response.status === 200, "Response status is not 200");
   });
   ```

2. **Logging**: Include descriptive logs to help with debugging
   ```javascript
   client.log("#Test: Calendar Toggles Update");
   ```

3. **Response Structure Tests**: Verify the structure of the response when applicable
   ```javascript
   client.test("Response has correct structure", function() {
       var jsonData = response.body;
       client.assert(jsonData.hasOwnProperty('state'), "Response does not have 'state' property");
       client.assert(jsonData.state === "successful", "State is not 'successful'");
   });
   ```

4. **Array Tests**: For responses returning arrays, verify array properties
   ```javascript
   client.test("Results is an array", function() {
       client.assert(Array.isArray(response.body.results), "Results is not an array");
   });
   ```

5. **Item Property Tests**: For array items, verify expected properties
   ```javascript
   if (response.body.results.length > 0) {
       client.test("Result items have correct properties", function() {
           const firstItem = response.body.results[0];
           client.assert(firstItem.hasOwnProperty('targetRefID'), "Item does not have 'targetRefID' property");
           // Additional property checks...
       });
   }
   ```

## Assertions

Use consistent assertion patterns:

1. **Status Code**: Always check for the expected status code
   ```javascript
   client.assert(response.status === 200, "Response status is not 200");
   ```

2. **Response Properties**: Verify critical response properties
   ```javascript
   client.assert(jsonData.state === "successful", "State is not 'successful'");
   ```

3. **Array Validation**: For array responses, validate array type and content
   ```javascript
   client.assert(Array.isArray(response.body), "Response is not an array");
   ```

4. **Conditional Assertions**: Use conditional logic for optional checks
   ```javascript
   if (response.body.results.length > 0) {
       // Assertions on array items
   }
   ```

## Variables and Parameters

1. **Environment Variables**: Use environment variables for base URLs and other shared values
   ```
   {{API_BASE_URL}}/api/v1/db/calendar/list
   ```

2. **Query Parameters**: Format query parameters clearly with descriptive names
   ```
   GET {{API_BASE_URL}}/api/v1/db/statistics/day?refID=IG-P1&startdate=2025-01-01
   ```

3. **Path Parameters**: Use descriptive path parameters for resource identifiers
   ```
   GET {{API_BASE_URL}}/api/v1/db/symbol_setup/IX.D.NASDAQ.IFE.IP
   ```

4. **Optional Parameters**: Test endpoints with both required and optional parameters
   ```
   // With optional parameters
   GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/profits?targetRefID=D2&refreshData=true
   
   // Without optional parameters
   GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/profits?targetRefID=P1
   ```

## Documentation

1. **Request Description**: Use the `###` comment to provide a clear description of what the request does
   ```
   ### Update Calendar-toggles: High Volume Trades
   ```

2. **Request Naming**: Use the `@name` annotation to give each request a unique identifier
   ```
   // @name calendar-deltareport : calendarID
   ```

3. **Separator Comments**: Use `###` as separators between requests for better readability

## Common Test Scenarios

Include tests for these common scenarios:

1. **Basic Retrieval**: Test simple GET requests to retrieve data
   ```
   GET {{API_BASE_URL}}/api/v1/db/symbol_setups
   ```

2. **Filtered Retrieval**: Test GET requests with query parameters for filtering
   ```
   GET {{API_BASE_URL}}/api/v1/db/trade_history?refID=IG-D1&symbol=IX.D.NASDAQ.IFE.IP&days=2
   ```

3. **Updates**: Test PUT requests to update resources
   ```
   PUT {{API_BASE_URL}}/api/v1/db/symbol_setup/US500?deadZoneLow=4134&deadZoneHigh=4163
   ```

4. **Time-Based Queries**: Test requests with time-based parameters
   ```
   GET {{API_BASE_URL}}/api/v1/db/statistics/day?refID=IG-P1&startdate=2025-01-01
   ```

5. **Limit and Pagination**: Test requests with limit parameters
   ```
   GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs?targetRefID=P1&sourceRefID=D1&limit=2
   ```

6. **Toggle Operations**: Test requests that toggle features or settings
   ```
   PUT {{API_BASE_URL}}/api/v1/db/calendar/toggles/1436?toggle_deactivate_high_volume_trades=1
   ```

7. **Refresh Operations**: Test requests with refresh parameters
   ```
   GET {{API_BASE_URL}}/api/v1/db/mirrorTradingLogs/profits?targetRefID=D2&refreshData=true
   ```

## Example Test File Structure

A well-structured test file should follow this pattern:

```
###
// @name descriptive-request-name
[HTTP_METHOD] [URL]
[Headers]

> {%
    // Basic success test
    client.test("Request executed successfully", function() {
        client.log("#Test: Description");
        client.assert(response.status === 200, "Response status is not 200");
    });
    
    // Response structure test
    client.test("Response has correct structure", function() {
        // Structure assertions
    });
    
    // Optional: Additional specific tests
%}

###
// Next request...
```

By following these best practices, you'll create consistent, maintainable, and effective HTTP tests for the Algotrader API.