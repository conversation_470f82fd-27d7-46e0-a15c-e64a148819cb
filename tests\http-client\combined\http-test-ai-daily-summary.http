
###

// @name Get AI Daily Summary
GET {{API_BASE_URL}}/api/v1/db/ai_daily_summary?refID=P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("AI Daily Summary Test");
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Response has correct structure", function() {
        var jsonData = JSON.parse(response.body);
        client.assert(jsonData.hasOwnProperty('daily_summary'), "Response does not have daily_summary");
    });

    client.test("Daily summary is not empty", function() {
        var jsonData = JSON.parse(response.body);
        client.assert(jsonData.daily_summary && jsonData.daily_summary.length > 0, "Daily summary is empty");
    });
%}

###

// @name Get AI Daily Summary with different refID
GET {{API_BASE_URL}}/api/v1/db/ai_daily_summary?refID=SimD2
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("AI Daily Summary Test with different refID");
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Response has correct structure", function() {
        var jsonData = JSON.parse(response.body);
        client.assert(jsonData.hasOwnProperty('daily_summary'), "Response does not have daily_summary");
    });

    client.test("Daily summary is not empty", function() {
        var jsonData = JSON.parse(response.body);
        client.assert(jsonData.daily_summary && jsonData.daily_summary.length > 0, "Daily summary is empty");
    });
%}

###
