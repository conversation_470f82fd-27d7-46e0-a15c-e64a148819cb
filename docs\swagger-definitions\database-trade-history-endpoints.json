{"paths": {"/api/v1/db/trade_history": {"get": {"summary": "Handelshistorie abrufen", "description": "Ruft die Handelshistorie für ein bestimmtes Konto ab, optional gefiltert nach Symbol und Zeitraum.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Informationen zu jedem Trade\n- Filterbar nach Symbol und Zeitraum in Tagen\n- Standardmäßig werden die letzten 7 Tage abgerufen (oder 3 Tage bei Filterung nach Symbol)\n\nAnwendungsfälle:\n- Analyse der Trading-Performance\n- Überprüfung vergangener Trades\n- Berechnung von Performance-Metriken\n- Identifizierung von erfolgreichen und nicht erfolgreichen Trading-Mustern", "tags": ["Trade History"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}, {"in": "query", "name": "symbol", "required": false, "schema": {"type": "string"}, "description": "Trading-Symbol, nach dem gefiltert werden soll", "example": "EURUSD"}, {"in": "query", "name": "days", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 365}, "description": "Anzahl der Tage in der Vergangenheit, für die Trades abgerufen werden sollen. Standardwert: 7 Tage (ohne Symbol-Filter) oder 3 Tage (mit Symbol-Filter)", "example": 7}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Handelshistorie", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeHistoryItem"}}, "example": [{"trade_id": 12345, "account": 1, "refId": "IG-D1", "symbol": "EURUSD", "cmd": 0, "volume": 1.5, "open_price": 1.0865, "close_price": 1.0885, "sl": 1.084, "tp": 1.09, "profit": 30.0, "commission": 1.5, "swap": 0.5, "entry_time": "2023-04-15T10:30:00Z", "exit_time": "2023-04-15T14:30:00Z", "comment": "Manual close", "magic_number": 12345, "duration_minutes": 240}, {"trade_id": 12346, "account": 1, "refId": "IG-D1", "symbol": "GBPUSD", "cmd": 1, "volume": 1.0, "open_price": 1.245, "close_price": 1.243, "sl": 1.247, "tp": 1.24, "profit": 20.0, "commission": 1.0, "swap": 0.0, "entry_time": "2023-04-15T11:15:00Z", "exit_time": "2023-04-15T13:45:00Z", "comment": "TP hit", "magic_number": 12345, "duration_minutes": 150}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/trade_history_stop_level": {"get": {"summary": "Stop-Loss-Levels aus der Handelshistorie abrufen", "description": "Ruft die Stop-Loss-Levels aus der Handelshistorie für ein bestimmtes Konto ab, optional gefiltert nach Symbol und Zeitraum.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält nur relevante Informationen für Stop-Loss-Analyse\n- Filterbar nach Symbol und Zeitraum in Tagen\n- Standardmäßig werden die letzten 7 Tage abgerufen (oder 3 Tage bei Filterung nach Symbol)\n\nAnwendungsfälle:\n- Analyse der Stop-Loss-Platzierung\n- Optimierung von Stop-Loss-Strategien\n- Identifizierung von Mustern bei Stop-Loss-Auslösungen\n- Berechnung von durchschnittlichen Stop-Loss-Distanzen", "tags": ["Trade History"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}, {"in": "query", "name": "symbol", "required": false, "schema": {"type": "string"}, "description": "Trading-Symbol, nach dem gefiltert werden soll", "example": "EURUSD"}, {"in": "query", "name": "days", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 365}, "description": "Anzahl der Tage in der Vergangenheit, für die Stop-Loss-Levels abgerufen werden sollen. Standardwert: 7 Tage (ohne Symbol-Filter) oder 3 Tage (mit Symbol-Filter)", "example": 7}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Stop-Loss-Levels", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeHistoryStopLevel"}}, "example": [{"trade_id": 12345, "symbol": "EURUSD", "cmd": 0, "open_price": 1.0865, "sl": 1.084, "entry_time": "2023-04-15T10:30:00Z"}, {"trade_id": 12346, "symbol": "GBPUSD", "cmd": 1, "open_price": 1.245, "sl": 1.247, "entry_time": "2023-04-15T11:15:00Z"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/trade_history_equity": {"get": {"summary": "Equity-<PERSON>rve a<PERSON>", "description": "Ruft die historische Equity-Kurve für ein bestimmtes Konto ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Equity-Informationen im Zeitverlauf\n- Berechnet Drawdown und High-Watermark\n- Berücksichtigt den aktuellen Kontostand für die Berechnung\n\nAnwendungsfälle:\n- Visualisierung der Equity-Entwicklung\n- Ana<PERSON><PERSON> von Drawdown-Perioden\n- Berechnung von Performance-Metriken wie Sharpe Ratio\n- Überwachung der Margin-Nutzung im Zeitverlauf", "tags": ["Trade History"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Referenz-ID des Kontos", "example": "IG-D1"}, {"in": "query", "name": "account", "required": false, "schema": {"type": "string"}, "description": "Optionale Sub-Konto-ID", "example": "12345"}, {"in": "query", "name": "currentbalance", "required": true, "schema": {"type": "number", "format": "float", "minimum": 0}, "description": "Aktueller Kontostand für die Berechnung", "example": 10000.0}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Equity-Kurve", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeHistoryEquity"}}, "example": [{"exit_time": "2023-04-15T14:30:00Z", "equity": 10250.5, "day_profit": 150.25, "balance": 10200.0, "floating_pl": 50.5, "margin_used": 500.0, "free_margin": 9750.5, "margin_level": 2050.1, "drawdown": 150.0, "high_watermark": 10400.5}, {"exit_time": "2023-04-15T15:00:00Z", "equity": 10280.75, "day_profit": 180.5, "balance": 10200.0, "floating_pl": 80.75, "margin_used": 500.0, "free_margin": 9780.75, "margin_level": 2056.15, "drawdown": 119.75, "high_watermark": 10400.5}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}