require('dotenv').config();
const ig = require('../../configs/config_ig');
const moment = require('moment');
const controller_db_basics = require('../database/symbol_controller');
const IGTradeExecutor = require('../../services/ig_trade_executor');
const { LOG_LEVELS, log, logPerformance } = require('../../services/logging_service');
const { withCacheWrapper, CACHE_PREFIX } = require('../../services/cache_service');

async function getIGTradesIndependent(refID) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getIGTradesIndependent', 'Starting IG trades retrieval', {
            refID
        });

        let tradeExecutor = new IGTradeExecutor(ig.igClientPools[refID], ig.logger);
        const positions = await tradeExecutor.getOpenPositions();

        // Add PipValue to each trade
        const result = positions.map(trade => ({
            ...trade,
            pipValue: trade.symbol_details?.tickValue || -1
        }));

        log(LOG_LEVELS.INFO, 'getIGTradesIndependent', 'Successfully retrieved IG trades', {
            refID,
            resultCount: result?.length || 0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getIGTradesIndependent', 'Failed to fetch IG trades', {
            error: err.message,
            stack: err.stack,
            refID
        });
        throw err;
    } finally {
        logPerformance('getIGTradesIndependent', startTime);
    }
}

exports.getIGTrades = async (req, res) => {
    try {
        const result = await withCacheWrapper(
            'TRADE',
            'getIGTrades',
            () => getIGTradesIndependent(req.query.refID),
            [req.query.refID]
        );
        res.send(result);
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getIGTrades', 'Error retrieving trades', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).send('Server error');
    }
};


async function getIGTradesHistoryIndependent(refID, start, end, minusDays) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getIGTradesHistoryIndependent', 'Starting IG trades history retrieval', {
            refID,
            start,
            end,
            minusDays
        });

        if (isNaN(end)) {
            end = new Date().getTime();
        }
        if (isNaN(start)) {
            start = new Date().getTime() - (24 * 60 * 60 * 1000 * minusDays);
        }

        const fromDate = moment(start).format('YYYY-MM-DD');
        const toDate = moment(end).format('YYYY-MM-DD');
        
        let tradeExecutor = new IGTradeExecutor(ig.igClientPools[refID], ig.logger);
        const result = await tradeExecutor.getTransactionHistory(fromDate, toDate);
        
        log(LOG_LEVELS.INFO, 'getIGTradesHistoryIndependent', 'Successfully retrieved IG trades history', {
            refID,
            fromDate,
            toDate,
            resultCount: result?.length || 0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getIGTradesHistoryIndependent', 'Failed to fetch IG trades history', {
            error: err.message,
            stack: err.stack,
            refID,
            start,
            end,
            minusDays
        });
        throw err;
    } finally {
        logPerformance('getIGTradesHistoryIndependent', startTime);
    }
}

exports.getIGTradesHistory = async (req, res) => {
    try {
        let start = parseInt(req.query.start_number);
        let end = parseInt(req.query.end_number);
        const minusDays = req.query.minus_days || 1;
        const refID = req.query.refID || 'IG-P1';
        
        const result = await withCacheWrapper(
            'TRADE',
            'getIGTradesHistory',
            () => getIGTradesHistoryIndependent(refID, start, end, minusDays),
            [refID, start, end, minusDays]
        );
        
        res.send(result);
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getIGTradesHistory', 'Error retrieving trade history', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).send('Server error');
    }
};


async function getIGAccountIndependent(refID) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getIGAccountIndependent', 'Starting IG account retrieval', {
            refID
        });

        const igClient = ig.igClientPools[refID];
        const data = await igClient.makeRequest('GET', 'accounts', {}, 1);
        const result = data.code === 200 ? data.response : null;

        log(LOG_LEVELS.INFO, 'getIGAccountIndependent', 'Successfully retrieved IG account', {
            refID,
            success: !!result
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getIGAccountIndependent', 'Failed to fetch IG account', {
            error: err.message,
            stack: err.stack,
            refID
        });
        throw err;
    } finally {
        logPerformance('getIGAccountIndependent', startTime);
    }
}

exports.getIGAccount = async (req, res) => {
    try {
        const refID = req.query.refID || 'IG-P1';
        const result = await withCacheWrapper(
            'ACCOUNT',
            'getIGAccount',
            () => getIGAccountIndependent(refID),
            [refID]
        );
        res.send(result);
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getIGAccount', 'Error retrieving account information', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).send('Server error');
    }
};


async function getIGMarginTradeIndependent(refID, symbol, volume) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getIGMarginTradeIndependent', 'Starting IG margin trade calculation', {
            refID,
            symbol,
            volume
        });

        const igClient = ig.igClientPools[refID];
        const data = await igClient.makeRequest('GET', `markets/${symbol}`, {}, 2);

        if (data.code === 200 && data.response.instrument) {
            const marginFactor = data.response.instrument.marginFactor;
            const contractSize = parseFloat(data.response.instrument.contractSize);
            const currentPrice = data.response.snapshot.bid;
            // Calculate margin requirement: (contractSize * volume * price * marginFactor%)
            const marginRequirement = (contractSize * volume * currentPrice * marginFactor) / 100;
            const onePipMeans = data.response.instrument.onePipMeans ? parseFloat(data.response.instrument.onePipMeans.split(' ')[0]) : 0;
            
            const result = {
                margin: marginRequirement,
                marginFactor: marginFactor,
                onePipMeans: onePipMeans,
                symbol: symbol,
                currency: data.response.instrument.currencies && data.response.instrument.currencies.length > 0
                    ? data.response.instrument.currencies[0].code
                    : 'Unknown',
                //raw: data.response
            };

            log(LOG_LEVELS.INFO, 'getIGMarginTradeIndependent', 'Successfully calculated margin', {
                refID,
                symbol,
                volume,
                margin: marginRequirement
            });

            return result;
        }
        
        log(LOG_LEVELS.WARN, 'getIGMarginTradeIndependent', 'Failed to calculate margin - invalid response', {
            refID,
            symbol,
            volume,
            responseCode: data.code
        });
        
        return null;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getIGMarginTradeIndependent', 'Failed to calculate margin', {
            error: err.message,
            stack: err.stack,
            refID,
            symbol,
            volume
        });
        throw err;
    } finally {
        logPerformance('getIGMarginTradeIndependent', startTime);
    }
}

exports.getIGMarginTrade = async (req, res) => {
    try {
        const refID = req.query.refID || 'IG-P1';
        const symbol = req.query.symbol;
        const volume = parseFloat(req.query.volume) || 1;
        
        const result = await withCacheWrapper(
            'TRADE',
            'getIGMarginTrade',
            () => getIGMarginTradeIndependent(refID, symbol, volume),
            [refID, symbol, volume]
        );
        
        res.send(result);
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getIGMarginTrade', 'Error calculating margin', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).send('Server error');
    }
};
