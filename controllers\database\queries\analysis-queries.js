/**
 * SQL Queries für detaillierte Analysen
 */
const AnalysisQueries = {
    weekDayStats: {
        sql: 'WITH weekdays AS (\
                  SELECT 1 AS dayOfWeek\
                  UNION ALL SELECT 2\
                  UNION ALL SELECT 3\
                  UNION ALL SELECT 4\
                  UNION ALL SELECT 5\
                  UNION ALL SELECT 6\
                  UNION ALL SELECT 7\
                )\
                SELECT a.refID, WEEKOFYEAR(x.exit_time) as week, w.dayOfWeek, IFNULL(COUNT(x.profit),0) as cnt_trades, IFNULL(round(SUM(x.profit),0),0) as profit \
                      FROM weekdays as w\
                LEFT JOIN `trades_history` x ON date(x.exit_time)>DATE_SUB(CURDATE(),INTERVAL ? week)  AND DAYOFWEEK(x.exit_time)=w.dayofWeek\
                LEFT JOIN `accounts` a ON a.refID=? AND x.account=a.account_id \
                    WHERE a.refID=?    \
                     GROUP BY a.refID, WEEKOFYEAR(x.exit_time), w.dayOfWeek',
        bigIntAsNumber: true,
        timezone: 'de_de'
    },

    dayHourStats: {
        sql: 'WITH hours AS (\
                  SELECT 0 AS hour\
                  UNION ALL SELECT 1\
                  UNION ALL SELECT 2\
                  UNION ALL SELECT 3\
                  UNION ALL SELECT 4\
                  UNION ALL SELECT 5\
                  UNION ALL SELECT 6\
                  UNION ALL SELECT 7\
                  UNION ALL SELECT 8\
                  UNION ALL SELECT 9\
                  UNION ALL SELECT 10\
                  UNION ALL SELECT 11\
                  UNION ALL SELECT 12\
                  UNION ALL SELECT 13\
                  UNION ALL SELECT 14\
                  UNION ALL SELECT 15\
                  UNION ALL SELECT 16\
                  UNION ALL SELECT 17\
                  UNION ALL SELECT 18\
                  UNION ALL SELECT 19\
                  UNION ALL SELECT 20\
                  UNION ALL SELECT 21\
                  UNION ALL SELECT 22\
                  UNION ALL SELECT 23\
                )\
                SELECT a.refID, WEEKOFYEAR(x.entry_time) as week, hours.hour, IFNULL(COUNT(x.profit),0) as cnt_trades, IFNULL(round(SUM(x.profit),0),0) as profit \
                FROM hours\
                LEFT JOIN `trades_history` x ON date(x.entry_time)>DATE_SUB(CURDATE(),INTERVAL ? week) AND HOUR(x.entry_time)=hours.hour\
                LEFT JOIN `accounts` a ON a.refID=? AND x.account=a.account_id \
                 WHERE a.refID=?    \
                GROUP BY a.refID, WEEKOFYEAR(x.entry_time), hours.hour;',
        bigIntAsNumber: true,
        timezone: 'de_de'
    },

    symbolStrategyStats: {
        sql: 'SELECT s.symbol, \
                     s.strategy, \
                     IFNULL(acc.refId,?) as refId, \
                     IFNULL(t.activate_ig_d1,0) AS activate_ig_d1, \
                     IFNULL(t.activate_ig_p1,0) AS activate_ig_p1, \
                     IFNULL(COUNT(xtb.profit),0) AS cnt_trades, \
                     IFNULL(ROUND(SUM(xtb.profit),0),0) AS profit\
              FROM `strategy_symbols` AS s\
              LEFT JOIN `strategy_toggle` AS t ON t.strategy = s.strategy AND t.symbol = s.symbol\
              LEFT JOIN `accounts` AS acc ON refID=? \
              LEFT JOIN `trades_history` AS xtb \
                     ON xtb.strategy=s.strategy AND xtb.symbol=s.symbol \
                        AND DATE(xtb.exit_time)> DATE_SUB(CURDATE(), INTERVAL ? month) \
             GROUP BY s.symbol, s.strategy, acc.refId\
             HAVING cnt_trades>0',
        bigIntAsNumber: true,
        timezone: 'de_de'
    },

    strategySymbolStats: {
        sql: 'SELECT s.strategy, \
                   s.symbol, \
                   IFNULL(acc.refId,?) as refId, \
                   IFNULL(t.activate_ig_d1,0) AS activate_ig_d1, \
                   IFNULL(COUNT(xtb.profit),0) AS cnt_trades, \
                   IFNULL(ROUND(SUM(xtb.profit),0),0) AS profit\
              FROM `strategy_symbols` AS s\
              LEFT JOIN `strategy_toggle` AS t ON t.strategy = s.strategy AND t.symbol = s.symbol\
              LEFT JOIN `accounts` AS acc ON refID=? \
              LEFT JOIN `trades_history` AS xtb \
                     ON xtb.strategy=s.strategy \
                        AND xtb.symbol=s.symbol \
                        AND DATE(xtb.exit_time)> DATE_SUB(CURDATE(), INTERVAL ? MONTH) \
                        AND acc.account_id=xtb.account \
             GROUP BY s.strategy, s.symbol, acc.refId \
               HAVING cnt_trades>0',
        bigIntAsNumber: true,
        timezone: 'de_de'
    }
};

module.exports = AnalysisQueries;
