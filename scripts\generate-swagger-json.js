/**
 * <PERSON><PERSON><PERSON> to generate the combined Swagger documentation
 * 
 * This script loads the Swagger configuration from docs/swagger.js
 * and writes the combined JSON to swagger-doc.json in the project root.
 */

const fs = require('fs');
const path = require('path');
const swaggerSpec = require('../docs/swagger');

// Format the JSON with indentation for readability
const formattedJson = JSON.stringify(swaggerSpec, null, 2);

// Write to the output file
const outputPath = path.join(__dirname, '..', 'swagger-doc.json');
fs.writeFileSync(outputPath, formattedJson);

console.log(`Swagger documentation generated at ${outputPath}`);