{"components": {"schemas": {"MarketInfo": {"type": "object", "properties": {"instrument": {"type": "object", "properties": {"name": {"type": "string", "description": "Name des Instruments", "example": "EUR/USD"}, "epic": {"type": "string", "description": "Epic-Code des Instruments", "example": "CS.D.EURUSD.TODAY.IP"}, "type": {"type": "string", "description": "Typ des Instruments", "example": "CURRENCIES"}, "marginFactor": {"type": "number", "format": "float", "description": "Margin-Faktor in Prozent", "example": 3.33}, "onePipMeans": {"type": "string", "description": "Bedeutung eines Pips", "example": "0.0001 price movement"}}}, "snapshot": {"type": "object", "properties": {"marketStatus": {"type": "string", "description": "Status des Marktes", "example": "TRADEABLE"}, "bid": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON><PERSON> (Bid)", "example": 1.08543}, "offer": {"type": "number", "format": "float", "description": "<PERSON>rief<PERSON><PERSON> (Ask)", "example": 1.08553}, "high": {"type": "number", "format": "float", "description": "<PERSON>esh<PERSON>", "example": 1.08743}, "low": {"type": "number", "format": "float", "description": "Tagestief", "example": 1.08343}}}}}, "PriceResponse": {"type": "object", "properties": {"bid": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON><PERSON> (Bid)", "example": 1.08543}, "ask": {"type": "number", "format": "float", "description": "<PERSON>rief<PERSON><PERSON> (Ask)", "example": 1.08553}, "spread": {"type": "number", "format": "float", "description": "Spread (Ask - Bid)", "example": 0.0001}, "timestamp": {"type": "string", "format": "date-time", "description": "Zeitstempel der Preisabfrage"}}}, "CandleData": {"type": "object", "properties": {"snapshotTime": {"type": "string", "format": "date-time", "description": "Zeitstempel des Kerzencharts"}, "openPrice": {"type": "number", "format": "float", "description": "Eröffnungskurs", "example": 1.08543}, "closePrice": {"type": "number", "format": "float", "description": "Schlusskurs", "example": 1.08563}, "highPrice": {"type": "number", "format": "float", "description": "Höchstkurs", "example": 1.08583}, "lowPrice": {"type": "number", "format": "float", "description": "Tiefstkurs", "example": 1.08523}, "lastTradedVolume": {"type": "number", "format": "integer", "description": "Handelsvolumen", "example": 12345}}}, "HistoricalPriceResponse": {"type": "array", "items": {"$ref": "#/components/schemas/CandleData"}}, "LastCandleResponse": {"type": "object", "$ref": "#/components/schemas/CandleData"}, "MarketDataError": {"type": "object", "properties": {"error": {"type": "string", "description": "Fehlermeldung", "example": "Failed to retrieve market information"}}}}, "responses": {"MarketDataError": {"description": "Fehler bei der Abfrage von <PERSON>daten", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarketDataError"}}}}}}}