/**
 * SQL-Queries für Mirror-Trading Funktionen
 */

const buildMirrorTradingSettingsQuery = () => {
    return {
        sql: 'select * from `mirror_trading_settings` where sourceAccountRefID = ? and targetAccountRefID = ?',
        bigIntAsNumber: true,
        timezone: 'de_de'
    };
};

const buildMirrorTradingLogsQuery = () => {
    return {
        sql: 'select * from `mirror_trading_logs` where sourceRefID = ? and targetRefID = ? order by timestamp desc limit ?',
        bigIntAsNumber: true,
        timezone: 'de_de'
    };
};

module.exports = {
    buildMirrorTradingSettingsQuery,
    buildMirrorTradingLogsQuery
};
