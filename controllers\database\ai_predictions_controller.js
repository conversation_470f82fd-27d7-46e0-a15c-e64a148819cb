const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON>ache<PERSON>rapper, CACHE_PREFIX } = require('../../services/cache_service');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');
const { buildSymbolAIPredictionsQuery } = require('./queries/ai_predictions_queries');

async function getSymbolAIPredictionsIndependent(symbol = null) {
    try {
        // Validate symbol if provided
        if (symbol !== null) {
            symbol = validateStringParam(symbol, {
                minLength: 1,
                maxLength: 50,
                paramName: 'symbol'
            });
        }

        const query = buildSymbolAIPredictionsQuery(symbol);
        const result = await executeQuery(query, symbol ? [symbol] : []);
        
        if (!result) {
            throw new DatabaseError('Failed to fetch AI predictions', { symbol });
        }

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getSymbolAIPredictionsIndependent', 'Failed to fetch AI predictions', {
            error: err.message,
            stack: err.stack,
            symbol: symbol
        });
        throw err;
    }
}

async function getSymbolAIPredictions(req, res) {
    try {
        const result = await withCacheWrapper(
            'GENERAL',
            'getSymbolAIPredictions',
            () => getSymbolAIPredictionsIndependent(req.query.symbol),
            [req.query.symbol]
        );

        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getSymbolAIPredictions');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getSymbolAIPredictions,
    getSymbolAIPredictionsIndependent
};
