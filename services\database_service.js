const db = require('../configs/config_db');
const { DatabaseError } = require('../controllers/database/errors/database_errors');
const { log, LOG_LEVELS, logPerformance } = require('./logging_service');


/**
 * Führt eine Datenbankabfrage aus und liefert das Ergebnis zurück
 *
 * Diese Funktion ist der zentrale Punkt für alle Datenbankzugriffe.
 * Sie übernimmt die Validierung der Parameter, Ausführung der Query,
 * Fehlerbehandlung und Performance-Logging. 
 *
 * Wichtige Features:
 * - Automatische Parameter-Validierung
 * - Performance-Logging für Optimierung
 * - Einheitliche Fehlerbehandlung
 * - Unterstützung für verschiedene Query-Formate
 *
 * @param {Object|string} query - Das Query-Objekt oder SQL-String
 * @param {string} query.sql - SQL-Statement wenn query ein Objekt ist
 * @param {Array} [query.values] - Query-Parameter wenn query ein Objekt ist
 * @param {boolean} [query.bigIntAsNumber] - BigInt als Number zurückgeben
 * @param {string} [query.timezone] - Zu verwendende Zeitzone
 * @param {Array} [params] - Query-Parameter wenn query ein String ist
 * @param {Object} [options] - Zusätzliche Ausführungsoptionen
 * @param {boolean} [options.bigIntAsNumber=true] - BigInt als Number zurückgeben
 * @param {string} [options.timezone='de_de'] - Zu verwendende Zeitzone
 * @returns {Promise<Array>} Query-Ergebnis als Array von Objekten
 * @throws {ValidationError} Bei ungültigen Query-Parametern oder SQL
 * @throws {DatabaseError} Bei Fehlern während der Datenbankabfrage
 *
 * @example
 * // Als String mit Parametern
 * const users = await executeQuery(
 *   'SELECT * FROM users WHERE id = ?',
 *   [123]
 * );
 *
 * // Als Query-Objekt mit Optionen
 * const orders = await executeQuery({
 *   sql: 'SELECT * FROM orders WHERE user_id = ? AND status = ?',
 *   values: [123, 'pending'],
 *   bigIntAsNumber: true,
 *   timezone: 'de_de'
 * });
 *
 * // Mit zusätzlichen Optionen und Fehlerbehandlung
 * try {
 *   const results = await executeQuery(
 *     'SELECT * FROM data',
 *     [],
 *     {
 *       bigIntAsNumber: true,
 *       timezone: 'de_de'
 *     }
 *   );
 * } catch (err) {
 *   if (err instanceof ValidationError) {
 *     console.error('Ungültige Parameter:', err.message);
 *   } else if (err instanceof DatabaseError) {
 *     console.error('Datenbankfehler:', err.message);
 *   }
 * }
 */
async function executeQuery(query, params = [], options = {}) {
    const startTime = process.hrtime.bigint();
    try {
        // Query-Objekt normalisieren
        const sqlQuery = typeof query === 'string' ? query : query.sql || query;
        const queryParams = Array.isArray(params) ? params : query.values || [];
        const queryOptions = {
            bigIntAsNumber: true,
            timezone: 'de_de',
            ...options,
            ...(typeof query === 'object' ? query : {})
        };

        // Parameter validieren
        if (!Array.isArray(queryParams)) {
            throw new ValidationError('Parameters must be an array');
        }

        if (!sqlQuery || typeof sqlQuery !== 'string') {
            throw new ValidationError('Invalid SQL query');
        }

        // Logging vor Ausführung
        // log(LOG_LEVELS.DEBUG, 'executeQuery', 'Executing query', {
        //     sql: sqlQuery,
        //     params: queryParams,
        //     options: queryOptions
        // });

        // Query ausführen
        const rows = await db.pool.query({
            sql: sqlQuery,
            values: queryParams,
            ...queryOptions
        });

        // Erfolgs-Logging
        // log(LOG_LEVELS.DEBUG, 'executeQuery', 'Query executed successfully', {
        //     rowCount: rows?.length || 0,
        //     executionTime: Number(process.hrtime.bigint() - startTime) / 1_000_000
        // });

        return rows;
    } catch (err) {
        // Fehler-Logging mit Details
        log(LOG_LEVELS.ERROR, 'executeQuery', 'Database query failed', {
            error: err.message,
            sql: typeof query === 'string' ? query : query.sql || query,
            params: params,
            stack: err.stack
        });

        // Fehler mit zusätzlichen Informationen werfen
        throw new DatabaseError('Database query failed', {
            originalError: err,
            query: typeof query === 'string' ? query : query.sql || query,
            params: params
        });
    } finally {
        logPerformance('executeQuery', startTime, {
            sql: typeof query === 'string' ? query : query.sql || query
        });
    }
}


module.exports = {
    executeQuery
};
