const htmlToText = require('html-to-text');


function removeTextFragments(text) {
    const fragmentsToRemove = [
        'https://de.xtb.com/den-deutschen-leitindex-gunstig-traden',
        '\\* Besonders niedrige Spreads\xa0DURCHGEHEND\xa0in der langen Handelszeit von 08 bis 22 Uhr!',
        '\\* Keine zusätzliche Ordergebühr/Orderkommission!',
        '\\* Mini CFDs, Trading ab 0,01 Kontrakten',
        '\\* Besonders präzise Kursstellung & Charting, basierend auf den Index-Futures',
        '\\* Handeln mit dem TESTSIEGER 2023 in Deutschland, Trading mit XTB \\[https://de.xtb.com/den-deutschen-leitindex-gunstig-traden\\]',
        'Quellen:\xa0xStation5\xa0von XTB',
        'HANDELN BEIM TESTSIEGER 2023:',
        '\\* Der\xa0Testsieger\xa0bei den CFD Brokern 2023 bei Brokerwahl.de ist XTB',
        '\\* über 66\\.000 Trader haben abgestimmt!',
        '\\* Bester Service, beste Spreads und beste Technik',
        '\\* Überzeugen Sie sich selbst und handeln Sie beim Besten CFD Broker Deutschlands laut Brokerwahl \\[https://de.xtb.com/de/bester-cfd-broker-2023\\]',
        'https://de.xtb.com/de/bester-cfd-broker-2023',
        'Offenlegung gemäß § 80 WpHG zwecks möglicher Interessenkonflikte',
        'Der Autor kann in den besprochenen Wertpapieren bzw\\. Basiswerten investiert sein\\.',
        'Die Autoren der Veröffentlichungen verfassen jene Informationen auf eigenes Risiko\\. Analysen und Einschätzungen werden nicht in Bezug auf spezifische Anlageziele und Bedürfnisse bestimmter Personen verfasst\\. Veröffentlichungen von XTB, die bestimmte Situationen an den Finanzmärkten kommentieren sowie allgemeine Aussagen von Mitarbeitern von XTB hinsichtlich der Finanzmärkte, stellen keine Beratung des Kunden durch XTB dar und können auch nicht als solche ausgelegt werden\\. XTB haftet nicht für Verluste, die direkt oder indirekt durch getroffene Handlungsentscheidungen in Bezug auf die Inhalte der Veröffentlichungen entstanden sind\\.',
        'Risikohinweis',
        '\\* Handeln mit dem TESTSIEGER 2023 in Deutschland, Trading mit XTB \\[\\]',
        'beim CFD-Handel mit diesem Anbieter. Sie sollten überlegen, ob Sie verstehen, wie CFDs funktionieren und ob Sie es sich leisten können, das hohe Risiko einzugehen, Ihr Geld zu verlieren. Anlageerfolge sowie Gewinne aus der Vergangenheit garantieren keine Erfolge in der Zukunft. Inhalte, Newsletter und Mitteilungen von XTB stellen keine Anlageberatung dar. Die Mitteilungen sind als Werbemitteilung zu verstehen.',
        'CFDs sind komplexe Instrumente und beinhalten wegen der Hebelwirkung ein hohes Risiko, schnell Geld zu verlieren\\. 80% der Kleinanlegerkonten verlieren Geld',
        'CFDs sind komplexe Instrumente und beinhalten wegen der Hebelwirkung ein hohes Risiko, schnell Geld zu verlieren. 79% der Kleinanlegerkonten verlieren Geld',
        'https://www.xtb.com/de/aktien',
        '\\* Über 66.000 Trader haben abgestimmt!',
        '\\* Besonders niedrige Spreads DURCHGEHEND in der langen Handelszeit von 08 bis 22 Uhr!',
        'Quelle: xStation5 von XTB'
    ];

    fragmentsToRemove.forEach(fragment => {
        const regex = new RegExp(fragment, 'g');
        text = text.replace(regex, '');
    });

    return text;
}

// eslint-disable-next-line no-unused-vars
async function optimizeTextBody(newsTitle, newsBody) {

    let l_bodytext = newsBody;

    // img replacements
    const regex1 = /<img[^>]*>/gi;
    l_bodytext = l_bodytext.replace(regex1, '');

    // html to plaintext
    let l_plainText = htmlToText.htmlToText(l_bodytext, {
        wordwrap: false,
        ignoreHref: true,
        ignoreImage: true,
    });

    l_plainText = l_plainText
        .replace(/\r/g, ' ')
        .replace(/\t/g, ' ')
        .replace(/\*\*\*/g, '')
        .replace(/ /g, ' ')
        .replace(/  +/g, ' ');

    const searchStrings = [
        'weltweite indizes handeln mit xtb',
        'offenlegung gemäß § 80 wphg zwecks möglicher interessenkonflikte',
        'risikohinweis',
    ];

    let l_minPos = l_bodytext.length;
    let l_bodytext_lower = l_bodytext.toLowerCase();
    searchStrings.forEach((searchStr) => {
        const l_pos = l_bodytext_lower.indexOf(searchStr);
        if (l_pos > 1 && l_pos < l_minPos) {
            l_minPos = l_pos;
            if (process.env.NODE_ENV !== 'production')
              console.log("[gpt_extractText][optimizeTextBody] Remove unnecessary text:", searchStr, "pos:", l_pos);
        }
    });

    l_plainText = removeTextFragments(l_plainText)
        .replace(/\ \n/g, '\n')
        .replace(/\n\n\n\n\n/g, '\n')
        .replace(/\n\n\n/g, '\n\n');

    l_plainText = l_plainText.substring(0, l_minPos);
    return '\n###\n'+ newsTitle+'\n' + l_plainText;
}

module.exports = {
    optimizeTextBody
}
