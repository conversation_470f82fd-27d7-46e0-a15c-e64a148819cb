
###
// @name Risk-State: Get States
GET {{API_BASE_URL}}/api/v1/db/riskmgmt/state
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(Array.isArray(response.body), "Response is not an array");
    });
%}

###
// @name get_risk_management_state_single
GET {{API_BASE_URL}}/api/v1/db/riskmgmt/state/US100
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
        if(response.body.length > 0) {
            client.assert(response.body[0].symbol === "IX.D.NASDAQ.IFE.IP", "Symbol is not IX.D.NASDAQ.IFE.IP");
        }
    });
%}

###
// @name Risk-State: Update: Risk_on_mode
PUT {{API_BASE_URL}}/api/v1/db/riskmgmt/state/US100/risk_on_mode?mode=Low-Risk
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Status is not success");
    });
%}

###
// @name Risk-State: Update: market_situation
PUT {{API_BASE_URL}}/api/v1/db/riskmgmt/state/US100/market_situation?situation=Long
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Status is not success");
    });
%}
