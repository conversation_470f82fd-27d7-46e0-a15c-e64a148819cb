
### Trade-History: IG-D1 nur für IX.D.NASDAQ.IFE.IP
GET {{API_BASE_URL}}/api/v1/db/trade_history?refID=IG-D1&symbol=IX.D.NASDAQ.IFE.IP&days=2
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

### Trade-History: IG-D1
GET {{API_BASE_URL}}/api/v1/db/trade_history?refID=IG-D1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name trades-history-stopps
GET {{API_BASE_URL}}/api/v1/db/trade_history_stops?refID=IG-D1&days=1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name trades-history-stopps
GET {{API_BASE_URL}}/api/v1/db/trade_history_stops?refID=IG-D1&days=3&symbol=IX.D.SPTRD.IFE.IP
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name trade-equity
GET {{API_BASE_URL}}/api/v1/db/trade_history_equity?currentbalance=1900&refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}