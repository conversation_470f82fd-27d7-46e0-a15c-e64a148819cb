/**
 * SQL-Queries für Delta Report Funktionen
 */

const buildDeltaReportQuery = () => {
    return {
        sql: 'SELECT ROW_NUMBER() OVER (ORDER BY c1.german_timestamp) AS row_num, \
                   c.title,\
                   c.country,\
                   c.previous,\
                   c.forecast,\
                   c.current,\
                   round(c1.open,0) AS open,\
                   round((c.current-c.forecast)*100/c.current,1) as `delta`,\
                   c1.german_timestamp, \
                   round(abs(c1.low)+c1.high,0) as c1_high_low_range, \
                   round((abs(c1.low)+c1.high)*100/c1.open,2) as c1_high_low_range_percent,\
                   round(c1.close,0) as c1_close_change, \
                   round(c1.close*100/c1.open,2) as c1_close_change_percent, \
                   c1.vol as c1_volume, \
                   round(c1.open-fd.open,0) as "c1_delta_open", \
                   round((c1.open-fd.open)*100/fd.open,2) as "c1_delta_open_percent", \
                   s.tickSize \
            from calendar c \
                     LEFT JOIN chartdata c1 on c1.german_timestamp between DATE_SUB(c.timestamp, INTERVAL 0 minute) and DATE_ADD(c.timestamp, INTERVAL 480 minute) \
                     LEFT JOIN chartdata fd on fd.german_timestamp between DATE_SUB(c.timestamp, INTERVAL 5 minute) and DATE_ADD(c.timestamp, INTERVAL 5 minute) \
                     LEFT JOIN symbols s on s.symbol = c1.symbol \
            WHERE impact >= 1 \
              AND c.ID=? \
              AND c1.symbol=? \
              and fd.symbol=c1.symbol\
            order by c1.german_timestamp',
        bigIntAsNumber: true,
        timezone: 'de_de'
    };
};

module.exports = {
    buildDeltaReportQuery
};
