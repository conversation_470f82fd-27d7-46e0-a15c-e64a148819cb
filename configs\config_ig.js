require('dotenv').config();
const { createLogger, format, transports, Logger } = require('winston');
const IGApiClient = require('../services/ig_api_client');
const IGMarketDataProvider = require('../services/ig_market_data_provider');
const IGSymbolFetcher = require('../services/ig_symbol_fetcher');

// Configure Winston logger
const logger = createLogger({
    format: format.combine(
        format.timestamp(),
        format.json()
    ),
    transports: [
        new transports.Console({
            level: 'debug'
        }),
        new transports.File({
            filename: 'logs/ig-api-error.log',
            level: 'error'
        }),
        new transports.File({
            filename: 'logs/ig-api-combined.log'
        })
    ]
});

// Create API client instances
const igClientPool_P1 = new IGApiClient({
    identifier: process.env.IG_LIVE_IDENTIFIER,
    password: process.env.IG_LIVE_PASSWORD,
    apiKey: process.env.IG_LIVE_API_KEY,
    alias: 'IG-P1',
    isDemo: false,
    sslVerify: false
}, logger);

const igClientPool_D1 = new IGApiClient({
    identifier: process.env.IG_DEMO_IDENTIFIER,
    password: process.env.IG_DEMO_PASSWORD,
    apiKey: process.env.IG_DEMO_API_KEY,
    isDemo: true,
    alias: 'IG-D1',
    sslVerify: false
}, logger);

const igClientPools = {
    'IG-P1': igClientPool_P1,
    'IG-D1': igClientPool_D1,
};

module.exports = Object.freeze({
    igClientPools,
    logger
});
