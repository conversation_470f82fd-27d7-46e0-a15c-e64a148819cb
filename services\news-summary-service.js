const { requestLLM } = require('./llm-service');
const extractTextHelper = require('../controllers/gpt/controller_gpt_extractText');
const databaseHelper = require('../controllers/gpt/controller_gpt_databaseHelper');
const { LLM } = require('../configs/constants');

const ALLOWED_SYMBOLS = ['US100', 'US500', 'US2000', 'US30', 'GOLD', 'SILVER', 'BITCOIN', 'DE40', 'AUS200', 'EURUSD', 'OIL.WTI'];

/**
 * Get or create a database entry for a news item
 * @param {Object} newsItem - The news item object with a 'key' property
 * @returns {Object} Database entry for the news item
 */
async function getOrCreateDatabaseEntry(newsItem) {
    let result = await databaseHelper.database_read(newsItem.key);
    if (result.uuid === null) {
        result = await databaseHelper.database_init(newsItem);
        if (result.uuid === null) {
            result.uuid = newsItem.key;
        }
    }
    return result;
}

/**
 * Check if the GPT summary should be updated
 * @param {Object} result - The database result object
 * @param {Object} newsItem - The news item object
 * @param {boolean} override - Flag to force update
 * @returns {boolean} True if update is needed, false otherwise
 */
function shouldUpdateSummary(result, newsItem, override) {
    const mismatchBodyLen = result?.bodylen !== newsItem?.body?.length;
    result.gpt_refresh_reason = "";

    if (mismatchBodyLen) {
        result.gpt_refresh_reason = `Mismatch in body length: old=${result.bodylen}, new=${newsItem.body.length}, delta=${newsItem.body.length - result.bodylen}`;
        if (process.env.NODE_ENV !== 'production')
          console.log("[news-summary-service][shouldUpdateSummary] Reason:", result.gpt_refresh_reason);
    }

    if (result.gpt_summarize == null) {
        const msg = `Missing summary for: ${newsItem.title}`;
        result.gpt_refresh_reason += (result.gpt_refresh_reason ? " | " : "") + msg;
        if (process.env.NODE_ENV !== 'production')
           console.log("[news-summary-service][shouldUpdateSummary] Message:", msg);
    }

    return result.gpt_summarize == null || override;
}

/**
 * Update the GPT summary for a news item
 * @param {Object} result - The database result object
 * @param {Object} newsItem - The news item object
 * @param {number} promptVersion - The prompt version
 */
async function getUpdatedSummaryObject(result, newsItem, promptVersion) {
    result.optimized_textbody = await extractTextHelper.optimizeTextBody(newsItem.title, newsItem.body);
    result.gpt_summarize = result.optimized_textbody;
    result.gpt_prompt = "-";
    result.gpt_summarize_date = new Date();
    result.gpt_prompt_version = -1;
    result.logo = newsItem.logo;
    result.tldr = newsItem.tldr || "";
    result.topic = newsItem.topic || "";
}

/**
 * Summarize a news item and store it in the database
 * @param {Object} newsItem - The news item object
 * @param {boolean} override - Flag to force update (default: false)
 * @param {number} promptVersion - The prompt version (default: -1)
 * @returns {Object} The updated database result
 */
async function summarizeAndStoreNewsItem(newsItem, override = false, promptVersion = -1) {
    if (process.env.NODE_ENV !== 'production')
      console.log("[news-summary-service][summarizeAndStoreNewsItem] Summarizing news item:", newsItem);
    const result = await getOrCreateDatabaseEntry(newsItem);
    if (process.env.NODE_ENV !== 'production')
      console.log("[news-summary-service][summarizeAndStoreNewsItem] Database entry UUID:", result.uuid);

    if (shouldUpdateSummary(result, newsItem, override)) {
        await getUpdatedSummaryObject(result, newsItem, promptVersion);
        await databaseHelper.database_update(result);
    }
    return result;
}

/**
 * Update optimized text body with merged symbol teaser (structured data)
 * @param {number} newsLimit - The limit of news items to process
 * @param {number} charLimit - The character limit for the output
 * @returns {string} The JSON string of merged summaries
 */
async function getMergedSymbolTeaserStructured(newsLimit, charLimit) {
    const inputJsons = await databaseHelper.getNewsSummariesFromDay(newsLimit);
    if (process.env.NODE_ENV !== 'production')
      console.log("[news-summary-service][getMergedSymbolTeaserStructured] Updating optimized text body (structured):", 
        { inputJsonsLength: inputJsons.length, charLimit, newsLimit });

    const outputJson = {
        summaryTitle: "Gesamtübersicht",
        summaryReports: [],
        summaries: [],
    };

    let totalLength = 0;

    for (const input of inputJsons) {
        if (input.gpt_summarize && typeof input.gpt_summarize === 'string') {
            let summary;
            try {
                const parsedSummary = JSON.parse(input.gpt_summarize);
                summary = parsedSummary.summary || input.gpt_summarize;
            } catch (error) {
                summary = input.gpt_summarize;
            }

            outputJson.summaries.push(summary);
            totalLength += summary.length;

            const symbolMatch = summary.match(/Symbol: ([A-Z0-9]+)/);
            const teaserMatch = summary.match(/Teaser: (.+)/);

            if (symbolMatch && teaserMatch) {
                const symbol = symbolMatch[1];
                const teaser = teaserMatch[1];

                if (ALLOWED_SYMBOLS.includes(symbol)) {
                    let existingReport = outputJson.summaryReports.find(report => report.symbol === symbol);
                    if (!existingReport) {
                        existingReport = { symbol, reports: [] };
                        outputJson.summaryReports.push(existingReport);
                    }

                    const newReport = { teasers: [teaser] };
                    const reportLength = JSON.stringify(newReport, null, 2).length;
                    totalLength += reportLength;

                    if (totalLength < charLimit) {
                        existingReport.reports.push(newReport);
                    } else {
                        if (process.env.NODE_ENV !== 'production')
                          console.log(`Reached char limit. Ignoring further messages. Total length: ${totalLength}`);
                        break;
                    }
                }
            }
        } else {
            console.warn(`Invalid gpt_summarize for input: ${input.title}`);
        }

        if (totalLength >= charLimit) break;
    }

    return JSON.stringify(outputJson, null, 2);
}

module.exports = {
    summarizeAndStoreNewsItem,
    getMergedSymbolTeaserStructured,
    shouldUpdateSummary,
    getUpdatedSummaryObject,
    getOrCreateDatabaseEntry
};
