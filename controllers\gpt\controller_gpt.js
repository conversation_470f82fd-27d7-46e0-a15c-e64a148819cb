const { requestLLM } = require('../../services/llm-service');
const databaseHelper = require('./controller_gpt_databaseHelper');
const newsSummaryService = require('../../services/news-summary-service');
const { LLM } = require('../../configs/constants');

exports.aiAssistant = async (req, res) => {
    const assistantId = req.query.assistantID;
    const userMessage = { role: 'user', content: req.body };
    const llmModel = req.query.llmModel || LLM.DEFAULT_MODEL;
    if (process.env.NODE_ENV !== 'production')
      console.log("[aiAssistant] :", assistantId, "userMessage:", JSON.stringify(userMessage), ", llmModel:", llmModel);
    try {
        const output = await requestLLM(userMessage, llmModel, assistantId);
        res.send(output);
    } catch (error) {
        console.error("Error in aiAssistant:", error);
        res.status(500).send("An error occurred while processing your request.");
    }
};

async function aiLangfusePromptRequestIndependent(promptID, promptVariables, gptModel = LLM.DEFAULT_MODEL, promptVersion = 1) {
    console.log("[gpt][aiLangfusePromptRequestIndependent] Processing request:", {
        promptID,
        //promptVariables: promptVariables,
        gptModel
    });

    try {
        const output = await requestLLM([], gptModel, null, promptID, promptVersion, promptVariables);
        if (process.env.NODE_ENV !== 'production')
          console.log("[gpt][aiLangfusePromptRequestIndependent] Output from requestGPTWithLangfuse:", output.substring(0, 250) + '...');
        return output;
    } catch (error) {
        console.error("Error in aiLangfusePromptRequestIndependent:", error);
        throw error;
    }
}

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/gpt-endpoints.json
 * 
 * @tags [AI Integration]
 */
exports.aiLangfusePromptRequest = async (req, res) => {
    if (process.env.NODE_ENV !== 'production')
       console.log("[gpt][aiLangfusePromptRequest] Received request");
    const promptID = req.query.promptID;
    const promptVersion = req.query.promptVersion || 1;
    const promptVariables = req.body ? typeof req.body === 'object'? JSON.stringify(req.body): req.body : "{}";
    const gptModel = req.query.gptModel || req.query.llmModel || LLM.DEFAULT_MODEL;

    try {
        const output = await aiLangfusePromptRequestIndependent(promptID, promptVariables, gptModel, promptVersion);
        res.send(output);
    } catch (error) {
        console.error("Error in aiLangfusePromptRequest:", error);
        res.status(500).send("An error occurred while processing your request.");
    }
};

exports.aiLangfusePromptRequestIndependent = aiLangfusePromptRequestIndependent;

exports.getLatestDaySummariesForAI = async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 5;
        const summaries = await databaseHelper.getLatestDaySummariesForAI(limit);
        
        const processedSummaries = summaries.split('</gptSummary_').filter(s => s.trim()).map(summary => {
            const dateMatch = summary.match(/<gptSummary_(\d{4}-\d{2}-\d{2})>/);
            const date = dateMatch ? dateMatch[1].split('-').reverse().join('.') : null;
            const content = summary + '</gptSummary_' + (dateMatch ? dateMatch[1] : '');
            return { date, content };
        });

        res.json({ summaries: processedSummaries });
    } catch (error) {
        console.error("Error in getLatestDaySummariesForAI:", error);
        res.status(500).json({ error: "An error occurred while retrieving the latest GPT day summaries." });
    }
};




exports.summarizeAndStoreNewsItem = newsSummaryService.summarizeAndStoreNewsItem;


exports.doUdateKISummaryAndStoreInDatabase = async (req, res) => {
    try {
        const override = (req.query.override === 'true' || req.query.override === true);
        const limit = parseInt(req.query.limit) || 35;
        const maxChars = 850000;

        const structuredSummaries = await updateOptimizedTextBodyWithMergeSymbolTeaserStructured(limit, maxChars);
        const key = `GPTDaySummary_${new Date().toDateString()}`;

        if (process.env.NODE_ENV !== 'production')
          console.log("[gpt][LLMNewsSummarizeMerge] Processing GPTDaySummary:", { key, limit, override, summariesLength: structuredSummaries.length });

        let result = await getOrCreateDatabaseEntryForMerge(key, structuredSummaries);

        if (getShouldUpdateKISummary(result, structuredSummaries, override)) {
            if (process.env.NODE_ENV !== 'production')
              console.log("[gpt][LLMNewsSummarizeMerge] Updating summary");
            await updateSummarizeWithLLM(result, structuredSummaries);
            result.gpt_summarize_date = new Date();
        }

        result.optimized_textbody = structuredSummaries;
        await databaseHelper.database_update(result);

        res.json(result);
    } catch (error) {
        console.error("[gpt][LLMNewsSummarizeMerge] Error in gptNewsSummarizeMerge:", error);
        res.status(500).json({ error: "[gpt][LLMNewsSummarizeMerge] An error occurred while processing the request." });
    }
};


exports.updateOptimizedTextBodyWithMergeSymbolTeaserStructured = async function(newsLimit, charLimit) {
    const inputJsons = await databaseHelper.getNewsSummariesFromDay(newsLimit);
    if (process.env.NODE_ENV !== 'production')
        console.log("[updateOptimizedTextBodyWithMergeSymbolTeaserStructured] Updating optimized text body (structured):", { inputJsonsLength: inputJsons.length, charLimit, newsLimit });

    const outputJson = {
        summaryTitle: "Gesamtübersicht",
        summaryReports: [],
        summaries: [],
    };

    let totalLength = 0;

    for (const input of inputJsons) {
        if (input.gpt_summarize && typeof input.gpt_summarize === 'string') {
            let summary;
            try {
                const parsedSummary = JSON.parse(input.gpt_summarize);
                summary = parsedSummary.summary || input.gpt_summarize;
            } catch (error) {
                // If parsing fails, use the original string
                summary = input.gpt_summarize;
            }

            outputJson.summaries.push(summary);
            totalLength += summary.length;

            // Extract symbols and teasers if available
            const symbolMatch = summary.match(/Symbol: ([A-Z0-9]+)/);
            const teaserMatch = summary.match(/Teaser: (.+)/);

            if (symbolMatch && teaserMatch) {
                const symbol = symbolMatch[1];
                const teaser = teaserMatch[1];

                if (ALLOWED_SYMBOLS.includes(symbol)) {
                    let existingReport = outputJson.summaryReports.find(report => report.symbol === symbol);
                    if (!existingReport) {
                        existingReport = { symbol, reports: [] };
                        outputJson.summaryReports.push(existingReport);
                    }

                    const newReport = { teasers: [teaser] };
                    const reportLength = JSON.stringify(newReport, null, 2).length;
                    totalLength += reportLength;

                    if (totalLength < charLimit) {
                        existingReport.reports.push(newReport);
                    } else {
                        if (process.env.NODE_ENV !== 'production')
                            console.log(`Reached char limit. Ignoring further messages. Total length: ${totalLength}`);
                        break;
                    }
                }
            }
        } else {
            console.warn(`Invalid gpt_summarize for input: ${input.title}`);
        }

        if (totalLength >= charLimit) break;
    }

    return JSON.stringify(outputJson, null, 2);
}

exports.getOrCreateDatabaseEntryForMerge = async function(key, structuredSummaries) {
    let result = await databaseHelper.database_read(key);
    if (result.uuid === null) {
        const newsItem = {
            key,
            time: null,
            title: key,
            bodylen: structuredSummaries.length,
            body: structuredSummaries,
        };
        result = await databaseHelper.database_init(newsItem);
        result.gpt_summarize_date = new Date();
    }
    return result;
}

/**
 * Check if the GPT summary for merged data should be updated
 * @param {Object} result - The database result
 * @param {string} concatenatedSummaries - The concatenated summaries
 * @param {boolean} override - Flag to force update
 * @returns {boolean} True if update is needed, false otherwise
 */
exports.getShouldUpdateKISummary = function(result, structuredSummaries, override) {
    const mismatchBodyLen = result.bodylen !== structuredSummaries.length;
    const delta = structuredSummaries.length - result.bodylen;
    result.gpt_refresh_reason = "";

    if (override) {
        result.gpt_refresh_reason = `Override flag: ${override}`;
        if (process.env.NODE_ENV !== 'production')
          console.log(result.gpt_refresh_reason);
    }

    if (mismatchBodyLen && delta > 30) {
        const msg = `Mismatch in body length (delta > 30): old=${result.bodylen}, new=${structuredSummaries.length}, delta=${delta}`;
        result.gpt_refresh_reason += (result.gpt_refresh_reason ? " | " : "") + msg;
        if (process.env.NODE_ENV !== 'production')
          console.log(msg);
    }

    return result.gpt_summarize === null || override;
}

/**
 * Bereinigt JSON-Strings von häufigen Syntax-Problemen
 * @param {string} jsonString - Der zu bereinigende JSON-String
 * @returns {string} - Bereinigter JSON-String
 */
function sanitizeJsonString(jsonString) {
    if (!jsonString || typeof jsonString !== 'string') {
        return jsonString;
    }
    
    return jsonString
        // Entfernt trailing commas vor } und ]
        .replace(/,\s*([}\]])/g, '$1')
        // Entfernt mehrfache Kommas
        .replace(/,\s*,+/g, ',')
        // Entfernt Kommas am Anfang von Arrays/Objekten nach { oder [
        .replace(/([{\[])\s*,/g, '$1')
        // Entfernt Kommas vor Zeilenende in Objekten/Arrays
        .replace(/,\s*(\r?\n\s*[}\]])/g, '$1')
        // Normalisiert Whitespace um Kommas
        .replace(/\s*,\s*/g, ', ')
        // Entfernt überflüssige Leerzeichen
        .trim();
}

/**
 * Update the GPT summary for merged data
 * @param {Object} result - The database result object
 * @param {string} concatenatedSummaries - The concatenated summaries
 */
exports.updateSummarizeWithLLM = async function(result, structuredSummaries) {
    const promptVer = 2;
    const promptID = "ki-algobot.llm-news-aggregation";
    
    const rawResponse = await requestLLM([], LLM.GOOGLE_GEMINI_2_5_PRO, null,
        promptID,
        promptVer,
        { "SUMMARIES": structuredSummaries});
    
    // JSON-Bereinigung anwenden, um überflüssige Kommas zu entfernen
    result.gpt_summarize = sanitizeJsonString(rawResponse);
    
    // Zusätzliche JSON-Validierung für Robustheit
    try {
        JSON.parse(result.gpt_summarize);
        if (process.env.NODE_ENV !== 'production')
            console.log("[updateSummarizeWithLLM] JSON erfolgreich bereinigt und validiert");
    } catch (error) {
        console.warn("[updateSummarizeWithLLM] JSON-Bereinigung war nicht erfolgreich:", error.message);
        console.warn("[updateSummarizeWithLLM] Verwende Raw Response:", rawResponse.substring(0, 200) + '...');
        // Fallback: Raw response verwenden
        result.gpt_summarize = rawResponse;
    }
    
    result.gpt_prompt_version = promptVer;
    result.gpt_prompt = promptID;
    result.body = JSON.stringify(structuredSummaries);
    result.optimized_textbody = JSON.stringify(structuredSummaries);
}

/**
 * Update optimized text body with merged symbol teaser (raw data)
 * @param {number} newsLimit - The limit of news items to process
 * @param {number} charLimit - The character limit for the output
 * @returns {string} The concatenated summaries
 */
async function updateOptimizedTextBodyWithMergeSymbolTeaser(newsLimit, charLimit) {
    const inputJsons = await databaseHelper.getNewsSummariesFromDay(newsLimit);
    if (process.env.NODE_ENV !== 'production')
      console.log("Updating optimized text body:", { inputJsonsLength: inputJsons.length, charLimit, newsLimit });

    let outputString = "";
    for (const input of inputJsons) {
        outputString += `# ${input.title}\n${input.gpt_summarize}\n`;
        if (process.env.NODE_ENV !== 'production')
            console.log("Processing input:", input.title);
    }

    return outputString;
}


const factorMapService = require('../../services/factor-map-service');
const {updateOptimizedTextBodyWithMergeSymbolTeaserStructured, getOrCreateDatabaseEntryForMerge,
    getShouldUpdateKISummary, updateSummarizeWithLLM
} = require("./controller_gpt");


exports.storeNews = async (req, res) => {
    try {
        let newsItems;
        
        // Handle different content types
        if (req.is('text/plain') && typeof req.body === 'string') {
            try {
                newsItems = JSON.parse(req.body);
                console.log("[gpt][storeNews] Received news items:", newsItems);
            } catch (error) {
                return res.status(400).json({ 
                    error: "Invalid JSON in request body. Please provide a valid JSON array of news items." 
                });
            }
        } else {
            newsItems = req.body;
        }
        
        // Validate that the body is an array
        if (!newsItems || !Array.isArray(newsItems)) {
            return res.status(400).json({ error: "Request body must be an array of news items" });
        }

        const override = (req.query.override === 'true' || req.query.override === true);
        const promptVersion = parseInt(req.query.promptVersion) || -1;
        const results = [];

        for (const newsItem of newsItems) {
            if (process.env.NODE_ENV !== 'production')
                console.log(`[gpt][storeNews] Processing news item: ${newsItem.title}, key: ${newsItem.key}`);
            
            const result = await newsSummaryService.summarizeAndStoreNewsItem(
                newsItem, 
                override, 
                promptVersion
            );
            results.push(result);
        }

        res.json({ 
            status: "success", 
            message: `${results.length} news items processed successfully`,
            preparations: results.length
        });
    } catch (error) {
        console.error("[gpt][storeNews] Error:", error);
        res.status(500).json({ error: "An error occurred while storing and summarizing the news items." });
    }
};

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/gpt-endpoints.json
 * 
 * @tags [AI Integration]
 */
exports.refreshFactorMap = async (req, res) => {
    try {
        const limitUsedGPTSummaries = req.query.limit || 5;
        const articleLimit = req.query.articel_limit || 15;
        const refreshNews = req.query.refreshNews || false;

        const newEntry = await factorMapService.refreshFactorMap(
            limitUsedGPTSummaries,
            articleLimit,
            refreshNews
        );

        res.json({
            message: "Factor Map refreshed successfully",
            newEntry: newEntry
        });
    } catch (error) {
        console.error("Error in refreshFactorMap:", error);
        res.status(500).json({ error: "An error occurred while refreshing the Factor Map." });
    }
};


