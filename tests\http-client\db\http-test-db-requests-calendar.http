###

// @name calendar-toggles
PUT {{API_BASE_URL}}/api/v1/db/calendar/toggles/5153?toggle_deactivate_new_trades=1&toggle_deactivate_high_volume_trades=1&toggle_close_all_positions_before=1&toggle_actiontime_minutes_window_before=5&toggle_actiontime_minutes_window_after=45
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: Calendar Toggles Update");
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Response has correct structure", function() {
        var jsonData = response.body;
        client.assert(jsonData.hasOwnProperty('state'), "Response does not have 'state' property");
        client.assert(jsonData.state === "successful", "State is not 'sucessfull'");
    });
%}

###

// @name calendar-deltareport : calendarID
GET {{API_BASE_URL}}/api/v1/db/calendar/deltareport?calendar_id=5336&symbol=US100
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name calendar-list
GET {{API_BASE_URL}}/api/v1/db/calendar/list?minus_days=30&plus_days=10
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: calendar");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###
