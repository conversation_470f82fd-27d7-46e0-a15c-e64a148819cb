const ig = require('../../configs/config_ig');
const { LOG_LEVELS, log } = require('../../services/logging_service');
const db = require('../../configs/config_db');
const IGTradePreparationService = require('../../services/ig_trade_preparation');
const IGTradeExecutorService = require('../../services/ig_trade_executor');
const IGMarketDataProvider = require('../../services/ig_market_data_provider');

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/ig-trading-endpoints-put-post.json
 * and docs/swagger-definitions/ig-trading-components.json
 * 
 * @tags [IG Trading]
 */
exports.ig_trade_bridgeformat = async (req, res) => {
    try {
        const refID = req.query.refID || 'IG-P1';
        const tradeRequest = req.body;

        log(LOG_LEVELS.INFO, 'ig_trade_bridgeformat', 'Processing legacy trade request', {
            refID,
            trade: tradeRequest
        });

        const executor = new IGTradeExecutorService(ig.igClientPools[refID], ig.logger);
        if (!executor) {
            return res.status(400).json({ error: 'Invalid refID' });
        }

        // Prepare the trade using IGTradePreparationService
        const marketDataProvider = new IGMarketDataProvider(ig.igClientPools[refID], ig.logger);
        const preparationService = new IGTradePreparationService(marketDataProvider);

        await preparationService.prepare(tradeRequest);

        const result = await executor.executeTrade(tradeRequest);

        // Store trade log if successful
        if (result.status === 'success') {
            await storeTradeLog(refID, tradeRequest, result, 'EXECUTED','API-Bridge');
        }

        res.json(result);

    } catch (err) {
        log(LOG_LEVELS.ERROR, 'ig_trade_legacy', 'Trade execution error', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: err.message });
    }
};

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/ig-trading-endpoints-put-post.json
 * and docs/swagger-definitions/ig-trading-components.json
 * 
 * @tags [IG Trading]
 */
exports.executeTrade = async (req, res) => {
    try {
        const refID = req.query.refID || 'IG-D1';
        const tradeRequest = req.body;

        log(LOG_LEVELS.INFO, 'executeTrade', 'Processing trade request', {
            refID,
            trade: tradeRequest
        });

        const executor = new IGTradeExecutorService(ig.igClientPools[refID]);
        if (!executor) {
            return res.status(400).json({ error: 'IGTradeExecutorService with refID couldn\'t initialized' });
        }

        const result = await executor.executeTrade(tradeRequest);

        if (result.status === 'success') {
            await storeTradeLog(refID, tradeRequest, result, 'API-Trade');
        }

        res.json(result);

    } catch (err) {
        log(LOG_LEVELS.ERROR, 'executeTrade', 'Trade execution error', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: err.message });
    }
};

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/ig-trading-endpoints-put-post.json
 * and docs/swagger-definitions/ig-trading-components.json
 * 
 * @tags [IG Trading]
 */
exports.closePosition = async (req, res) => {
    try {
        const refID = req.query.refID || 'IG-P1';
        const { dealId, ...closeParams } = req.body;

        if (!dealId) {
            return res.status(400).json({ error: 'dealId is required' });
        }

        const executor = new IGTradeExecutorService(ig.igClientPools[refID]);
        if (!executor) {
            return res.status(400).json({ error: 'Invalid refID' });
        }
        if (!closeParams.currencyCode) {
            const symbolDetails = await executor.getSymbolDetails(closeParams.symbol);
            closeParams.currencyCode = symbolDetails.currency;
            log(LOG_LEVELS.INFO, 'closePosition', 'Currency code not provided, using symbol details', {
                symbol: closeParams.symbol,
                symbolDetails: JSON.stringify(symbolDetails),
                currencyCode: closeParams.currencyCode
            });
        }

        const result = await executor.closePosition(dealId, closeParams);

        // Store trade log
        if (result.code === 200) {
            await storeTradeLog(refID, req.body, result, 'CLOSED', 'API-ClosePosition');
        }

        res.json(result);

    } catch (err) {
        log(LOG_LEVELS.ERROR, 'closePosition', 'Position close error', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: err.message });
    }
};

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/ig-trading-endpoints-put-post.json
 * and docs/swagger-definitions/ig-trading-components.json
 * 
 * @tags [IG Trading]
 */
exports.modifyPosition = async (req, res) => {
    try {
        const refID = req.query.refID || 'IG-P1';
        const { dealId, ...modifyParams } = req.body;

        if (!dealId) {
            return res.status(400).json({ error: 'dealId is required' });
        }

        const executor = new IGTradeExecutorService(ig.igClientPools[refID]);
        if (!executor) {
            return res.status(400).json({ error: 'Invalid refID' });
        }

        const result = await executor.modifyPosition(dealId, modifyParams);

        // Store trade log
        if (result.code === 200) {
            await storeTradeLog(refID, req.body, result, 'MODIFIED', 'API-ModifyPosition');
        }

        res.json(result);

    } catch (err) {
        log(LOG_LEVELS.ERROR, 'modifyPosition', 'Position modification error', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: err });
    }
};

/**
 * Note: Swagger documentation has been moved to docs/swagger-definitions/ig-trading-endpoints-put-post.json
 * and docs/swagger-definitions/ig-trading-components.json
 * 
 * @tags [IG Trading]
 */
exports.getTradeStatus = async (req, res) => {
    try {
        const refID = req.query.refID || 'IG-P1';
        const { dealReference } = req.body;

        if (!dealReference) {
            return res.status(400).json({ error: 'dealReference is required' });
        }

        const executor = new IGTradeExecutorService(ig.igClientPools[refID]);
        if (!executor) {
            return res.status(400).json({ error: 'Invalid refID' });
        }

        const result = await executor.getTransactionStatus(dealReference);
        res.json(result);

    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getTradeStatus', 'Trade status check error', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({ error: err.message });
    }
};

async function storeTradeLog(refID, tradeData, result, status = 'EXECUTED', source = 'API') {
    const query = `
        INSERT INTO mirror_trading_logs_profit
        (targetRefID, log_timestamp, log_order2, log_order, profit, sl, tp, \`offset\`, status, additional_info, source)
        VALUES (?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    log(LOG_LEVELS.DEBUG, 'storeTradeLog', 'Storing trade log', {
        refID,
        status,
        source,
        tradeData: JSON.stringify(tradeData),
        dealId: tradeData.dealId,
    });
    const params = [
        refID,
        tradeData.dealId,
        tradeData.dealId,
        tradeData.profit || 0,
        tradeData.sl || tradeData.stopLevel || 0,
        tradeData.tp || tradeData.limitLevel || 0,
        tradeData.offset || 0,
        status,
        JSON.stringify(tradeData),
        source
    ];

    try {
        if (tradeData.profit != null) {
            await db.pool.query(query, params);
            log(LOG_LEVELS.DEBUG, 'storeTradeLog', 'Trade log stored', {
                refID,
                status,
                dealId: tradeData.dealId
            });
        } else {
            log(LOG_LEVELS.DEBUG, 'storeTradeLog', 'Trade log not stored due to profit condition', {
                refID,
                status,
                dealId: tradeData.dealId,
                profit: tradeData.profit
            });
        }
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'storeTradeLog', 'Failed to store trade log', {
            error: err.message,
        });
    }
}