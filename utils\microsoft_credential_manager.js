const crypto = require('crypto');
const { LoggingService } = require('../services/logging_service');

/**
 * Microsoft Credential Manager
 * 
 * Provides secure credential management for Microsoft Graph API integration:
 * - Credential validation and sanitization
 * - Secure credential storage patterns
 * - Credential rotation support
 * - Security audit logging
 */

const logger = LoggingService.getInstance();

class MicrosoftCredentialManager {
    constructor() {
        this.requiredCredentials = [
            'MICROSOFT_TENANT_ID',
            'MICROSOFT_CLIENT_ID', 
            'MICROSOFT_CLIENT_SECRET'
        ];
        
        this.credentialPatterns = {
            MICROSOFT_TENANT_ID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
            MICROSOFT_CLIENT_ID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
            MICROSOFT_CLIENT_SECRET: /^.{8,}$/ // At least 8 characters
        };
    }

    /**
     * Validate all Microsoft credentials
     * @returns {Object} Validation result with details
     */
    validateCredentials() {
        const result = {
            isValid: true,
            missingCredentials: [],
            invalidCredentials: [],
            warnings: []
        };

        for (const credential of this.requiredCredentials) {
            const value = process.env[credential];
            
            if (!value) {
                result.isValid = false;
                result.missingCredentials.push(credential);
                continue;
            }

            // Validate credential format
            const pattern = this.credentialPatterns[credential];
            if (pattern && !pattern.test(value)) {
                result.isValid = false;
                result.invalidCredentials.push({
                    credential,
                    reason: 'Invalid format'
                });
                continue;
            }

            // Security checks
            if (this._isWeakCredential(credential, value)) {
                result.warnings.push({
                    credential,
                    reason: 'Potentially weak credential detected'
                });
            }
        }

        // Log validation results
        if (!result.isValid) {
            logger.error('Microsoft credential validation failed', {
                missingCredentials: result.missingCredentials,
                invalidCredentials: result.invalidCredentials.map(c => c.credential),
                warningCount: result.warnings.length
            });
        } else {
            logger.info('Microsoft credential validation successful', {
                credentialCount: this.requiredCredentials.length,
                warningCount: result.warnings.length
            });
        }

        return result;
    }

    /**
     * Get sanitized credential for logging
     * @param {string} credentialName 
     * @returns {string} Sanitized credential value
     */
    getSanitizedCredential(credentialName) {
        const value = process.env[credentialName];
        if (!value) return 'NOT_SET';
        
        // Show only first and last 4 characters for UUIDs
        if (this.credentialPatterns[credentialName] === this.credentialPatterns.MICROSOFT_TENANT_ID ||
            this.credentialPatterns[credentialName] === this.credentialPatterns.MICROSOFT_CLIENT_ID) {
            if (value.length >= 8) {
                return `${value.substring(0, 4)}...${value.substring(value.length - 4)}`;
            }
        }
        
        // For secrets, show only length and first 2 characters
        if (credentialName.includes('SECRET')) {
            return `${value.substring(0, 2)}...(${value.length} chars)`;
        }
        
        return `${value.substring(0, 4)}...(${value.length} chars)`;
    }

    /**
     * Generate correlation ID for credential operations
     * @returns {string} Correlation ID
     */
    generateCorrelationId() {
        return `cred_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;
    }

    /**
     * Audit credential access
     * @param {string} operation - Operation being performed
     * @param {string} correlationId - Correlation ID for tracking
     */
    auditCredentialAccess(operation, correlationId) {
        logger.info('Microsoft credential access', {
            operation,
            correlationId,
            timestamp: new Date().toISOString(),
            tenantId: this.getSanitizedCredential('MICROSOFT_TENANT_ID'),
            clientId: this.getSanitizedCredential('MICROSOFT_CLIENT_ID')
        });
    }

    /**
     * Check if credential appears to be weak
     * @private
     */
    _isWeakCredential(credentialName, value) {
        // Check for common weak patterns
        const weakPatterns = [
            /^(test|demo|dev|sample)/i,
            /^(password|secret|key)$/i,
            /^(123|abc|000)/,
            /^(.)\1{7,}$/ // Repeated characters
        ];

        return weakPatterns.some(pattern => pattern.test(value));
    }

    /**
     * Validate credential rotation requirements
     * @returns {Object} Rotation status and recommendations
     */
    checkCredentialRotation() {
        const result = {
            rotationNeeded: false,
            recommendations: [],
            lastRotationCheck: new Date().toISOString()
        };

        // In a production environment, you would check:
        // - Last rotation date from secure storage
        // - Credential age policies
        // - Security incident flags
        
        // For now, provide general recommendations
        result.recommendations.push({
            type: 'security',
            message: 'Regularly rotate Microsoft Graph API credentials (recommended: every 90 days)'
        });

        result.recommendations.push({
            type: 'monitoring',
            message: 'Monitor credential usage patterns for anomalies'
        });

        logger.info('Credential rotation check completed', {
            rotationNeeded: result.rotationNeeded,
            recommendationCount: result.recommendations.length
        });

        return result;
    }

    /**
     * Create secure configuration object
     * @returns {Object} Secure configuration with validated credentials
     */
    createSecureConfig() {
        const validation = this.validateCredentials();
        
        if (!validation.isValid) {
            throw new Error(`Invalid Microsoft credentials: ${validation.missingCredentials.concat(
                validation.invalidCredentials.map(c => c.credential)
            ).join(', ')}`);
        }

        const correlationId = this.generateCorrelationId();
        this.auditCredentialAccess('config_creation', correlationId);

        return {
            tenantId: process.env.MICROSOFT_TENANT_ID,
            clientId: process.env.MICROSOFT_CLIENT_ID,
            clientSecret: process.env.MICROSOFT_CLIENT_SECRET,
            correlationId,
            validationWarnings: validation.warnings
        };
    }

    /**
     * Mask sensitive data in objects for logging
     * @param {Object} data - Data object to mask
     * @returns {Object} Masked data object
     */
    maskSensitiveData(data) {
        if (!data || typeof data !== 'object') return data;

        const masked = { ...data };
        const sensitiveFields = [
            'client_secret', 'clientSecret', 'secret', 'password', 'token', 'access_token',
            'refresh_token', 'authorization', 'auth', 'key', 'apiKey', 'api_key'
        ];

        for (const field of sensitiveFields) {
            if (masked[field]) {
                const value = masked[field];
                if (typeof value === 'string' && value.length > 4) {
                    masked[field] = `${value.substring(0, 2)}...(${value.length} chars)`;
                } else {
                    masked[field] = '[MASKED]';
                }
            }
        }

        return masked;
    }
}

// Singleton instance
let instance = null;

/**
 * Get singleton instance of MicrosoftCredentialManager
 * @returns {MicrosoftCredentialManager}
 */
function getInstance() {
    if (!instance) {
        instance = new MicrosoftCredentialManager();
    }
    return instance;
}

module.exports = {
    MicrosoftCredentialManager,
    getInstance
};
