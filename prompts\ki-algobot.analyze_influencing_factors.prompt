You are an analyst and stock market expert. Your task is to analyze the relevant influence factors for the stock market based on the following news. Focus particularly on the strong recurring factors for global indices, especially NASDAQ, DAX, and S&P500.

Here are the news items to analyze:
<nachrichten>
{{NACHRICHTEN}}
</nachrichten>

Follow these steps:

1. Carefully read and analyze the provided news.
2. Identify the main influence factors on the stock market.
3. Categorize these factors into broader categories (e.g., "Economic Factors", "Monetary Policy", etc.).
4. Determine the impact of each factor on a scale of 1 to 10.
5. Classify each factor as either bullish (long) or bearish (short).
6. Provide relevant details or facts for each factor.

Format your response strictly as a JSON structure. This structure will be used to generate a treemap in Apexcharts. Follow these guidelines:

- Use the provided basic structure as a template.
- Each main category should have a "name" and "color" (use appropriate hex color codes).
- Within each category, include a "data" array with individual factors.
- For each factor in the "data" array:
  - Use "x" for the factor name, prefixing with "[L]" for bullish (long) factors or "[S]" for bearish (short) factors.
  - Use "y" for the impact value (1-10).
  - Use "hint" to provide additional details or facts.
- Factor names should be concise and include relevant facts, e.g., "[S] DexCom Quarterly Results with -41% Price Reaction".
- Ensure all influence factors are between 1 and 10.

Here's an example of the desired JSON structure:

[  
  {  
    "name": "Wirtschaftliche Faktoren: Makroökonomische Entwicklungen",  
    "color": "#008FFB",  
    "data": [  
      {  
        "x": "[L] US-BIP-Bericht mit 2.8% vs. 2%",  
        "y": 3,  
        "hint": "Positive Überraschung im US-BIP-Bericht für Q2 2024 mit einem Anstieg des annualisierten Wachstums auf 2,8 %, deutlich über den erwarteten 2,0 %. Verbraucherausgaben übertrafen ebenfalls die Erwartungen."  
      },  
      {  
        "x": "[S] US-Hausverkäufe hinter den Erwartungen",  
        "y": 2,  
        "hint": "US-Daten zu neuen Hausverkäufen blieben hinter den Erwartungen zurück, mit einem Rückgang im Juni. Schwächere Daten beim Verkauf bestehender Häuser deuten auf mögliche zukünftige Marktbelastungen hin."  
      }  
    ]  
  },  
  {  
    "name": "Marktanalyse: Sentiment-Analysen",  
    "color": "#FEB019",  
    "data": [  
      {  
        "x": "[S] Greed / Fear Index mit 39 im Fear-Bereich",  
        "y": 4,  
        "hint": "Die Stimmung der internationalen Anleger gemessen am Greed / Fear Index zeigt mit einem Wert von 39 eine 'Fear'-Stimmung; die Stimmung hat sich verglichen zu vorherigen Zeitpunkten abgekühlt."  
      }  
    ]  
  }  
];

Provide only the final result as a JSON structure, without any additional explanation or text outside the JSON.