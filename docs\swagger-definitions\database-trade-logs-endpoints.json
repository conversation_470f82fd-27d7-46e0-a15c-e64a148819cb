{"paths": {"/api/v1/db/mirrorTradingLogs": {"get": {"summary": "Trading-Logs abrufen", "description": "Ruft detaillierte Trading-Logs für ein bestimmtes Konto oder eine Kombination von Konten ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Informationen zu Trading-Aktivitäten\n- Filterbar nach Zeitraum in Tagen\n- Limitierbar auf eine bestimmte Anzahl von Einträgen\n- Option zur Reduzierung der Nachrichtenlänge für kompaktere Anzeige\n\nAnwendungsfälle:\n- Überwachung von Trading-Aktivitäten\n- Fehleranalyse bei fehlgeschlagenen Trades\n- Audit-Trail für Trading-Operationen\n- Debugging von Trading-Systemen", "tags": ["Trade Logs"], "parameters": [{"in": "query", "name": "refID", "required": true, "schema": {"type": "string"}, "description": "Primäre Referenz-ID des Kontos", "example": "IG-D1"}, {"in": "query", "name": "refID_alternate", "required": false, "schema": {"type": "string", "default": "-"}, "description": "Alternative Referenz-ID für die Filterung (optional)", "example": "IG-P1"}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 400, "minimum": 1, "maximum": 1000}, "description": "Maximale Anzahl der zurückzugebenden Log-Einträge", "example": 400}, {"in": "query", "name": "days", "required": false, "schema": {"type": "integer", "default": 365, "minimum": 1, "maximum": 365}, "description": "<PERSON><PERSON><PERSON> der Tage in der Vergangenheit, für die Logs abgerufen werden sollen", "example": 365}, {"in": "query", "name": "reducemessage", "required": false, "schema": {"type": "boolean", "default": false}, "description": "<PERSON>n true, werden die Nachrichtentexte gekürzt, um die Antwortgröße zu reduzieren", "example": false}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Trading-Logs", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeLog"}}, "example": [{"log_id": 12345, "timestamp": "2023-04-15T14:30:00Z", "refId": "IG-D1", "refId_alternate": "IG-P1", "action": "OPEN_TRADE", "symbol": "EURUSD", "message": "Opening trade for EURUSD at 1.0865, volume 1.5, SL at 1.0840, TP at 1.0900", "level": "INFO", "source": "TradeExecutor", "trade_id": "DIAAABC123", "status": "SUCCESS", "error_message": null}, {"log_id": 12344, "timestamp": "2023-04-15T13:45:00Z", "refId": "IG-D1", "refId_alternate": "IG-P1", "action": "CLOSE_TRADE", "symbol": "GBPUSD", "message": "Closing trade DIAAABC122 for GBPUSD at 1.2430, profit: 20.0", "level": "INFO", "source": "TradeExecutor", "trade_id": "DIAAABC122", "status": "SUCCESS", "error_message": null}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}