const DEFAULT_API_KEY = 'ml!algotrader';

const apiKeyAuth = (req, res, next) => {
    // Check multiple possible locations for the API key
    const headerApiKey = req.header('X-API-Key');
    const queryApiKey = req.query.api_key || req.query.apiKey || req.query.key;
    const validApiKey = process.env.API_KEY || DEFAULT_API_KEY;
    const decodedQueryApiKey = queryApiKey ? decodeURIComponent(queryApiKey) : null;
    
    if (headerApiKey === validApiKey || decodedQueryApiKey === validApiKey) {
        next();
    } else {
        return res.status(401).json({ 
            error: 'Unauthorized - Invalid API Key',
            hint: process.env.NODE_ENV !== 'production' ? 
                  `Expected: ${validApiKey}, Got: ${headerApiKey || decodedQueryApiKey || 'none'}` : undefined
        });
    }
};

module.exports = { apiKeyAuth };
