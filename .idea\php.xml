<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <PhpCSConfiguration asDefaultInterpreter="true" />
    </phpcs_settings>
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/../AlgoTrader/common/vendor/textalk/websocket" />
      <path value="$PROJECT_DIR$/../AlgoTrader/common/vendor/composer" />
      <path value="$PROJECT_DIR$/../AlgoTrader/common/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/../AlgoTrader/common/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/../AlgoTrader/common/vendor/psr/log" />
      <path value="$PROJECT_DIR$/../AlgoTrader/common/vendor/phrity/util-errorhandler" />
      <path value="$PROJECT_DIR$/../AlgoTrader/common/vendor/phrity/net-uri" />
      <path value="$PROJECT_DIR$/../AlgoTrader/common/vendor/phpmailer/phpmailer" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="7.4">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>