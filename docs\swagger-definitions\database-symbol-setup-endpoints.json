{"paths": {"/api/v1/db/symbol_setups": {"get": {"summary": "Symbol-Setups abrufen (alle Symbole)", "description": "Ruft die Trading-Konfigurationen für alle Symbole ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Konfigurationsparameter für jedes Symbol\n- Filterbar nach Timeframe und Handelsrichtung\n- Enthält Deadzone-Informationen für Risikomanagement\n\nAnwendungsfälle:\n- Initialisierung von Trading-Systemen\n- Konfiguration von Trading-Parametern\n- Überwachung von Deadzones für Risikomanagement\n- Anzeige von Symbol-Konfigurationen im Trading-Dashboard", "tags": ["Symbol Management"], "parameters": [{"in": "query", "name": "timeframe", "required": false, "schema": {"type": "integer", "default": 30}, "description": "Zeitrahmen in Minuten", "example": 30}, {"in": "query", "name": "cmd", "required": false, "schema": {"type": "integer", "default": 0, "enum": [0, 1]}, "description": "Handelsrichtung (0 = <PERSON>, 1 = Short)", "example": 0}], "responses": {"200": {"description": "Erfolgreiche Abfrage der Symbol-Setups", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SymbolSetup"}}, "example": [{"symbol": "EURUSD", "timeframe": 30, "cmd": 0, "defaulthighchancevolume": 2.0, "defaultmidchancevolume": 1.0, "chartPriceOffsetXTBandTradeView": 0.0005, "stoppOffset": 0.0025, "stoppOffSet_targetTF": 0.0035, "stoppOffsetmidchancevolume": 0.002, "trailstoppOffset": 0.0015, "trailstoppOffsetForWinningPositions": 0.001, "minimum_volume_m15": 0.5, "deadZoneLow": 1.085, "deadZoneHigh": 1.087, "deadZoneUpdateTime": "2023-04-15T14:30:00Z", "deadZoneValidUntil": "2023-04-24", "deadZoneUpdateSource": "Postman", "deadZoneFromTime": "08:00:00", "deadZoneToTime": "16:00:00"}, {"symbol": "GBPUSD", "timeframe": 30, "cmd": 0, "defaulthighchancevolume": 1.5, "defaultmidchancevolume": 0.8, "chartPriceOffsetXTBandTradeView": 0.0005, "stoppOffset": 0.003, "stoppOffSet_targetTF": 0.004, "stoppOffsetmidchancevolume": 0.0025, "trailstoppOffset": 0.002, "trailstoppOffsetForWinningPositions": 0.0015, "minimum_volume_m15": 0.4, "deadZoneLow": 1.245, "deadZoneHigh": 1.247, "deadZoneUpdateTime": "2023-04-15T14:30:00Z", "deadZoneValidUntil": "2023-04-24", "deadZoneUpdateSource": "Postman", "deadZoneFromTime": "08:00:00", "deadZoneToTime": "16:00:00"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}, "/api/v1/db/symbol_setup/{symbol}": {"get": {"summary": "Symbol-Setup für ein bestimmtes Symbol abrufen", "description": "Ruft die Trading-Konfiguration für ein bestimmtes Symbol ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält detaillierte Konfigurationsparameter für das angegebene Symbol\n- Filterbar nach Timeframe und Handelsrichtung\n- Enthält Deadzone-Informationen für Risikomanagement\n\nAnwendungsfälle:\n- Konfiguration von Trading-Parametern für ein bestimmtes Symbol\n- Überwachung von Deadzones für Risikomanagement\n- Anzeige von Symbol-Konfigurationen im Trading-Dashboard\n- Initialisierung von symbolspezifischen Trading-Strategien", "tags": ["Symbol Management"], "parameters": [{"in": "path", "name": "symbol", "required": true, "schema": {"type": "string"}, "description": "Trading-Symbol, für das die Konfiguration abgerufen werden soll", "example": "EURUSD"}, {"in": "query", "name": "timeframe", "required": false, "schema": {"type": "integer", "default": 30}, "description": "Zeitrahmen in Minuten", "example": 30}, {"in": "query", "name": "cmd", "required": false, "schema": {"type": "integer", "default": 0, "enum": [0, 1]}, "description": "Handelsrichtung (0 = <PERSON>, 1 = Short)", "example": 0}], "responses": {"200": {"description": "Erfolgreiche Abfrage des Symbol-Setups", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SymbolSetup"}}, "example": [{"symbol": "EURUSD", "timeframe": 30, "cmd": 0, "defaulthighchancevolume": 2.0, "defaultmidchancevolume": 1.0, "chartPriceOffsetXTBandTradeView": 0.0005, "stoppOffset": 0.0025, "stoppOffSet_targetTF": 0.0035, "stoppOffsetmidchancevolume": 0.002, "trailstoppOffset": 0.0015, "trailstoppOffsetForWinningPositions": 0.001, "minimum_volume_m15": 0.5, "deadZoneLow": 1.085, "deadZoneHigh": 1.087, "deadZoneUpdateTime": "2023-04-15T14:30:00Z", "deadZoneValidUntil": "2023-04-24", "deadZoneUpdateSource": "Postman", "deadZoneFromTime": "08:00:00", "deadZoneToTime": "16:00:00"}]}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}