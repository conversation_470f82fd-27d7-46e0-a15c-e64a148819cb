{"components": {"schemas": {"WeekStatistics": {"type": "object", "properties": {"year": {"type": "integer", "description": "Jahr der Statistik", "example": 2023}, "week": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> (1-53)", "minimum": 1, "maximum": 53, "example": 15}, "min_profit": {"type": "number", "format": "float", "description": "Minimaler Gewinn/Verlust in dieser Woche", "example": -180}, "max_profit": {"type": "number", "format": "float", "description": "Maximaler Gewinn/Verlust in dieser Woche", "example": 420}, "cnt_trades": {"type": "integer", "description": "<PERSON><PERSON><PERSON> der Trades in dieser Woche", "example": 35}, "sum_profit": {"type": "number", "format": "float", "description": "Gesamtgewinn/-verlust in dieser Woche", "example": 850}}}, "CurrentWeekStatistics": {"type": "object", "properties": {"year": {"type": "integer", "description": "Jahr der Statistik", "example": 2023}, "week": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> (1-53)", "minimum": 1, "maximum": 53, "example": 15}, "min_profit": {"type": "number", "format": "float", "description": "Minimaler Gewinn/Verlust in der aktuellen Woche", "example": -180}, "max_profit": {"type": "number", "format": "float", "description": "Maximaler Gewinn/Verlust in der aktuellen Woche", "example": 420}, "cnt_trades": {"type": "integer", "description": "Anzahl der Trades in der aktuellen Woche", "example": 35}, "sum_profit": {"type": "number", "format": "float", "description": "Gesamtgewinn/-verlust in der aktuellen Woche", "example": 850}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Parameter 'refID' is required"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getWeekStatistics"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Parameter 'refID' is required", "function": "getWeekStatistics"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch week statistics", "function": "getWeekStatistics"}}}}}}}