const { LOG_LEVELS, log } = require('./logging_service');
const NodeCache = require('node-cache');

class IGSymbolFetcher {
    constructor(igApiClient, logger, dbHandler = null) {
        this.igApiClient = igApiClient;
        this.logger = logger;
        this.dbHandler = dbHandler;
        this.cache = new NodeCache({ stdTTL: 3600 }); // 1 hour TTL for symbols
        this.BATCH_SIZE = 50;
        this.CONCURRENT_REQUESTS = 3;
    }

    enrichContext(data) {
        return {
            ...data,
            correlation_id: this.igApiClient.correlationId,
            component: 'symbol-fetcher',
            timestamp: Date.now(),
            environment: this.igApiClient.isDemo() ? 'demo' : 'live'
        };
    }

    async getAllSymbols() {
        const startTime = process.hrtime.bigint();
        const cacheKey = 'all_symbols';

        // Try cache first
        const cachedData = this.cache.get(cacheKey);
        if (cachedData) {
            log(LOG_LEVELS.DEBUG, 'getAllSymbols', 'Cache hit', this.enrichContext({
                symbol_count: cachedData.length
            }));
            return cachedData;
        }

        try {
            const transformedSymbols = await this.fetchAllMarketData();
            
            if (transformedSymbols.length > 0) {
                this.cache.set(cacheKey, transformedSymbols);
            }

            log(LOG_LEVELS.INFO, 'getAllSymbols', 'Symbols fetched', this.enrichContext({
                symbol_count: transformedSymbols.length,
                duration_ms: Number(process.hrtime.bigint() - startTime) / 1e6
            }));

            return transformedSymbols;
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'getAllSymbols', 'Error fetching symbols', this.enrichContext({
                error: error.message
            }));
            throw error;
        }
    }

    async fetchAllMarketData() {
        const startTime = process.hrtime.bigint();
        const transformedSymbols = [];
        let currentOffset = 0;
        let hasMore = true;

        while (hasMore) {
            try {
                const result = await this.igApiClient.makeRequest(
                    'GET',
                    `markets?pageSize=${this.BATCH_SIZE}&offset=${currentOffset}`,
                    {},
                    2
                );

                if (result.code !== 200 || !result.response?.markets) {
                    log(LOG_LEVELS.ERROR, 'fetchAllMarketData', 'Invalid response', this.enrichContext({
                        offset: currentOffset,
                        response_code: result.code
                    }));
                    break;
                }

                const markets = result.response.markets;
                
                // Transform and filter valid markets
                const validMarkets = markets
                    .map(market => this.transformMarketToSymbol(market))
                    .filter(symbol => symbol !== null);

                transformedSymbols.push(...validMarkets);

                // Update pagination
                if (markets.length < this.BATCH_SIZE) {
                    hasMore = false;
                } else {
                    currentOffset += this.BATCH_SIZE;
                }

                log(LOG_LEVELS.DEBUG, 'fetchAllMarketData', 'Batch processed', this.enrichContext({
                    offset: currentOffset,
                    batch_size: markets.length,
                    valid_markets: validMarkets.length,
                    total_collected: transformedSymbols.length
                }));

                // Rate limiting pause
                await new Promise(resolve => setTimeout(resolve, 100));

            } catch (error) {
                log(LOG_LEVELS.ERROR, 'fetchAllMarketData', 'Batch error', this.enrichContext({
                    offset: currentOffset,
                    error: error.message
                }));
                hasMore = false;
            }
        }

        log(LOG_LEVELS.INFO, 'fetchAllMarketData', 'All markets fetched', this.enrichContext({
            total_symbols: transformedSymbols.length,
            duration_ms: Number(process.hrtime.bigint() - startTime) / 1e6
        }));

        return transformedSymbols;
    }

    transformMarketToSymbol(market) {
        try {
            if (!market.symbol || !market.instrumentName) {
                return null;
            }

            return {
                symbol: market.epic,
                description: market.instrumentName,
                currency: market.currencies?.[0]?.code || 'USD',
                categoryName: market.instrumentType,
                lotMin: market.lotSize || 0.1,
                lotMax: market.dealingRules?.maxDealSize?.value || 100,
                lotStep: market.dealingRules?.minDealSize?.value || 0.1,
                precision: market.precision || 2,
                contractSize: 1,
                time: Date.now(),
                tickValue: market.tickSize || 0.01,
                marginMode: 0,
                profitMode: 0,
                swapType: 0,
                swapLong: 0,
                swapShort: 0,
                starting: null,
                expiration: null,
                stopsLevel: market.dealingRules?.minControlledRiskStopDistance?.value || 0,
                ig_margin_factor: market.marginFactor || 0.1
            };
        } catch (error) {
            log(LOG_LEVELS.ERROR, 'transformMarketToSymbol', 'Transform error', this.enrichContext({
                symbol: market.epic,
                error: error.message
            }));
            return null;
        }
    }

    async refreshSymbolData(symbol) {
        const startTime = process.hrtime.bigint();

        try {
            const result = await this.igApiClient.makeRequest('GET', `markets/${symbol}`, {}, 2);

            if (result.code === 200 && result.response) {
                const transformedSymbol = this.transformMarketToSymbol(result.response);
                
                if (transformedSymbol) {
                    // Update cache for this symbol
                    const cacheKey = `symbol_${symbol}`;
                    this.cache.set(cacheKey, transformedSymbol);

                    // Update database if handler available
                    if (this.dbHandler) {
                        await this.dbHandler.updateSymbol(transformedSymbol);
                    }

                    log(LOG_LEVELS.INFO, 'refreshSymbolData', 'Symbol refreshed', this.enrichContext({
                        symbol,
                        duration_ms: Number(process.hrtime.bigint() - startTime) / 1e6
                    }));

                    return transformedSymbol;
                }
            }

            throw new Error('Invalid market data received');

        } catch (error) {
            log(LOG_LEVELS.ERROR, 'refreshSymbolData', 'Refresh error', this.enrichContext({
                symbol,
                error: error.message
            }));
            throw error;
        }
    }
}

module.exports = IGSymbolFetcher;