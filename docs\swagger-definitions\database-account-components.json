{"components": {"schemas": {"AccountSettings": {"type": "object", "properties": {"account_id": {"type": "integer", "description": "Eindeutige ID des Kontos", "example": 1}, "refId": {"type": "string", "description": "Referenz-ID des Kontos", "example": "IG-D1"}, "account_name": {"type": "string", "description": "Name des Kontos", "example": "Demo Account 1"}, "account_type": {"type": "string", "description": "Typ des Kontos", "example": "DEMO"}, "account_currency": {"type": "string", "description": "Währung des Kontos", "example": "EUR"}, "account_balance": {"type": "number", "format": "float", "description": "Aktueller Kontostand", "example": 10000.0}, "account_leverage": {"type": "integer", "description": "<PERSON><PERSON>", "example": 30}, "account_status": {"type": "string", "description": "Status des Kontos", "example": "ACTIVE"}}}, "TradingMode": {"type": "object", "properties": {"account_id": {"type": "integer", "description": "Eindeutige ID des Kontos", "example": 1}, "refId": {"type": "string", "description": "Referenz-ID des Kontos", "example": "IG-D1"}, "trading_mode": {"type": "string", "description": "Aktueller Trading-Modus", "example": "AUTOMATIC"}, "trading_mode_since": {"type": "string", "format": "date-time", "description": "Zeitpunkt, seit dem der aktuelle Trading-Modus aktiv ist", "example": "2023-04-15T10:30:00Z"}, "trading_mode_reason": {"type": "string", "description": "Grund für den aktuellen Trading-Modus", "example": "User setting"}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Parameter 'refID' is required"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getAccountSettings"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Parameter 'refID' is required", "function": "getAccountSettings"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "No account found", "function": "getAccountSettings"}}}}}}}