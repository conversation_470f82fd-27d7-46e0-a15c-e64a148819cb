{"paths": {"/api/v1/db/symbols/pip_values": {"get": {"summary": "Symbole mit Pip-Werten abrufen", "description": "Ruft alle verwendeten Trading-Symbole mit ihren entsprechenden Pip-Werten ab.\n\nTechnische Details:\n- Cached-Antwort (TTL: 5 Minuten)\n- Enthält nur Symbole, die in der Symbol-Setup-Tabelle verwendet werden\n- Enthält nur Symbole mit definierten Pip-Werten\n- Pip-Werte sind in EUR pro Lot angegeben\n\nAnwendungsfälle:\n- Berechnung von Gewinn/Verlust in EUR\n- Risikomanagement und Position-Sizing\n- Berechnung von Margin-Anforderungen\n- Anzeige von Pip-Werten im Trading-Dashboard", "tags": ["Symbol Management"], "responses": {"200": {"description": "Erfolgreiche Abfrage der Symbole mit Pip-Werten", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SymbolPipValue"}}, "example": [{"symbol": "EURUSD", "pipValuePerLotInProfitEUR": 10.0}, {"symbol": "GBPUSD", "pipValuePerLotInProfitEUR": 12.5}, {"symbol": "USDJPY", "pipValuePerLotInProfitEUR": 8.2}, {"symbol": "DAX", "pipValuePerLotInProfitEUR": 25.0}]}}}, "500": {"$ref": "#/components/responses/DatabaseError"}}}}}}