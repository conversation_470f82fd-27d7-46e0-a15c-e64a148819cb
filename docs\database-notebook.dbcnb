cells:
  - kind: 2
    languageId: sql
    value: "SELECT p.targetRefID,\r

      \                          DATE_ADD(p.log_timestamp, INTERVAL 2 HOUR) AS
      log_timestamp,\r

      \                         p.log_order2,\r

      \                         p.log_order,\r

      \                         p.profit,\r

      \                         p.status,\r

      \                         p.sl,\r

      \                         p.tp,\r

      \                         h.symbol,                         \r

      \                         h.cmd,\r

      \                         h.comment,\r

      \                         h.entry_time,\r

      \                         h.exit_time,\r

      \                         h.strategy,\r

      \                         h.manual_stop\r

      \                  FROM mirror_trading_logs_profit p,\r

      \                       trades_history h\r

      \                  WHERE p.targetRefID = 'IG-D1'\r

      \                    and p.log_order=h.source_xtb_orderID \r

      \                    AND p.log_timestamp between DATE_SUB(CURDATE(),
      INTERVAL 1 DAY) AND DATE_SUB(CURDATE(), INTERVAL 0 DAY)\r

      \                  ORDER BY p.log_order2, p.log_timestamp"
    metadata: {}
metadata:
  conn:
    id: qY9KiLEJSpi_lAKsa7oWg
    name: dba_xtbbridge
  database: dbxtbbridge
  schema: ""
