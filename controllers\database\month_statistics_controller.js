const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam, validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');

async function getMonthStatisticsIndependent(refID) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getMonthStatisticsIndependent', 'Starting month statistics retrieval', {
            refID
        });

        refID = validateStringParam(refID, {
            required: true,
            paramName: 'refID'
        });

        const query = {
            sql: 'SELECT YEAR(t.exit_time) as year, MONTH(t.exit_time) as month, round(min(profit),0) AS min_profit, round(max(profit),0) AS max_profit, COUNT(*) AS cnt_trades, round(SUM(profit),0) AS sum_profit \
             FROM `trades_history` t, `accounts` a \
             WHERE t.account=a.account_id \
              AND t.exit_time>=DATE_SUB(NOW(), INTERVAL 3 Month) \
              AND a.refId=? \
            GROUP BY MONTH(t.exit_time) \
             ORDER BY year,month',
            bigIntAsNumber: true
        };

        const result = await executeQuery(query, [refID]);

        log(LOG_LEVELS.INFO, 'getMonthStatisticsIndependent', 'Successfully retrieved month statistics', {
            refID,
            resultCount: result?.length || 0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getMonthStatisticsIndependent', 'Failed to fetch month statistics', {
            error: err.message,
            stack: err.stack,
            refID
        });
        throw err;
    } finally {
        logPerformance('getMonthStatisticsIndependent', startTime);
    }
}

async function getMonthStatistics(req, res) {
    try {
        const result = await withCacheWrapper(
            'GENERAL',
            'getMonthStatistics',
            () => getMonthStatisticsIndependent(req.query.refID),
            [req.query.refID]
        );
        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getMonthStatistics');
        res.status(errorResponse.status).json(errorResponse);
    }
}

async function getCurrentMonthStatisticsIndependent(refID) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getCurrentMonthStatisticsIndependent', 'Starting current month statistics retrieval', {
            refID
        });

        refID = validateStringParam(refID, {
            required: true,
            paramName: 'refID'
        });

        const query = {
            sql: 'SELECT YEAR(t.exit_time) AS year, month(t.exit_time) AS month, ROUND(MIN(profit),0) AS min_profit, ROUND(MAX(profit),0) AS max_profit, COUNT(*) AS cnt_trades, ROUND(SUM(profit),0) AS sum_profit\
                FROM `trades_history` t, `accounts` a \
                WHERE t.account=a.account_id AND a.refId=? \
                  AND month(t.exit_time)=month(NOW()) \
                  AND year(t.exit_time)=year(NOW()) \
                ORDER BY YEAR DESC, month DESC',
            bigIntAsNumber: true
        };

        const result = await executeQuery(query, [refID]);

        log(LOG_LEVELS.INFO, 'getCurrentMonthStatisticsIndependent', 'Successfully retrieved current month statistics', {
            refID,
            resultCount: result?.length || 0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getCurrentMonthStatisticsIndependent', 'Failed to fetch current month statistics', {
            error: err.message,
            stack: err.stack,
            refID
        });
        throw err;
    } finally {
        logPerformance('getCurrentMonthStatisticsIndependent', startTime);
    }
}

async function getCurrentMonthStatistics(req, res) {
    try {
        const result = await withCacheWrapper(
            'GENERAL',
            'getCurrentMonthStatistics',
            () => getCurrentMonthStatisticsIndependent(req.query.refID),
            [req.query.refID]
        );
        res.send(result);
    } catch (err) {
        const errorResponse = errorHandler(err, 'getCurrentMonthStatistics');
        res.status(errorResponse.status).json(errorResponse);
    }
}

module.exports = {
    getMonthStatistics,
    getMonthStatisticsIndependent,
    getCurrentMonthStatistics,
    getCurrentMonthStatisticsIndependent
};
