CREATE TABLE IF NOT EXISTS `ai_requests` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `request_id` VARCHAR(36) NOT NULL,
  `timestamp` DATETIME NOT NULL,
  `llm_model` VARCHAR(50) NOT NULL,
  `prompt_text` LONGTEXT,
  `response_text` <PERSON>ONG<PERSON><PERSON>,
  `processing_time_ms` INT UNSIGNED,
  `status` ENUM('success', 'error') NOT NULL,
  `error_message` TEXT,
  `assistant_id` VARCHAR(100),
  `prompt_id` VARCHAR(100),
  `prompt_version` INT,
  `token_count` INT UNSIGNED,
  `token_input_count` INT UNSIGNED,
  `token_output_count` INT UNSIGNED,
  INDEX `idx_request_id` (`request_id`),
  INDEX `idx_timestamp` (`timestamp`),
  INDEX `idx_llm_model` (`llm_model`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;