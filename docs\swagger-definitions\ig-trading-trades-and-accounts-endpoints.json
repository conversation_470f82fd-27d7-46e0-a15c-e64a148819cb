{"paths": {"/api/v1/ig/trades": {"get": {"summary": "Aktuelle offene Trades abrufen", "description": "Ruft alle aktuell offenen Trades von der IG Markets-Plattform ab.\n\nTechnische Details:\n- Gecachte Antworten (kurze TTL)\n- Enthält Pip-Wert-Berechnungen\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Portfolio-Überwachung\n- Positionsmanagement\n- Risikobewertung\n- Gewinn-/Verlustüberwachung", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto", "example": "IG-P1"}], "responses": {"200": {"description": "Offene Trades erfolgreich abgerufen", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IGTrade"}}}}}, "500": {"$ref": "#/components/responses/IGError"}}}}, "/api/v1/ig/trades/history": {"get": {"summary": "Handelshistorie abrufen", "description": "Ruft historische Handelstransaktionen von der IG Markets-Plattform ab.\n\nTechnische Details:\n- Gecachte Antworten (mittlere TTL)\n- Unterstützt Datumsbereichsfilterung\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Handelsanalyse\n- Leistungsüberwachung\n- Steuerberichterstattung\n- Strategieoptimierung", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}, {"name": "from", "in": "query", "schema": {"type": "string", "format": "date"}, "description": "Startdatum für die Handelshistorie (Format: YYYY-MM-DD)", "example": "2025-04-20"}, {"name": "to", "in": "query", "schema": {"type": "string", "format": "date"}, "description": "Enddatum für die Handelshistorie (Format: YYYY-MM-DD)", "example": "2025-04-21"}, {"name": "symbol", "in": "query", "schema": {"type": "string"}, "description": "Filtern nach Handelssymbol", "example": "CS.D.EURUSD.MINI.IP"}, {"name": "minus_days", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "Anzahl der Tage in der Vergangenheit, wenn kein Startdatum angegeben ist"}], "responses": {"200": {"description": "Handelshistorie erfolgreich abgerufen", "content": {"application/json": {"schema": {"type": "object", "properties": {"activities": {"type": "array", "items": {"$ref": "#/components/schemas/IGTradeHistory"}}}}}}}, "500": {"$ref": "#/components/responses/IGError"}}}}, "/api/v1/ig/accounts": {"get": {"summary": "Kontoinformationen abrufen", "description": "Ruft Kontoinformationen von der IG Markets-Plattform ab.\n\nTechnische Details:\n- Gecachte Antworten (kurze TTL)\n- Enthält Kontostand, verfügbare Mittel und Margin-Informationen\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Kontoüberwachung\n- Risikomanagement\n- Kapitalallokation", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}], "responses": {"200": {"description": "Kontoinformationen erfolgreich abgerufen", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IGAccount"}}}}, "500": {"$ref": "#/components/responses/IGError"}}}}, "/api/v1/ig/trades/margin-trade": {"get": {"summary": "Margin-Anforderungen berechnen", "description": "Berechnet die Margin-Anforderungen für einen potenziellen Trade.\n\nTechnische Details:\n- Gecachte Antworten (mittlere TTL)\n- Berücksichtigt Instrumentenspezifikationen und aktuelle Marktpreise\n- Erfordert gültige IG Markets-Anmeldedaten\n\nAnwendungsfälle:\n- Handelsplanung\n- Risikomanagement\n- Kapitalallokation", "tags": ["IG Trading"], "parameters": [{"name": "refID", "in": "query", "schema": {"type": "string", "default": "IG-P1"}, "description": "Referenz-ID für das zu verwendende IG-Konto"}, {"name": "symbol", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Handelssymbol, für das die Margin berechnet werden soll", "example": "CS.D.EURUSD.MINI.IP"}, {"name": "direction", "in": "query", "required": true, "schema": {"type": "string", "enum": ["BUY", "SELL"]}, "description": "Handelsrichtung (BUY oder SELL)"}, {"name": "size", "in": "query", "schema": {"type": "number", "format": "float", "default": 1}, "description": "Handelsvolumen für die Margin-Berechnung", "example": 2.0}], "responses": {"200": {"description": "Margin-Anforderungen erfolgreich berechnet", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IGMarginCalculation"}}}}, "500": {"$ref": "#/components/responses/IGError"}}}}}}