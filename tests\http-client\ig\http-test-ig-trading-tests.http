@demo-epic = CS.D.EURUSD.MINI.IP
@volume = 0.5
@sl_points = 50
@tp_points = 100

### Execute: Buy-Trade (IG-Direct-Market-Order)
POST {{API_BASE_URL}}/api/v1/ig/trades/execute?refID=IG-D1
Content-Type: application/json

{
    "epic": "{{demo-epic}}",
    "direction": "BUY",
    "size": "{{volume}}",
    "expiry": "-",
    "orderType": "MARKET",
    "timeInForce": "FILL_OR_KILL",
    "guaranteedStop": "false",
    "forceOpen": "true",
    "stopLevel": "1.0850",
    "dealReference": "TEST-BUY-1",
    "currencyCode": "EUR"
}

> {%
    client.test("Trade Execution Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Trade execution failed");
        
        if(response.body.dealId) {
            client.global.set("dealId", response.body.dealId);
        }
    });

    client.test("Trade Response contains required fields", function() {
        client.assert(response.body.hasOwnProperty('dealReference'), "Missing deal reference");
        client.assert(response.body.hasOwnProperty('dealId'), "Missing deal ID");
        client.assert(response.body.hasOwnProperty('status'), "Missing status");
    });
%}

### Execute: Legacy-Trade-Format (XTB)
POST {{API_BASE_URL}}/api/v1/ig/trades/bridge?refID=IG-D1
Content-Type: application/json
{
    "tradeTransInfo": {
        "cmd": 0,
        "symbol": "IX.D.DAX.IGN.IP",
        "volume": 1,
        "sl": 18000
    },
    "simulation": true,
    "strategy": "1-2-3-strike",
    "timeframe": "15"
}

> {%
    client.test("Legacy Trade Execution successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Legacy trade execution failed");
    });

    client.test("Legacy Trade Response format correct", function() {
        client.assert(response.body.hasOwnProperty('dealReference'), "Missing deal reference");
        client.assert(response.body.hasOwnProperty('dealId'), "Missing deal ID");
    });
%}

### GET: Trade-Status for Deal-ID
@dealReference = DIAAAAS8T2GWKBA
POST {{API_BASE_URL}}/api/v1/ig/trades/status
Content-Type: application/json

{
    "dealReference": "{{dealReference}}"
}

> {%
    client.test("Trade Status Request successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
    });

    client.test("Trade Status contains required fields", function() {
        client.assert(response.body.hasOwnProperty('status'), "Missing deal ID");
        client.assert(response.body.hasOwnProperty('dealReference'), "Missing deal status");
    });
%}

### POST: Modify-Position
POST {{API_BASE_URL}}/api/v1/ig/trades/modify?refID=IG-D1
Content-Type: application/json

{
    "dealId": "DIAAAATK67GVSAE",
    "stopLevel": "6237",
    "profit": 1
}

> {%
    client.test("Position Modification successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Position modification failed");
    });
%}

### POST: Close-Position
POST {{API_BASE_URL}}/api/v1/ig/trades/close?refID=IG-D1
Content-Type: application/json

{
    "dealId": "DIAAAATK67GVSAE",
    "direction": "SELL",
    "epic": "IX.D.DAX.IGN.IP",
    "size": "1",
    "orderType": "MARKET",
    "dealId": "DIAAAATK67GVSAE",
    "profit":  10   
}

> {%
    client.test("Position Close successful", function() {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === "success", "Position close failed");
    });
%}
