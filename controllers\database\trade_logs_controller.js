const { DatabaseError, ValidationError, errorHandler } = require('./errors/database_errors');
const { with<PERSON><PERSON><PERSON><PERSON><PERSON>, CACHE_PREFIX } = require('../../services/cache_service');
const { TIME_CONSTANTS, QUERY_LIMITS } = require('../../configs/constants');
const { log, LOG_LEVELS, logPerformance } = require('../../services/logging_service');
const { validateNumericParam, validateStringParam } = require('../../services/validation_service');
const { executeQuery } = require('../../services/database_service');
const { buildTradeLogsQuery } = require('./queries/trade_logs_queries');

async function getTradeLogsIndependent(refID, refID_alternate = "-", limit = 400, days = 365, reducemessage = false) {
    const startTime = process.hrtime.bigint();
    try {
        log(LOG_LEVELS.INFO, 'getTradeLogsIndependent', 'Starting trade logs retrieval', {
            refID,
            refID_alternate,
            limit,
            days,
            reducemessage
        });

        // Validate refID
        refID = validateStringParam(refID, {
            required: true,
            paramName: 'refID'
        });

        // Validate alternate refID
        refID_alternate = validateStringParam(refID_alternate, {
            defaultValue: "-",
            paramName: 'refID_alternate'
        });

        // Validate limit
        limit = validateNumericParam(limit, {
            defaultValue: QUERY_LIMITS.DEFAULT_TRADE_LOGS,
            min: 1,
            max: QUERY_LIMITS.MAX_TRADES,
            paramName: 'limit'
        });

        // Validate days
        days = validateNumericParam(days, {
            defaultValue: TIME_CONSTANTS.DEFAULT_DAYS.TRADE_LOGS,
            min: 1,
            max: 365,
            paramName: 'days'
        });

        // Validate reducemessage
        reducemessage = typeof reducemessage === 'boolean' ? reducemessage : false;

        log(LOG_LEVELS.DEBUG, 'getTradeLogsIndependent', 'Executing query', {
            refID,
            refID_alternate,
            days,
            limit,
            reducemessage
        });

        const query = buildTradeLogsQuery(reducemessage);
        const result = await executeQuery(query, [refID, refID_alternate, days, limit]);
        
        log(LOG_LEVELS.INFO, 'getTradeLogsIndependent', 'Successfully retrieved trade logs', {
            refID,
            refID_alternate,
            resultCount: result?result.length:0
        });

        return result;
    } catch (err) {
        log(LOG_LEVELS.ERROR, 'getTradeLogsIndependent', 'Failed to fetch trade logs', {
            error: err.message,
            stack: err.stack,
            refID,
            refID_alternate,
            days,
            limit
        });
        throw err;
    } finally {
        logPerformance('getTradeLogsIndependent', startTime);
    }
}

/**
 * API-Endpunkt zum Abrufen von Trade-Logs
 */
async function getTradeLogs(req, res) {
    try {
        // Validiere Parameter vor dem Cache-Wrapper
        const p_limit = validateNumericParam(
            req.query.limit ? parseInt(req.query.limit) : undefined,
            {
                defaultValue: QUERY_LIMITS.DEFAULT_TRADE_LOGS,
                min: 1,
                max: QUERY_LIMITS.MAX_TRADES,
                paramName: 'limit'
            }
        );

        const p_refID = validateStringParam(req.query.refID, {
            required: true,
            paramName: 'refID'
        });

        const p_refID_alternate = validateStringParam(req.query.refID_alternate, {
            defaultValue: "-",
            paramName: 'refID_alternate'
        });

        let p_days;
        if (req.query.days !== undefined) {
            const parsedDays = parseInt(req.query.days);
            if (isNaN(parsedDays) || parsedDays < 0) {
                throw new ValidationError('days must be a non-negative number');
            }
            // Wenn days=0, setzen wir es auf 1 um mindestens den aktuellen Tag anzuzeigen
            p_days = Math.max(1, parsedDays);
        } else {
            p_days = TIME_CONSTANTS.DEFAULT_DAYS.TRADE_LOGS;
        }

        if (p_days > 365) {
            throw new ValidationError('days cannot exceed 365');
        }

        const p_reducemessage = req.query.reducemessage === 'true';

        const result = await withCacheWrapper(
            'GENERAL',
            'getTradeLogs',
            () => getTradeLogsIndependent(p_refID, p_refID_alternate, p_limit, p_days, p_reducemessage),
            [p_refID, p_refID_alternate, p_limit, p_days, p_reducemessage]
        );

        res.send(result);
    } catch (err) {
        if (!res.headersSent) {
            const errorResponse = errorHandler(err, 'getTradeLogs');
            res.status(errorResponse.status).json(errorResponse);
        }
    }
}

module.exports = {
    getTradeLogs,
    getTradeLogsIndependent
};
