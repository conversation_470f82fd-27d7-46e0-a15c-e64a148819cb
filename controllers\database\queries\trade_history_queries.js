const { TIME_CONSTANTS } = require('../../../configs/constants');

/**
 * SQL-Queries für Trade History Funktionen
 */

const buildTradeHistoryQuery = (symbol) => {
    return buildFilteredTradeHistoryQuery(symbol, false);
};

const buildTradeHistoryEquityQuery = () => {
    return {
        sql: `SELECT exit_time,
                     ROUND(@running_total := @running_total - profit, 1) AS equity, 
                     profit AS day_profit
              FROM \`trades_sum\`,
                   (SELECT @running_total := ?) rt
              WHERE refID = ?  
                AND exit_time > DATE_SUB(CURDATE(), INTERVAL 365 DAY)
              ORDER BY exit_time desc`,
        bigIntAsNumber: true
    };
};

const buildTradeHistoryStopLevelQuery = (symbol) => {
    const baseQuery= `
        SELECT X.*, t.strategy, t.timeframe, t.symbol, t.cmd, t.\`create\`, t.price, t.trade_volume
        FROM mirror_trading_logs_profit x,
             trades t
        WHERE x.targetRefID = ? AND x.log_timestamp > DATE_SUB(CURDATE(), INTERVAL ? DAY)
          AND x.log_order2=t.xtb_orderid
          AND status in ('MODIFIED','SL-MOD-EXECUTED')`;

    return symbol === undefined
       ? `${baseQuery} ORDER BY x.log_timestamp DESC LIMIT 5000`
       : `${baseQuery} AND t.symbol=? ORDER BY x.log_timestamp DESC LIMIT 5000`;  
};

const buildFilteredTradeHistoryQuery = (symbol, isStopLevel = false) => {
    const baseQuery = `
        SELECT x.*, a.*, 
               t.activate_ig_d1, t.activate_ig_p1, t.activate_break_even_stop, 
               t.activate_high_volume, t.activate_trailing_stops, t.activate_initial_stop_multiple,
               b.backtest_netwin_percentage, b.backtest_hitrate, b.backtest_profit_factor, 
               b.backtest_maxdrawdown_percentage, b.backtest_trades, b.backtest_snapshot_time, 
               b.notes, b.backtest_sharpeRatio
        FROM \`trades_history\` x
        JOIN accounts a ON x.refID = a.refId
        LEFT JOIN \`strategy_toggle\` t ON t.strategy = x.strategy AND t.timeframe = x.timeframe AND t.symbol = x.symbol
        LEFT JOIN strategy_backtests b ON t.strategy = b.strategy AND t.timeframe = b.timeframe AND t.symbol = b.symbol
        WHERE a.refId = ? AND exit_time > DATE_SUB(CURDATE(), INTERVAL ? DAY)
    `;

    return symbol === undefined
        ? `${baseQuery} ORDER BY exit_time DESC, entry_time DESC LIMIT 5000`
        : `${baseQuery} AND x.symbol = ? ORDER BY exit_time DESC, entry_time DESC LIMIT 5000`;
};

module.exports = {
    buildTradeHistoryQuery,
    buildTradeHistoryStopLevelQuery,
    buildTradeHistoryEquityQuery
};
