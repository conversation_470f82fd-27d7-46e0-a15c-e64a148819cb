###

// @name ai_prompt: Nvidia's AI system ACE mit Llama 3.3
POST {{API_BASE_URL}}/api/v1/ai/prompt?promptID=n8n.mail.summary&llmModel=llama-3.3-70b-versatile
Content-Type: text/plain

{
  "TEXT_TO_ANALYZE": "Nvidia's AI system ACE will power in-game character interactions in Mecha Break, a mech battle game launching in 2025. "
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}



###

// @name ai_prompt: Nvidia's AI system ACE mit claude-3-5
POST {{API_BASE_URL}}/api/v1/ai/prompt?promptID=n8n.mail.summary&llmModel=claude-3-5-haiku-20241022
Content-Type: text/plain

{
  "TEXT_TO_ANALYZE": "Nvidia's AI system ACE will power in-game character interactions in Mecha Break, a mech battle game launching in 2025. "
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name ai_prompt: Nvidia's AI system ACE mit gpt-4o-mini
POST {{API_BASE_URL}}/api/v1/ai/prompt?promptID=n8n.mail.summary&llmModel=gpt-4o-mini
Content-Type: text/plain

{
  "TEXT_TO_ANALYZE": "Nvidia's AI system ACE will power in-game character interactions in Mecha Break, a mech battle game launching in 2025. "
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name ai_prompt: Nvidia's AI system ACE mit mistral-large-latest
// siehe https://docs.mistral.ai/getting-started/models/models_overview/#tag/batch/operation/jobs_api_routes_batch_cancel_batch_job
POST {{API_BASE_URL}}/api/v1/ai/prompt?promptID=n8n.mail.summary&llmModel=mistral-large-latest
Content-Type: text/plain

{
  "TEXT_TO_ANALYZE": "Nvidia's AI system ACE will power in-game character interactions in Mecha Break, a mech battle game launching in 2025. "
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}
###

// @name ai_prompt: Nvidia's AI system ACE mit gemini-2.5-pro-exp-03-25
// siehe https://docs.mistral.ai/getting-started/models/models_overview/#tag/batch/operation/jobs_api_routes_batch_cancel_batch_job
POST {{API_BASE_URL}}/api/v1/ai/prompt?promptID=n8n.mail.summary&llmModel=gemini-2.5-pro-exp-03-25
Content-Type: text/plain

{
  "TEXT_TO_ANALYZE": "Nvidia's AI system ACE will power in-game character interactions in Mecha Break, a mech battle game launching in 2025. "
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name ai_prompt: Nvidia's AI system ACE mit gemini-2.0-flash
// siehe https://docs.mistral.ai/getting-started/models/models_overview/#tag/batch/operation/jobs_api_routes_batch_cancel_batch_job
POST {{API_BASE_URL}}/api/v1/ai/prompt?promptID=n8n.mail.summary&llmModel=gemini-2.0-flash
Content-Type: text/plain

{
  "TEXT_TO_ANALYZE": "Nvidia's AI system ACE will power in-game character interactions in Mecha Break, a mech battle game launching in 2025. "
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name ai_prompt: n8n.mail.relevancescore
POST {{API_BASE_URL}}/api/v1/ai/prompt?promptID=n8n.mail.relevancescore
Content-Type: text/plain

{
  "GEWICHTUNGSFAKTOREN": {
    "Wirtschaftsdaten": {
      "BIP": 5,
      "Arbeitslosenzahlen": 4,
      "Inflationsrate": 3
    },
    "Unternehmensberichte": {
      "Quartalszahlen": 5,
      "Gewinnwarnungen": 4,
      "Wichtige Unternehmensnachrichten": 3
    },
    "Zentralbankentscheidungen": {
      "Zinssätze": 5,
      "Geldpolitische Maßnahmen": 4
    },
    "Geopolitische Ereignisse": {
      "Wahlen": 4,
      "Internationale Konflikte": 3,
      "Handelsabkommen": 2
    },
    "Branchenspezifische Nachrichten": {
      "Technologische Durchbrüche": 5,
      "Regulatorische Änderungen": 4
    },
    "Analystenbewertungen und -empfehlungen": 3,
    "Marktstimmungen und -trends": {
      "Allgemeine Marktbewegungen": 3,
      "Technische Analysen": 2
    },
    "Technische Entwicklungen rund um KI": {
      "Neue KI-Tools": 5,
      "Fortschritte in der KI-Forschung": 4
    },
    "Gerüchte und Spekulationen": {
      "Kurzfristige Marktbewegungen": 2,
      "Langfristige Bedeutung": 1
    }
  },
  "ABSENDER_UND_BETREFF": "From: =?UTF-8?B?REVSIFNQSUVHRUwg4oCTIERpZSBMYWdlIGFtIFNvbm50YWc=?=\r\n <<EMAIL>>",
  "INHALT": "@Channel *Aktuelle politische und gesellschaftliche Entwicklungen in Deutschland*\n\n*Bundesinnenministerin plant Verschärfung des Waffenrechts*\n\nBundesinnenministerin Nancy Faeser (SPD) plant eine Verschärfung des Waffenrechts in Deutschland. Konkret soll das Tragen von Messern mit einer Klingenlänge über 6 cm in der Öffentlichkeit verboten werden. Zudem ist ein komplettes Verbot von Springmessern vorgesehen. \n\nDie Ministerin begründet diesen Schritt mit der Notwendigkeit, die öffentliche Sicherheit zu erhöhen und potenzielle Gewalttaten zu verhindern. Insbesondere bei spontan ausbrechenden Konflikten könne das Vorhandensein von Messern zu einer gefährlichen Eskalation führen.\n\nDie geplante Gesetzesänderung zielt darauf ab, die Hemmschwelle für den Einsatz von Messern als Waffe zu erhöhen. Kritiker sehen darin jedoch eine übermäßige Einschränkung der persönlichen Freiheit. Befürworter argumentieren, dass die Maßnahme Leben retten und die Sicherheit im öffentlichen Raum verbessern könne.\n\nDiese Entwicklung reiht sich ein in eine breitere Debatte über den Umgang mit Waffen und die Balance zwischen Sicherheit und persönlicher Freiheit in Deutschland. Für Anleger könnte dies Auswirkungen auf Unternehmen haben, die im Bereich der Sicherheitstechnologie oder der Herstellung von Messern und anderen potenziell betroffenen Produkten tätig sind.\n\nDetails: https://www.bundesregierung.de/breg-de/aktuelles/faeser-waffenrecht-2162106\n\n*Radikalisierung in der Politik - Warnung vor neuem Faschismus*\n\nDer SPIEGEL warnt in seiner aktuellen Ausgabe vor einer möglichen Entwicklung hin zu einem \"neuen Faschismus\" in verschiedenen Ländern. Dabei werden namentlich die Politiker Björn Höcke (AfD, Deutschland), Marine Le Pen (Rassemblement National, Frankreich) und Donald Trump (Republikaner, USA) genannt.\n\nDie Titelseite des Magazins lehnt sich bewusst an die faschistische Ästhetik der 1930er Jahre an, um auf Parallelen und potenzielle Gefahren aufmerksam zu machen. Die Redaktion betont, dass es unterschiedliche Meinungen darüber geben kann, ob die genannten Politiker tatsächlich als Faschisten zu bezeichnen sind. Jedoch sieht man deutliche Anzeichen für Entwicklungen, die in einen neuen Faschismus münden könnten.\n\nDiese Warnung spiegelt die wachsende Besorgnis über populistische und extreme politische Strömungen in westlichen Demokratien wider. Für Investoren könnte dies Auswirkungen auf die politische Stabilität und wirtschaftliche Rahmenbedingungen in den betroffenen Ländern haben.\n\nDie Diskussion um diese Thematik zeigt auch die zunehmende Polarisierung der politischen Landschaft, was wiederum Einfluss auf Wahlausgänge und politische Entscheidungen haben kann. Anleger sollten diese Entwicklungen aufmerksam verfolgen, da sie potenziell weitreichende Folgen für Märkte und Investitionen haben könnten.\n\nDetails: https://www.spiegel.de/politik/deutschland/\n\n*Debatte über US-Mittelstreckenwaffen in Deutschland*\n\nIn Deutschland wird aktuell eine Debatte über die mögliche Stationierung neuer US-amerikanischer Mittelstreckenwaffen geführt. Der SPD-Fraktionsvorsitzende Rolf Mützenich hat dieses Thema angesprochen und kritisiert, dass die Politik eine offene Diskussion darüber scheut.\n\nDie Debatte steht im Kontext der sogenannten \"Zeitenwende\" in der deutschen Sicherheits- und Verteidigungspolitik, die als Reaktion auf den russischen Angriffskrieg gegen die Ukraine ausgerufen wurde. Es geht um die Frage, wie Deutschland seine Verteidigungsfähigkeit stärken und gleichzeitig seine Bündnisverpflichtungen innerhalb der NATO erfüllen kann.\n\nDie Stationierung von US-Mittelstreckenwaffen wäre ein hochsensibles Thema, das an die Nachrüstungsdebatte der 1980er Jahre erinnert. Es berührt Fragen der nationalen Souveränität, des Verhältnisses zu den USA und Russland sowie der nuklearen Abschreckung.\n\nFür Investoren könnte diese Diskussion Auswirkungen auf den Verteidigungssektor haben. Eine mögliche Aufrüstung könnte zu erhöhten Investitionen in Rüstungsunternehmen und Technologiefirmen im Bereich der Verteidigung führen. Gleichzeitig könnten geopolitische Spannungen die allgemeine Marktstabilität beeinflussen.\n\nDetails: https://www.bundestag.de/dokumente/textarchiv/2022/kw22-de-zeitenwende-895705\n\n*Wahlkampf in Ostdeutschland: Kritik an unrealistischen Versprechungen*\n\nIn den ostdeutschen Bundesländern Sachsen, Thüringen und Brandenburg stehen 2024 Landtagswahlen an. Der sich anbahnende Wahlkampf wird bereits jetzt kritisch beobachtet. Politiker verschiedener Parteien werden beschuldigt, unrealistische Versprechen zu machen und die tatsächlichen Probleme der Bürger zu ignorieren.\n\nDie Kritik richtet sich gegen eine Art \"Friedenskundgebungs-Mentalität\" im Wahlkampf, bei der Politiker Forderungen stellen und Versprechungen machen, die sie realistischerweise nicht einhalten können. Dies wird als Versuch gewertet, die Wähler für dumm zu verkaufen und von den eigentlichen Herausforderungen abzulenken.\n\nDiese Entwicklung spiegelt die komplexe politische Landschaft in Ostdeutschland wider, wo Parteien wie die AfD in Umfragen stark abschneiden und etablierte Parteien um Wählerstimmen kämpfen. Die Situation wird durch wirtschaftliche Herausforderungen, demografischen Wandel und das Gefühl vieler Ostdeutscher, vom Westen abgehängt zu sein, zusätzlich verschärft.\n\nFür Investoren könnte diese politische Dynamik Auswirkungen auf regionale Wirtschaftspolitik, Infrastrukturinvestitionen und das allgemeine Geschäftsklima in Ostdeutschland haben. Es besteht die Möglichkeit von politischer Instabilität und schwierigen Koalitionsbildungen nach den Wahlen, was wiederum Einfluss auf wirtschaftliche Entscheidungen und Investitionen in der Region haben könnte.\n\nDetails: https://www.tagesschau.de/inland/innenpolitik/landtagswahlen-2024-100.html"
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}



###


// @name ai_prompt: n8n.mail.classification
POST {{API_BASE_URL}}/api/v1/ai/prompt?promptID=n8n.mail.classification
Content-Type: text/plain

{
  "SENDER_AND_SUBJECT": "From: heise KI-Update <<EMAIL>>",
  "CONTENT": "@Channel *Analyse: Nvidia's Dominanz im KI-Markt und Auswirkungen auf den Technologiesektor*\n\n*Nvidia's Marktführerschaft im KI-Chip-Segment*\n\nNvidia hat sich als klarer Marktführer im Bereich der KI-Chips etabliert und dominiert derzeit den Markt für Grafikprozessoren (GPUs), die für KI-Anwendungen optimiert sind. Das Unternehmen profitiert stark vom aktuellen KI-Boom und der steigenden Nachfrage nach leistungsfähiger Hardware für maschinelles Lernen und künstliche Intelligenz. Nvidia's Technologie wird von führenden Tech-Unternehmen wie Microsoft, Google und Meta für ihre KI-Infrastruktur ei..."
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}



###

// @name ai_prompt: ki-algobot.llm-news-aggregation
POST {{API_BASE_URL}}/api/v1/ai/prompt?promptTitle=ki-algobot.llm-news-aggregation
Content-Type: text/plain

"This is a test message for the Langfuse prompt request."

> {%
    client.test("Langfuse Prompt Request executed successfully", function() {
        client.log("#Test: Langfuse Prompt Request");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###
