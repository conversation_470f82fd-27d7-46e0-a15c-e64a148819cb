###

// @name strategy/activation_report_aggregated
GET {{API_BASE_URL}}/api/v1/db/strategies/report_aggregated?weeks=1&useStatsWithLastStrategyChance=before&ignoreInActiveState=true
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name strategy/activation_report_aggregated
GET {{API_BASE_URL}}/api/v1/db/strategies/last_events
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name strategy/activation_report_aggregated
GET {{API_BASE_URL}}/api/v1/db/strategies/report_aggregated?weeks=1&useStatsWithLastStrategyChance=after
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name strategy/activation_report_aggregated
GET {{API_BASE_URL}}/api/v1/db/strategies/documentation
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name strategies
GET {{API_BASE_URL}}/api/v1/db/strategies/report?startdate=2023-01-01&refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}


###

// @name strategies
GET {{API_BASE_URL}}/api/v1/db/strategies/report?weeks=1&refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name strategies
GET {{API_BASE_URL}}/api/v1/db/strategies/report?months=1&refID=IG-P1
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

// @name strategies
GET {{API_BASE_URL}}/api/v1/db/strategies/report?months=1&refID=IG-P1&group_by_day=true
Content-Type: application/json

> {%
    client.test("Request executed successfully", function() {
        client.log("#Basic-Test-Run: BUY-Position");
        client.log("- '"+response.body["preparations"]+"'");
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###
