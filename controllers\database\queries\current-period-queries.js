/**
 * SQL Queries für aktuelle Perioden (Tag/Woche/Monat/Jahr)
 */
const CurrentPeriodQueries = {
    currentDay: {
        sql: 'SELECT YEAR(t.exit_time) AS YEAR, MONTH(t.exit_time) AS MONTH, DATE(t.exit_time) AS DATE, ROUND(MIN(profit),0) AS min_profit, ROUND(MAX(profit),0) AS max_profit, COUNT(*) AS cnt_trades, ROUND(SUM(profit),0) AS sum_profit\
                FROM `trades_history` t, `accounts` a, (SELECT ACCOUNT, DATE(MAX(exit_time)) AS maxdate FROM `trades_history` GROUP BY account) md \
               WHERE t.account=a.account_id AND a.refId=? AND DATE(t.exit_time)=md.maxdate AND md.account=a.account_id',
        bigIntAsNumber: true
    },

    currentWeek: {
        sql: 'SELECT YEAR(t.exit_time) AS year, WEEKOFYEAR(t.exit_time) AS week, ROUND(MIN(profit),0) AS min_profit, ROUND(MAX(profit),0) AS max_profit, COUNT(*) AS cnt_trades, ROUND(SUM(profit),0) AS sum_profit\
                FROM `trades_history` t \
                WHERE t.refId=? \
                  AND YEAR(t.exit_time)=YEAR(NOW()) \
                  AND WEEKOFYEAR(t.exit_time)=WEEKOFYEAR(NOW())',
        bigIntAsNumber: true
    },

    currentMonth: {
        sql: 'SELECT YEAR(t.exit_time) AS year, month(t.exit_time) AS month, ROUND(MIN(profit),0) AS min_profit, ROUND(MAX(profit),0) AS max_profit, COUNT(*) AS cnt_trades, ROUND(SUM(profit),0) AS sum_profit\
                FROM `trades_history` t, `accounts` a \
                WHERE t.account=a.account_id AND a.refId=? \
                  AND month(t.exit_time)=month(NOW()) \
                  AND year(t.exit_time)=year(NOW()) \
                ORDER BY YEAR DESC, month DESC',
        bigIntAsNumber: true
    },

    currentYear: {
        sql: 'SELECT YEAR(t.exit_time) AS year, ROUND(MIN(profit),0) AS min_profit, ROUND(MAX(profit),0) AS max_profit, COUNT(*) AS cnt_trades, ROUND(SUM(profit),0) AS sum_profit\
                FROM `trades_history` t, `accounts` a \
                WHERE t.account=a.account_id AND a.refId=? \
                  AND t.exit_time>\'2023-07-01\' \
                  AND year(t.exit_time)=year(NOW())',
        bigIntAsNumber: true
    }
};

module.exports = CurrentPeriodQueries;
