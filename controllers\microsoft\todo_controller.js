const { LoggingService } = require('../../services/logging_service');
const MicrosoftTodoService = require('../../services/microsoft_todo_service');
const {
    ValidationError,
    BusinessLogicError,
    MicrosoftGraphError,
    MicrosoftOAuthError,
    MicrosoftRateLimitError,
    MicrosoftResourceNotFoundError,
    errorHandler
} = require('../database/errors/database_errors');

/**
 * Microsoft ToDo Controller
 * 
 * Handles HTTP requests for Microsoft ToDo integration.
 * Provides endpoints for task creation and management.
 * 
 * Features:
 * - Input validation and sanitization
 * - Service layer integration
 * - Comprehensive error handling
 * - Structured logging
 * - Consistent response formatting
 */
class MicrosoftTodoController {
    constructor() {
        this.logger = LoggingService.getInstance();
        this.todoService = new MicrosoftTodoService(this.logger);
    }

    /**
     * Create a new task in Microsoft ToDo
     * 
     * @swagger
     * /api/v1/microsoft/todo/tasks:
     *   post:
     *     tags: [Microsoft ToDo]
     *     summary: Create a new task in Microsoft ToDo
     *     description: Creates a new task in the specified Microsoft ToDo list
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/MicrosoftTodoTaskCreate'
     *     responses:
     *       201:
     *         description: Task created successfully
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/MicrosoftTodoTaskResponse'
     *       400:
     *         description: Invalid input data
     *       401:
     *         description: Authentication failed
     *       500:
     *         description: Internal server error
     */
    async createTask(req, res) {
        const startTime = Date.now();
        const requestId = req.id || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        try {
            this.logger.info('Microsoft ToDo task creation request received', {
                requestId,
                body: this._sanitizeRequestBody(req.body),
                userAgent: req.headers['user-agent'],
                ip: req.ip
            });

            // Validate and extract request data
            const { taskData, shouldUseDefaultList } = this._validateAndExtractTaskData(req.body);
            
            this.logger.debug('Task data validated successfully', {
                requestId,
                listId: taskData.listId,
                title: taskData.title,
                hasBody: !!taskData.body,
                hasDueDate: !!taskData.dueDateTime,
                importance: taskData.importance
            });

            // Prepare task data for service
            const serviceTaskData = {
                title: taskData.title,
                body: taskData.body ? {
                    content: taskData.body,
                    contentType: 'text'
                } : undefined,
                dueDateTime: taskData.dueDateTime ? {
                    dateTime: taskData.dueDateTime,
                    timeZone: taskData.timeZone || 'UTC'
                } : undefined,
                importance: taskData.importance,
                categories: taskData.categories
            };

            // Create task via service - use default list or specific list
            let createdTask;
            if (shouldUseDefaultList) {
                this.logger.info('Creating task in default Microsoft ToDo list', {
                    requestId,
                    title: taskData.title
                });
                createdTask = await this.todoService.createTaskInDefaultList(serviceTaskData);
            } else {
                this.logger.info('Creating task in specified Microsoft ToDo list', {
                    requestId,
                    title: taskData.title,
                    listId: taskData.listId
                });
                createdTask = await this.todoService.createTask(taskData.listId, serviceTaskData);
            }

            const duration = Date.now() - startTime;
            
            this.logger.info('Microsoft ToDo task created successfully', {
                requestId,
                taskId: createdTask.id,
                title: createdTask.title,
                duration,
                listId: createdTask.listInfo ? createdTask.listInfo.id : taskData.listId,
                listName: createdTask.listInfo ? createdTask.listInfo.displayName : 'Unknown',
                usedDefaultList: shouldUseDefaultList
            });

            // Format response
            const response = {
                success: true,
                message: 'Task created successfully',
                data: {
                    id: createdTask.id,
                    title: createdTask.title,
                    body: createdTask.body?.content || null,
                    status: createdTask.status,
                    importance: createdTask.importance,
                    createdDateTime: createdTask.createdDateTime,
                    lastModifiedDateTime: createdTask.lastModifiedDateTime,
                    dueDateTime: createdTask.dueDateTime?.dateTime || null,
                    categories: createdTask.categories || [],
                    webUrl: createdTask.webUrl || null,
                    listId: createdTask.listInfo ? createdTask.listInfo.id : taskData.listId,
                    listInfo: createdTask.listInfo ? {
                        id: createdTask.listInfo.id,
                        displayName: createdTask.listInfo.displayName,
                        wellKnownListName: createdTask.listInfo.wellKnownListName,
                        isDefaultList: shouldUseDefaultList
                    } : null
                },
                meta: {
                    requestId,
                    processingTime: duration,
                    timestamp: new Date().toISOString()
                }
            };

            return res.status(201).json(response);

        } catch (error) {
            const duration = Date.now() - startTime;

            // Use centralized error handler
            const errorResponse = errorHandler(error, 'MicrosoftTodoController.createTask');

            // Add request metadata to the error response
            const response = {
                ...errorResponse,
                meta: {
                    requestId,
                    processingTime: duration,
                    timestamp: new Date().toISOString()
                }
            };

            // Add retry-after header for rate limit errors
            if (error instanceof MicrosoftRateLimitError && error.retryAfter) {
                res.set('Retry-After', error.retryAfter.toString());
            }

            return res.status(errorResponse.status).json(response);
        }
    }

    /**
     * Validate and extract task data from request body
     * @private
     */
    _validateAndExtractTaskData(body) {
        if (!body || typeof body !== 'object') {
            throw new ValidationError('Request body must be a valid JSON object');
        }

        // Validate required fields - listId is optional if useDefaultList is true
        const shouldUseDefaultList = body.useDefaultList === true || (!body.listId && body.useDefaultList !== false);

        if (!shouldUseDefaultList && (!body.listId || typeof body.listId !== 'string' || body.listId.trim().length === 0)) {
            throw new ValidationError('listId is required and must be a non-empty string when useDefaultList is false');
        }

        if (!body.title || typeof body.title !== 'string' || body.title.trim().length === 0) {
            throw new ValidationError('title is required and must be a non-empty string');
        }

        // Validate optional fields
        if (body.body && typeof body.body !== 'string') {
            throw new ValidationError('body must be a string if provided');
        }

        if (body.dueDateTime && !this._isValidISODate(body.dueDateTime)) {
            throw new ValidationError('dueDateTime must be a valid ISO 8601 date string if provided');
        }

        if (body.importance && !['low', 'normal', 'high'].includes(body.importance)) {
            throw new ValidationError('importance must be one of: low, normal, high');
        }

        if (body.categories && (!Array.isArray(body.categories) || !body.categories.every(cat => typeof cat === 'string'))) {
            throw new ValidationError('categories must be an array of strings if provided');
        }

        if (body.timeZone && typeof body.timeZone !== 'string') {
            throw new ValidationError('timeZone must be a string if provided');
        }

        // Validate string lengths
        if (body.title.length > 255) {
            throw new ValidationError('title must not exceed 255 characters');
        }

        if (body.body && body.body.length > 4000) {
            throw new ValidationError('body must not exceed 4000 characters');
        }

        if (body.categories && body.categories.length > 25) {
            throw new ValidationError('categories must not exceed 25 items');
        }

        return {
            taskData: {
                listId: body.listId ? body.listId.trim() : undefined,
                title: body.title.trim(),
                body: body.body ? body.body.trim() : undefined,
                dueDateTime: body.dueDateTime,
                importance: body.importance || 'normal',
                categories: body.categories || [],
                timeZone: body.timeZone
            },
            shouldUseDefaultList
        };
    }

    /**
     * Validate ISO 8601 date format
     * @private
     */
    _isValidISODate(dateString) {
        try {
            const date = new Date(dateString);
            return date instanceof Date && !isNaN(date) && dateString === date.toISOString();
        } catch {
            return false;
        }
    }

    /**
     * Sanitize request body for logging (remove sensitive data)
     * @private
     */
    _sanitizeRequestBody(body) {
        if (!body || typeof body !== 'object') {
            return body;
        }

        const sanitized = { ...body };
        
        // Truncate long text fields for logging
        if (sanitized.title && sanitized.title.length > 100) {
            sanitized.title = sanitized.title.substring(0, 100) + '...';
        }
        
        if (sanitized.body && sanitized.body.length > 200) {
            sanitized.body = sanitized.body.substring(0, 200) + '...';
        }

        return sanitized;
    }

    /**
     * Get Microsoft ToDo service performance metrics
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async getPerformanceMetrics(req, res) {
        const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const startTime = Date.now();

        try {
            this.logger.info('Getting Microsoft ToDo performance metrics', {
                requestId,
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });

            const metrics = this.todoService.getPerformanceMetrics();
            const processingTime = Date.now() - startTime;

            res.status(200).json({
                success: true,
                message: 'Performance metrics retrieved successfully',
                data: metrics,
                meta: {
                    requestId,
                    processingTime,
                    timestamp: new Date().toISOString()
                }
            });

        } catch (error) {
            const processingTime = Date.now() - startTime;
            this.logger.error('Failed to get performance metrics', {
                requestId,
                error: error.message,
                processingTime
            });

            // Use centralized error handler
            const errorResponse = errorHandler(error, 'MicrosoftTodoController.getPerformanceMetrics');

            // Add request metadata to the error response
            const response = {
                ...errorResponse,
                meta: {
                    requestId,
                    processingTime,
                    timestamp: new Date().toISOString()
                }
            };

            return res.status(errorResponse.status).json(response);
        }
    }
}

module.exports = new MicrosoftTodoController();
