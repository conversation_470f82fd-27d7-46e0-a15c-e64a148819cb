{
    "deploy.reloaded": {

        "showPopupOnSuccess": false,
        "showStatusWhenFinished": false,
        "packages": [       
            {
                "name": "API Changes",
                "description": "Deploy API-related changes only",
                "files": [
                    "middleware/*.js",
                    "services/*.js",
                    "configs/*.js",
                    "controllers/**/*.js",
                    "docs/**/*.js",
                    "docs/**/*.json",
                    "server.js",
                    "package.json"
                ],
                "deployOnChange": true,
                "deployOnSave": false,
                "targets": [ "ml-algotrader-api" ]
            }             
        ],
        "targets": [            
            {
                "type": "local",
                "name": "ml-algotrader-api",
                "description": "MacMini-Deployment",
                "dir": "Z:/www/vhosts/ml-algotrader.com/api.ml-algotrader.com"
            }
        ]
    },
    "editor.fontLigatures": false,
    "editor.fontVariations": false,

}