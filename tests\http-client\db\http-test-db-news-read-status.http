### Update News Read Status to true

// @name update-news-status-true
POST {{API_BASE_URL}}/api/v1/db/news/status
Content-Type: application/json

{
  "id": "youtube_2LdTgTHm3lo_681908bd0b776",
  "is_read": true
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: Update News Read Status to true");
        client.assert(response.status === 200, "Response status is not 200");
    });
    
    client.test("Response has correct structure", function() {
        var jsonData = response.body;
        client.assert(jsonData.hasOwnProperty('state'), "Response does not have 'state' property");
        client.assert(jsonData.state === "successful", "State is not 'successful'");
    });
%}

### Update News Read Status to false

// @name update-news-status-false
POST {{API_BASE_URL}}/api/v1/db/news/status
Content-Type: application/json

{
  "id": 1,
  "is_read": false
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: Update News Read Status to false");
        client.assert(response.status === 200, "Response status is not 200");
    });
    
    client.test("Response has correct structure", function() {
        var jsonData = response.body;
        client.assert(jsonData.hasOwnProperty('state'), "Response does not have 'state' property");
        client.assert(jsonData.state === "successful", "State is not 'successful'");
    });
%}

### Update News Bookmark Status to true

// @name update-news-bookmark-status-true
POST {{API_BASE_URL}}/api/v1/db/news/status
Content-Type: application/json

{
  "id": 1,
  "is_bookmarked": true
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: Update News Bookmark Status to true");
        client.assert(response.status === 200, "Response status is not 200");
    });
    
    client.test("Response has correct structure", function() {
        var jsonData = response.body;
        client.assert(jsonData.hasOwnProperty('state'), "Response does not have 'state' property");
        client.assert(jsonData.state === "successful", "State is not 'successful'");
    });
%}

### Update both Read and Bookmark Status

// @name update-news-both-statuses
POST {{API_BASE_URL}}/api/v1/db/news/status
Content-Type: application/json

{
  "id": 1,
  "is_read": true,
  "is_bookmarked": true
}

> {%
    client.test("Request executed successfully", function() {
        client.log("#Test: Update both Read and Bookmark Status");
        client.assert(response.status === 200, "Response status is not 200");
    });
    
    client.test("Response has correct structure", function() {
        var jsonData = response.body;
        client.assert(jsonData.hasOwnProperty('state'), "Response does not have 'state' property");
        client.assert(jsonData.state === "successful", "State is not 'successful'");
    });
%}

### Test with invalid news ID

// @name update-news-status-invalid-id
POST {{API_BASE_URL}}/api/v1/db/news/status
Content-Type: application/json

{
  "id": 99999,
  "is_read": true
}

> {%
    client.test("Request returns not found for invalid ID", function() {
        client.log("#Test: Update News Status with invalid ID");
        client.assert(response.status === 404, "Response status is not 404");
    });
%}

### Test with missing ID field

// @name update-news-status-missing-id
POST {{API_BASE_URL}}/api/v1/db/news/status
Content-Type: application/json

{
  "is_read": true
}

> {%
    client.test("Request returns bad request for missing ID", function() {
        client.log("#Test: Update News Status with missing ID field");
        client.assert(response.status === 400, "Response status is not 400");
    });
%}

### Test with missing status fields

// @name update-news-status-missing-fields
POST {{API_BASE_URL}}/api/v1/db/news/status
Content-Type: application/json

{
  "id": 1
}

> {%
    client.test("Request returns bad request for missing status fields", function() {
        client.log("#Test: Update News Status with missing status fields");
        client.assert(response.status === 400, "Response status is not 400");
    });
%}