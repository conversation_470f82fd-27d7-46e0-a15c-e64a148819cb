# FactorMap Erstellung

Dieses Dokument beschreibt den Prozess der Erstellung einer FactorMap mittels der `refreshFactorMap`-Methode im `factor-map-service.js`.

## Überblick

Die FactorMap ist ein zentrales Element des Algotrader-Systems, das Einflussfaktoren auf den Markt analysiert und bewertet. Sie wird durch einen mehrstufigen KI-gestützten Prozess erstellt, der Nachrichtenzusammenfassungen verarbeitet und daraus Marktfaktoren ableitet.

## Funktionsweise der refreshFactorMap-Methode

Die `refreshFactorMap`-Methode orchestriert den gesamten Prozess der Erstellung einer neuen FactorMap. Sie führt folgende Schritte aus:

### Parameter

Die Methode akzeptiert drei Parameter:

- `limitUsedGPTSummaries` (Standard: 5): Begrenzt die Anzahl der verwendeten GPT-Nachrichtenzusammenfassungen
- `articleLimit` (Standard: 10): Begrenzt die Anzahl der Artikel, die für Zusammenfassungen verwendet werden
- `refreshNews` (Standard: false): <PERSON><PERSON><PERSON>, ob Nachrichtendaten aktualisiert werden sollen

### Prozessablauf

1. **Logging**: In Nicht-Produktionsumgebungen wird der Start des Aktualisierungsprozesses protokolliert

2. **Nachrichtenaktualisierung** (optional, derzeit auskommentiert):
   - Wenn `refreshNews` auf `true` gesetzt ist, würden Nachrichtendaten aus externen Quellen aktualisiert
   - Die Funktion `doUpdateKISummaryAndStoreInDatabase` würde aufgerufen, um neue KI-Zusammenfassungen zu erstellen

3. **Abrufen von Zusammenfassungen**:
   - Die Methode `databaseHelper.getLatestDaySummariesForAI(limitUsedGPTSummaries)` ruft die neuesten Tageszusammenfassungen aus der Datenbank ab
   - Die Anzahl der abgerufenen Zusammenfassungen wird durch den Parameter `limitUsedGPTSummaries` begrenzt

4. **Erste KI-Analyse - Einflussfaktoren identifizieren**:
   - Die Methode `requestLLM` wird aufgerufen, um ein Large Language Model (Google Gemini) zu nutzen
   - Der Prompt "ki-algobot.analyze_influencing_factors" wird verwendet
   - Die Nachrichtenzusammenfassungen werden als Kontext übergeben
   - Das LLM analysiert die Nachrichten und identifiziert relevante Marktfaktoren

5. **Zweite KI-Analyse - Marktbewertung**:
   - Eine zweite `requestLLM`-Anfrage wird mit dem Prompt "ki-algobot.analyse_market_with_factors" durchgeführt
   - Die zuvor identifizierten Faktoren werden als Kontext übergeben
   - Das LLM bewertet die Marktlage basierend auf den identifizierten Faktoren

6. **Datenbankaktualisierung**:
   - Die Ergebnisse beider KI-Analysen werden mittels `databaseHelper.createFactorMapEntry` in der Datenbank gespeichert
   - Die Methode gibt das erstellte Datenbankobjekt zurück

## Hilfsfunktion: doUpdateKISummaryAndStoreInDatabase

Diese Hilfsfunktion (derzeit nicht aktiv genutzt) ist für die Aktualisierung von KI-Zusammenfassungen zuständig:

1. Ruft strukturierte Zusammenfassungen von Symbolen mit `getMergedSymbolTeaserStructured` ab
2. Erstellt oder ruft einen Datenbankeintrag mit einem Schlüssel basierend auf dem aktuellen Datum ab
3. Prüft, ob die Zusammenfassung aktualisiert werden muss
4. Aktualisiert die Zusammenfassung bei Bedarf
5. Speichert den optimierten Textinhalt in der Datenbank

## Technische Details

- **LLM-Integration**: Die Methode nutzt Google Gemini über den `llm-service`
- **Datenbank-Integration**: Verwendet `databaseHelper` für Datenbankoperationen
- **Nachrichtenverarbeitung**: Nutzt Funktionen aus dem `news-summary-service`
- **Umgebungserkennung**: Verhält sich in Produktions- und Nicht-Produktionsumgebungen unterschiedlich

## Verwendung

Die `refreshFactorMap`-Methode kann direkt oder über eine API aufgerufen werden, um eine neue FactorMap zu erstellen. Sie wird typischerweise regelmäßig ausgeführt, um aktuelle Marktfaktoren zu identifizieren und zu bewerten.

```javascript
// Beispielaufruf mit Standardparametern
const result = await refreshFactorMap();

// Beispielaufruf mit benutzerdefinierten Parametern
const result = await refreshFactorMap(10, 20, true);
```

## Datenfluss

```
Nachrichtenzusammenfassungen → LLM (Faktorenanalyse) → LLM (Marktbewertung) → Datenbank
```

Die FactorMap stellt eine KI-gestützte Analyse der aktuellen Marktlage dar, basierend auf den neuesten Nachrichtenzusammenfassungen und identifizierten Einflussfaktoren.