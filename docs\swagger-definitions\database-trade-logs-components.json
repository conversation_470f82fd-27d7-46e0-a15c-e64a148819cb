{"components": {"schemas": {"TradeLog": {"type": "object", "properties": {"log_id": {"type": "integer", "description": "Eindeutige ID des Log-Eintrags", "example": 12345}, "timestamp": {"type": "string", "format": "date-time", "description": "Zeitpunkt des Log-Eintrags", "example": "2023-04-15T14:30:00Z"}, "refId": {"type": "string", "description": "Referenz-ID des Kontos", "example": "IG-D1"}, "refId_alternate": {"type": "string", "description": "Alternative Referenz-ID (falls vorhanden)", "example": "IG-P1"}, "action": {"type": "string", "description": "Durchgeführte Aktion", "example": "OPEN_TRADE"}, "symbol": {"type": "string", "description": "Gehandeltes Symbol", "example": "EURUSD"}, "message": {"type": "string", "description": "Detaillierte Nachricht zum Log-Eintrag", "example": "Opening trade for EURUSD at 1.0865, volume 1.5, SL at 1.0840, TP at 1.0900"}, "level": {"type": "string", "description": "Log-Level (INFO, WARN, ERROR)", "enum": ["INFO", "WARN", "ERROR"], "example": "INFO"}, "source": {"type": "string", "description": "Quelle des Log-Eintrags", "example": "TradeExecutor"}, "trade_id": {"type": "string", "description": "ID des zugehörigen Trades (falls vorhanden)", "example": "DIAAABC123"}, "status": {"type": "string", "description": "Status der Aktion", "enum": ["SUCCESS", "FAILED", "PENDING"], "example": "SUCCESS"}, "error_message": {"type": "string", "description": "Fehlermeldung (falls vorhanden)", "example": null}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "HTTP-Statuscode", "example": 400}, "error": {"type": "string", "description": "Fehlertyp", "example": "ValidationError"}, "message": {"type": "string", "description": "Fehlermeldung", "example": "Parameter 'refID' is required"}, "function": {"type": "string", "description": "Funk<PERSON>, in der der Fehler aufgetreten ist", "example": "getTradeLogs"}}}}, "responses": {"ValidationError": {"description": "Validierungsfehler bei den Eingabeparametern", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 400, "error": "ValidationError", "message": "Parameter 'refID' is required", "function": "getTradeLogs"}}}}, "DatabaseError": {"description": "Fehler bei der Datenbankabfrage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"status": 500, "error": "DatabaseError", "message": "Failed to fetch trade logs", "function": "getTradeLogs"}}}}}}}