# System Patterns

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.

2025-01-02 20:47:00 - Initial Memory Bank creation during UMB command execution.

## Coding Patterns

* **Controller Pattern**: Route handlers separate from business logic - controllers handle HTTP req/res while independent functions contain core logic
* **Parameter Validation**: Consistent validation using validation service with default values, constraints, and error handling
* **Caching Strategy**: `withCacheWrapper` function for consistent caching with TTL-based expiration and selective invalidation
* **Error Handling**: Structured error responses with consistent logging using log levels (ERROR, WARN, INFO, DEBUG)
* **Performance Logging**: `process.hrtime.bigint()` for precise timing with `logPerformance` function in finally blocks

## Architectural Patterns

* **Multi-Provider Integration**: Abstracted AI service integration allowing seamless switching between OpenAI, Groq, Mistral, Google AI, Anthropic
* **Database Connection Pooling**: Generic-pool for MariaDB connections with node-cache for query result optimization
* **Middleware Chain**: Express middleware for authentication (API key), CORS, IP filtering, and request parsing
* **RESTful API Design**: Resource-based URLs with appropriate HTTP methods, versioned paths (/api/v1/), consistent JSON responses
* **Monitoring Integration**: Helicone for AI cost tracking, HyperDX for observability, Winston for structured logging

## Testing Patterns

* **HTTP Client Tests**: Organized by functionality in `.http` files with consistent naming (`http-test-[area]-[resource]-[specifics].http`)
* **Test Script Structure**: Basic success tests, response structure validation, array property checks, conditional assertions
* **Environment Variables**: `{{API_BASE_URL}}` for base URL flexibility across environments
* **Request Documentation**: `@name` annotations, descriptive comments with `###` separators, realistic examples