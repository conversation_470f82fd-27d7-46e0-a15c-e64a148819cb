{"components": {"schemas": {"AIPrediction": {"type": "object", "properties": {"symbol": {"type": "string", "description": "Trading symbol", "example": "EURUSD"}, "prediction": {"type": "string", "description": "Direction prediction", "example": "UP"}, "confidence": {"type": "number", "format": "float", "description": "Confidence level (0-1)", "example": 0.85}, "timestamp": {"type": "string", "format": "date-time", "description": "Prediction timestamp", "example": "2025-01-15T10:30:00Z"}}}, "PromptVariables": {"type": "object", "description": "Variables to be used in the prompt template", "properties": {"TEXT_TO_ANALYZE": {"type": "string", "description": "Text content to be analyzed by the LLM", "example": "Nvidia's AI system ACE will power in-game character interactions in Mecha Break, a mech battle game launching in 2025."}, "SENDER_AND_SUBJECT": {"type": "string", "description": "Email sender and subject information", "example": "From: heise KI-Update <<EMAIL>>"}, "CONTENT": {"type": "string", "description": "Main content to be processed by the LLM", "example": "@Channel *Analyse: Nvidia's Dominanz im KI-Markt und Auswirkungen auf den Technologiesektor*"}, "GEWICHTUNGSFAKTOREN": {"type": "object", "description": "Weighting factors for relevance scoring", "example": {"Wirtschaftsdaten": {"BIP": 5, "Arbeitslosenzahlen": 4}}}, "ABSENDER_UND_BETREFF": {"type": "string", "description": "Email sender and subject information in German", "example": "From: =?UTF-8?B?REVSIFNQSUVHRUwg4oCTIERpZSBMYWdlIGFtIFNvbm50YWc=?= <<EMAIL>>"}, "INHALT": {"type": "string", "description": "Main content to be processed by the LLM in German", "example": "@Channel *Aktuelle politische und gesellschaftliche Entwicklungen in Deutschland*"}}, "additionalProperties": true}, "LLMModel": {"type": "string", "description": "LLM model identifier", "enum": ["llama-3.3-70b-versatile", "claude-3-5-haiku-20241022", "gpt-4o-mini", "mistral-large-latest", "gemini-2.5-pro-exp-03-25", "gemini-2.0-flash"], "example": "gpt-4o-mini"}, "PromptResponse": {"type": "object", "properties": {"result": {"type": "string", "description": "The LLM-generated response to the prompt"}, "model": {"type": "string", "description": "The LLM model used for processing"}, "promptID": {"type": "string", "description": "The ID of the prompt that was executed"}, "promptVersion": {"type": "integer", "description": "The version of the prompt that was used"}}}}}}